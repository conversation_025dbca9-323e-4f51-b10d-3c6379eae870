#!/bin/zsh

# Perform a pull on each branch and merge.

# set variable from release branch name
from_name="abedprod"
from_web_branch="release/abed"
from_api_branch="release/abed"

# set variable to release branch name 
to_name="abedqc2"
to_web_branch="release/abed-qc2"
to_api_branch="release/abed-qc2"

project="ABED"

# Define color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[34m'
NC='\033[0m' # No Color
# define a function that accepts from branch, directory, to branch, and merge type
function create_merge_request {
    project_name=$(basename $1)
    merge_type=$4
    case $merge_type in
        "merge") merge_branch_title="Merge";;
        "back-merge") merge_branch_title="Back-Merge";;
    esac

    cd $1
    git checkout $2
    git pull
    git checkout $3
    git pull
    branch_name="$merge_type/$from_name-to-$to_name/$(date +%Y-%m-%d)"
    git checkout -b $branch_name
    git checkout $branch_name
    if git merge $2;then
        git push # -u origin $branch_name
    else
        echo "Auto push failed on $1"
    fi

    #from_branch=$(echo $2 | sed 's|/|-|g')
    from_branch=$branch_name
    # to_branch=$(echo $3 | sed 's|/|-|g')
    to_branch=$3
    title="${project} ${merge_branch_title} - $(echo $from_name | tr '[:lower:]' '[:upper:]') to $(echo $to_name | tr '[:lower:]' '[:upper:]') - $(date +%Y-%m-%d)"
    encoded_title=$(echo $title | sed 's| |%20|g')
    link="https://bubo.vretta.com/vea/platform/${project_name}/-/merge_requests/new?merge_request%5Bsource_branch%5D=${from_branch}&merge_request%5Btarget_branch%5D=${to_branch}&merge_request%5Btitle%5D=${encoded_title}"
    echo
    echo "${RED}${title} ${NC}"
    echo "${BLUE}${link} ${NC}"
    echo
}

# call the function
create_merge_request ~/vretta/vea-web-client/ $from_web_branch $to_web_branch "back-merge"
echo "\n\n"
# create_merge_request ~/vretta/vea-api/ $from_api_branch $to_api_branch "back-merge"
