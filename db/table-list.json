[{"id": "_translations_log"}, {"id": "_translations", "hooks": {"before": {"create": ["dbCreated", "dbUpdated"], "patch": ["dbUpdated"]}}}, {"id": "accomm_applicant_transfer_req"}, {"id": "accomm_info"}, {"id": "accomm_requests_responses"}, {"id": "accomm_requests"}, {"id": "accommodations"}, {"id": "alt_version_access_file_notes"}, {"id": "alt_version_access_files"}, {"id": "alt_version_format_values"}, {"id": "alt_version_formats"}, {"id": "alt_version_information"}, {"id": "alt_version_request_notes"}, {"id": "alt_version_request_status"}, {"id": "alt_version_requests"}, {"id": "api_endpoint_test_log"}, {"id": "appeals"}, {"id": "applicant_credit_info", "isView": true}, {"id": "assessment_components"}, {"id": "assessment_courses"}, {"id": "assessment_def"}, {"id": "assessment_prices"}, {"id": "asset_group_link_info", "isView": true}, {"id": "asset_groups"}, {"id": "asset_library_fields"}, {"id": "attempts"}, {"id": "auth_as"}, {"id": "auth_login_keys"}, {"id": "auth_password_reset"}, {"id": "auth_passwords"}, {"id": "auth_workflow_stages"}, {"id": "authoring_groups"}, {"id": "booked_attempts_info"}, {"id": "bulk_student_results_reports"}, {"id": "credit_transaction_label_mapping"}, {"id": "credit_transactions"}, {"id": "credits"}, {"id": "data_export_job_tags"}, {"id": "data_export_jobs"}, {"id": "data_methods"}, {"id": "data_psych_runs"}, {"id": "data_targets"}, {"id": "dictionary_abed_fr"}, {"id": "dictionary_abed"}, {"id": "downloadable_data_files_cb", "isView": true}, {"id": "downloadable_data_files_tc", "isView": true}, {"id": "downloadable_data_files"}, {"id": "examinee_keys"}, {"id": "extended_access_users"}, {"id": "group_asset_link"}, {"id": "institution_user_accomm_applicant", "isView": true}, {"id": "institution_user_roles_active", "isView": true}, {"id": "institutions"}, {"id": "instructor_keys"}, {"id": "inter_rater_reliability_by_response"}, {"id": "item_asset_libraries"}, {"id": "item_asset_link_info", "isView": true}, {"id": "item_asset_link"}, {"id": "item_asset_summary", "isView": true}, {"id": "item_asset_versions"}, {"id": "item_assets"}, {"id": "item_impressions"}, {"id": "item_set_audits"}, {"id": "item_tag_link"}, {"id": "item_tags"}, {"id": "item_version_asset_usage"}, {"id": "log"}, {"id": "mail_campaign_tracker"}, {"id": "mail_campaign"}, {"id": "mapping_mpt_status_codes"}, {"id": "marked_claimed_batches_groups"}, {"id": "marker_access_keys"}, {"id": "marking_acct_creations"}, {"id": "marking_batch_alloc_policies"}, {"id": "marking_batch_alloc_policy_edit_logs"}, {"id": "marking_certification_tests"}, {"id": "marking_certifications"}, {"id": "marking_chat_messages"}, {"id": "marking_chats"}, {"id": "marking_claimed_batch_responses"}, {"id": "marking_claimed_batch_responses_paired_draft"}, {"id": "marking_claimed_batches"}, {"id": "marking_claimed_batches_paired_draft"}, {"id": "marking_document_data"}, {"id": "marking_document_items"}, {"id": "marking_documents"}, {"id": "marking_exemplar_responses"}, {"id": "marking_exemplar_set_attempts"}, {"id": "marking_exemplar_set_metas"}, {"id": "marking_exemplar_set_responses"}, {"id": "marking_exemplar_set_types"}, {"id": "marking_exemplar_sets"}, {"id": "marking_exemplar_stages_profile_types"}, {"id": "marking_exemplar_stages_profiles"}, {"id": "marking_exemplar_stages"}, {"id": "marking_exemplars_selected_responses"}, {"id": "marking_flag_options"}, {"id": "marking_item_data"}, {"id": "marking_item_marker_batch_status"}, {"id": "marking_item_marker_perf_blocks"}, {"id": "marking_item_marker_tasks"}, {"id": "marking_item_pt_summary"}, {"id": "marking_item_qt_summary"}, {"id": "marking_item_scoring_summary_calculations"}, {"id": "marking_item_scoring_summary"}, {"id": "marking_item_templates"}, {"id": "marking_item_thresholds"}, {"id": "marking_item_validity_summary"}, {"id": "marking_items"}, {"id": "marking_local_mark_pool_log"}, {"id": "marking_local_scoring_exports"}, {"id": "marking_local_scoring_marker_exports"}, {"id": "marking_marker_pool_mrkr"}, {"id": "marking_marker_pool_supr"}, {"id": "marking_marker_stats", "multi": "['patch']"}, {"id": "marking_marks_item_comments_by_taqr", "isView": true}, {"id": "marking_marks_item_comments"}, {"id": "marking_marks_item_response_comments_by_item", "isView": true}, {"id": "marking_marks_item_response_comments_by_taqr", "isView": true}, {"id": "marking_marks_item"}, {"id": "marking_marks_response_comments"}, {"id": "marking_marks_responses"}, {"id": "marking_pace_events", "multi": "['patch']"}, {"id": "marking_pairs_users"}, {"id": "marking_pairs"}, {"id": "marking_pool_by_supr", "isView": true}, {"id": "marking_pool_group_by_taqr_id", "isView": true}, {"id": "marking_pool_item_priority"}, {"id": "marking_pool_mrkrs", "isView": true}, {"id": "marking_pool_role_type_by_id", "isView": true}, {"id": "marking_pool_supervisor_name", "isView": true}, {"id": "marking_pool"}, {"id": "marking_pools_for_mrkr", "isView": true}, {"id": "marking_question_options"}, {"id": "marking_reliability_set_responses"}, {"id": "marking_response_annotations"}, {"id": "marking_response_marks"}, {"id": "marking_response_metas"}, {"id": "marking_response_scores"}, {"id": "marking_response_scores_paired_draft"}, {"id": "marking_response_selection_comment"}, {"id": "marking_response_selection_log"}, {"id": "marking_response_selections"}, {"id": "marking_response_set_selections"}, {"id": "marking_response_set_types"}, {"id": "marking_response_set"}, {"id": "marking_response_upload"}, {"id": "marking_responses"}, {"id": "marking_rubric_entries"}, {"id": "marking_rubric_items"}, {"id": "marking_rubrics"}, {"id": "marking_score_options"}, {"id": "marking_score_profile_flags"}, {"id": "marking_score_profile_groups"}, {"id": "marking_score_profile_options"}, {"id": "marking_score_profiles"}, {"id": "marking_scorer_reads"}, {"id": "marking_session_assignment_json"}, {"id": "marking_session_data"}, {"id": "marking_session_participants"}, {"id": "marking_supplement_taqr_cache_log"}, {"id": "marking_supplement_taqr_cache"}, {"id": "marking_taqr_cache_log"}, {"id": "marking_taqr_cache"}, {"id": "marking_time_spent"}, {"id": "marking_user_data"}, {"id": "marking_user_info"}, {"id": "marking_window_attempts", "isView": true}, {"id": "marking_window_by_uid", "isView": true}, {"id": "marking_window_certification_tests"}, {"id": "marking_window_items"}, {"id": "marking_window_markers", "isView": true}, {"id": "marking_window_marks", "isView": true}, {"id": "marking_window_responses"}, {"id": "marking_window_role_type_by_id", "isView": true}, {"id": "marking_window_subm_compl", "isView": true}, {"id": "marking_window_subm_new", "isView": true}, {"id": "marking_window_supervisors", "isView": true}, {"id": "marking_window_task_definitions"}, {"id": "marking_window_test_takers", "isView": true}, {"id": "marking_window_test_window"}, {"id": "marking_window_unique_items", "isView": true}, {"id": "marking_window_user_meta"}, {"id": "marking_window_user_notifications"}, {"id": "marking_windows"}, {"id": "notification_groups"}, {"id": "notifications", "multi": "['patch']"}, {"id": "oct_applicant_ids_archive"}, {"id": "oct_applicant_ids"}, {"id": "oct_teachables"}, {"id": "odi_sample_temp"}, {"id": "odi_stu_asmt_level"}, {"id": "odi_stu_item_level"}, {"id": "payment_refund_setup"}, {"id": "process_data_logs_exports_log"}, {"id": "provincial_print_materials_quantities"}, {"id": "psych_config_history"}, {"id": "psych_item_summary", "isView": true}, {"id": "psych_pipeline_configs"}, {"id": "psych_response_summary", "isView": true}, {"id": "psych_student_responses", "isView": true}, {"id": "psych_test_attempts", "isView": true}, {"id": "purchase_invoices_log", "isView": false}, {"id": "purchase_invoices", "isView": false}, {"id": "purchased_attempts_log", "isView": false}, {"id": "question_set_by_uid", "isView": true}, {"id": "question_set_parameters_versions"}, {"id": "question_set_parameters"}, {"id": "question_set_roles_by_uid", "isView": true}, {"id": "question_set_single_members", "isView": true}, {"id": "questionnaire_test_sessions"}, {"id": "questionset_group_members", "isView": true}, {"id": "realtime_class_users"}, {"id": "reported_issue_comments"}, {"id": "reported_issue_resolutions"}, {"id": "reported_issues_common"}, {"id": "reported_issues_student_exceptions"}, {"id": "reported_issues_student_item_exceptions"}, {"id": "reported_issues_with_resolution"}, {"id": "reported_issues"}, {"id": "response_sets_transfer_log"}, {"id": "rp_category_schema"}, {"id": "rp_cut_score_profile"}, {"id": "rp_cut_score_schema"}, {"id": "rp_domain_schema"}, {"id": "rp_domain_score_scaling_factor_profile"}, {"id": "rp_reporting_profiles"}, {"id": "rp_layout_profiles"}, {"id": "rp_layout_config"}, {"id": "rp_text_node_refs"}, {"id": "scan_review_batch_responses"}, {"id": "scan_review_batches"}, {"id": "scan_review_edits"}, {"id": "scan_review_taqr_pool"}, {"id": "school_admin_bulk_load_record"}, {"id": "school_assessments_map"}, {"id": "school_class_common_forms"}, {"id": "school_class_test_sessions"}, {"id": "school_classes_guest"}, {"id": "school_classes"}, {"id": "school_districts"}, {"id": "school_ip_addresses"}, {"id": "school_participation"}, {"id": "school_payment_agreements"}, {"id": "school_physical_address"}, {"id": "school_semesters"}, {"id": "school_special_material_requests"}, {"id": "school_student_asmt_info_signoffs"}, {"id": "school_students", "isView": true}, {"id": "school_test_access"}, {"id": "school_tw_student_signoffs"}, {"id": "school_type_assessment_settings"}, {"id": "school_type_profiles"}, {"id": "schools_summary", "isView": true}, {"id": "schools"}, {"id": "scorer_status_by_item_log"}, {"id": "scoring_message_centre_messages"}, {"id": "scoring_message_centre_recipients"}, {"id": "seb_config_assignments"}, {"id": "seb_configs"}, {"id": "session_locations"}, {"id": "session_question_audit"}, {"id": "session_schedules"}, {"id": "se_assessment_structures"}, {"id": "se_item_set_structures"}, {"id": "special_material_requests"}, {"id": "stage_submissions"}, {"id": "student_attempt_purchases", "isView": false}, {"id": "student_constructed_response"}, {"id": "student_final_mark"}, {"id": "student_form_configs"}, {"id": "student_imp_exp_configs"}, {"id": "student_metas_profiles"}, {"id": "student_reports"}, {"id": "student_requests"}, {"id": "student_user_metas_snapshot"}, {"id": "student"}, {"id": "students_bulk_scan_uploads"}, {"id": "style_profile_versions"}, {"id": "style_profiles"}, {"id": "successful_attempts"}, {"id": "support_knowledge_base"}, {"id": "support_reporting"}, {"id": "support_system_messages"}, {"id": "sys_constants_md_translations"}, {"id": "sys_constants_numeric"}, {"id": "sys_constants_string"}, {"id": "sys_sent_email_logs"}, {"id": "taqr_processing_log"}, {"id": "teacher_report_issue_categories"}, {"id": "technical_readiness_tracking"}, {"id": "technical_readiness_verification"}, {"id": "temp_local_scoring"}, {"id": "temp_question_set_conflicts_log"}, {"id": "temp_question_set_log"}, {"id": "temp_question_set"}, {"id": "test_attempt_confirmation_codes"}, {"id": "test_attempt_metas"}, {"id": "test_attempt_qr_correct", "isView": true, "isUnoptimized": true}, {"id": "test_attempt_question_response_entries"}, {"id": "test_attempt_question_response_score_logs"}, {"id": "test_attempt_question_response_scores"}, {"id": "test_attempt_question_responses_log"}, {"id": "test_attempt_question_responses"}, {"id": "test_attempt_scan_reassign_log"}, {"id": "test_attempt_scan_responses", "multi": "['create']"}, {"id": "test_attempt_screen_sessions"}, {"id": "test_attempt_sub_sessions"}, {"id": "test_attempt_unsubmission_reasons"}, {"id": "test_attempt_unsubmissions"}, {"id": "test_attempts_info"}, {"id": "test_attempts_num_q_ans", "isView": true, "isUnoptimized": true}, {"id": "test_attempts"}, {"id": "test_content"}, {"id": "test_controller_dashboard_cache"}, {"id": "test_design_blocks"}, {"id": "test_design_item_diff_sign_off"}, {"id": "test_design_questions"}, {"id": "test_design_sign_off"}, {"id": "test_designs"}, {"id": "test_forms"}, {"id": "test_invigilation"}, {"id": "test_question_auth_change_log"}, {"id": "test_question_auth_note_files"}, {"id": "test_question_auth_note_highlights"}, {"id": "test_question_auth_note_versions"}, {"id": "test_question_auth_notes_by_uid_all", "isView": true}, {"id": "test_question_auth_notes", "multi": "['patch', 'remove']"}, {"id": "test_question_by_taqr_id", "isView": true}, {"id": "test_question_expected_responses"}, {"id": "test_question_graphic_requests"}, {"id": "test_question_register_generic_param_map"}, {"id": "test_question_register_param_map"}, {"id": "test_question_register"}, {"id": "test_question_response_register"}, {"id": "test_question_revisions", "isView": true}, {"id": "test_question_scales_register"}, {"id": "test_question_scoring_codes"}, {"id": "test_question_scoring_info"}, {"id": "test_question_suggestion_versions"}, {"id": "test_question_suggestions"}, {"id": "test_question_template_auth_groups"}, {"id": "test_question_template_versions"}, {"id": "test_question_templates"}, {"id": "test_question_version_by_test_design"}, {"id": "test_question_versions"}, {"id": "test_question_workflow_assignments"}, {"id": "test_question_workflow_status"}, {"id": "test_questions"}, {"id": "test_reports_bc_by_school", "isView": true}, {"id": "test_reports_bc2"}, {"id": "test_reports"}, {"id": "test_session_avail_seb_headers", "isView": true}, {"id": "test_session_avail_support", "isView": false}, {"id": "test_session_avail_tt", "isView": false}, {"id": "test_session_booking_users_info_ext", "isView": true}, {"id": "test_session_booking_users_info", "isView": true}, {"id": "test_session_booking_users", "isView": true}, {"id": "test_session_dates", "isView": true}, {"id": "test_session_lang_req_tally", "isView": true}, {"id": "test_session_mng_booking", "isView": true}, {"id": "test_session_passwords"}, {"id": "test_session_purchases", "isView": false}, {"id": "test_session_role_type_by_id", "isView": true}, {"id": "test_session_roles", "isView": true}, {"id": "test_session_setup_locations", "hooks": {"before": {"create": ["dbCreated", "dbUpdated"], "patch": ["dbUpdated"]}}}, {"id": "test_session_setup_time_slots", "hooks": {"before": {"create": ["dbCreated", "dbUpdated"], "patch": ["dbUpdated"]}}}, {"id": "test_session_setups"}, {"id": "test_session_sub_sessions_presets"}, {"id": "test_session_sub_sessions"}, {"id": "test_session_test_forms", "isView": true}, {"id": "test_session_test_takers", "isView": true}, {"id": "test_session_transfer_req"}, {"id": "test_session_waitlist_users_info", "isView": true}, {"id": "test_session_waitlist_users", "isView": true}, {"id": "test_sessions__res_val_info", "isView": true}, {"id": "test_sessions_by_inst", "isView": true}, {"id": "test_sessions_dashboard_info", "isView": true}, {"id": "test_sessions_tally_by_invig", "isView": true}, {"id": "test_sessions_with_dates", "isView": true}, {"id": "test_sessions"}, {"id": "test_window_activity_logs"}, {"id": "test_window_files"}, {"id": "test_window_sessions_created", "isView": true}, {"id": "test_window_sessions", "isView": true}, {"id": "test_window_student_codes"}, {"id": "test_window_td_alloc_rules_log"}, {"id": "test_window_td_alloc_rules"}, {"id": "test_window_td_types"}, {"id": "test_windows"}, {"id": "test"}, {"id": "transactions"}, {"id": "tt_notification_log"}, {"id": "tt_notification_session", "isView": true}, {"id": "tt_notification_tracker", "isView": true}, {"id": "tw_exceptions_student_items"}, {"id": "tw_exceptions_students"}, {"id": "tw_ta_blob_cache"}, {"id": "tw_twtar_profiles"}, {"id": "tw_user_metas"}, {"id": "twtar_form_prioritization"}, {"id": "twtdar_signoffs"}, {"id": "u_group_active_role_tally", "isView": true}, {"id": "u_group_type_allowed_roles"}, {"id": "u_group_types"}, {"id": "u_groups_metas"}, {"id": "u_groups_singular", "isView": true}, {"id": "u_groups"}, {"id": "u_invite_waves"}, {"id": "u_invites", "hooks": {"before": {"create": ["dbCreated"]}}}, {"id": "u_role_actions_test_log_resolution_types"}, {"id": "u_role_actions"}, {"id": "u_role_types"}, {"id": "u_ta_users", "isView": true}, {"id": "u_tc_cb_users", "isView": true}, {"id": "u_tc_inst_seb", "isView": true}, {"id": "u_tc_ta_users", "isView": true}, {"id": "u_tc_tc_users", "isView": true}, {"id": "user_group_checklist"}, {"id": "user_metas_import_disc"}, {"id": "user_metas_imports"}, {"id": "user_metas_test", "multi": "['create']", "hooks": {"before": {"create": ["dbCreated", "dbUpdated"]}}}, {"id": "user_metas", "multi": "['create']", "hooks": {"before": {"create": ["dbCreated", "dbUpdated"]}}}, {"id": "user_role_active_w_group_type", "isView": true}, {"id": "user_role_w_group_type", "isView": true}, {"id": "user_roles_active", "isView": true}, {"id": "user_roles", "before": {"create": ["dbCreated"]}}, {"id": "users", "hooks": {"before": {"create": ["dbCreated"]}}}, {"id": "validated_test_dates"}, {"id": "marking_response_confirmations"}, {"id": "marking_groups"}, {"id": "marking_groups_users"}]