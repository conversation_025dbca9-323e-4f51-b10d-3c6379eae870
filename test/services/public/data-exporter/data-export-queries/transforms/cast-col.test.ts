import assert from 'assert';
import fs from 'fs';

import { cast_col } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/cast-col'

describe('data-exporter | data-export-queries | transforms :: cast_col', () => {
  it('works for empty input DataFrame', () => {
    const df_input = [];
    const expected = [];
  
    const casts = [{ col_target: "score", target_type: "number"}]

    const result = cast_col(df_input, casts);
  
    assert.deepStrictEqual(result, expected, "Works when input DataFrame is empty");
  });

  it('works for valid number conversion', () => {
    const df_input = [
      { item_id: "1", score: "1" },
      { item_id: "2", score: "2" },
    ];
    const expected = [
      { item_id: "1", score: 1 },
      { item_id: "2", score: 2 },
    ];
  
    const casts = [{ col_target: "score", target_type: "number"}]
  
    const result = cast_col(df_input, casts);
  
    assert.deepStrictEqual(result, expected, "Correctly converts string to number");
  });

  it('throws error for invalid number conversion', () => {
    const df_input = [
      { item_id: "1", score: "100" },
      { item_id: "2", score: "abc" },
    ];
  
    const casts = [{ col_target: "score", target_type: "number"}]
  
    assert.throws(
      () => cast_col(df_input, casts),
      /Invalid value for number conversion: abc/,
      "Throws error for invalid number conversion"
    );
  });

  it('works for null and undefined values', () => {
    const df_input = [
      { id: "1", value: null },
      { id: "2", value: undefined },
      { id: "3", value: "100" },
    ];
    const expected = [
      { id: "1", value: null },
      { id: "2", value: undefined },
      { id: "3", value: 100 },
    ];
  
    const casts = [{ col_target: "value", target_type: "number"}]
  
    const result = cast_col(df_input, casts);
  
    assert.deepStrictEqual(result, expected, "Handles null and undefined values gracefully");
  });

  it('works for date conversion, multiple cast instructions', () => {
    const df_input = [
      { item_id: "1", score: "1", created_on: 1737389149000},
      { item_id: "2", score: "2", created_on: 1737389149000 },
    ];
    const expected = [
      { item_id: "1", score: 1, created_on: '2025-01-20T16:05:49.000Z'},
      { item_id: "2", score: 2, created_on: '2025-01-20T16:05:49.000Z' },
    ];
  
    const casts = [
      { col_target: "score", target_type: "number"},
      { col_target: "created_on", target_type: "date"},
    ]
  
    const result = cast_col(df_input, casts);
  
    assert.deepStrictEqual(result, expected, "Correctly converts int to date, also handles multiple cast instructions correctly.");
  });
});
