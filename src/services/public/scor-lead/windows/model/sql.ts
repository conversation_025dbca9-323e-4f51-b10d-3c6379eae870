export const SQL_MW_SIMPLE = ` /*SQL_MW_SIMPLE*/ 
      select mw.is_active
           , mw.is_locked
           , mw.name marking_window_name
           , mw.meta_config
           , mw.is_rescore
           , mw.is_scan_reassign_allowed
           , mw.is_scorings_inspect_access
           , mw.is_expert_score_edit
           , mw.is_second_read_recheck_disabled
           , mw.is_group_marking
           , mw.group_id
      from marking_windows mw
      where id = ?
`

export const SQL_MW_ITEM_STRUCT = ` /*SQL_MW_ITEM_STRUCT*/ 
    select mwi.marking_window_id
        , mwi.id mwi_id
        , mwi.sync_batches_to_wmi_id
        , mwi.group_to_mwi_id 
        , mwi.slug
        , mwi.caption
        , mwi.item_id
        , mwi.batch_alloc_policy_id
        , mwi.score_profile_id 
    from marking_window_items mwi 
    where mwi.marking_window_id IN (:mw_ids)
        and mwi.id = mwi.sync_batches_to_wmi_id
    order by mwi.sync_batches_to_wmi_id
        , mwi.group_to_mwi_id 
        , mwi.id 
`

export const SQL_MW_SCORING_POLICIES = ` /*SQL_MW_SCORING_POLICIES*/ 
    select mbap.id
        , mbap.read_rules
    from marking_batch_alloc_policies mbap
    where mbap.id in (:batch_alloc_policy_ids)
`

