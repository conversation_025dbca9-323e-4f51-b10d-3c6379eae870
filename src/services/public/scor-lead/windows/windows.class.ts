import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../util/db-raw';
import { SQL_LEADER_MARKING_WINDOWS } from '../../../../sql/scoring';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { SQL_MW_ITEM_STRUCT, SQL_MW_SCORING_POLICIES, SQL_MW_SIMPLE } from './model/sql';
interface Data {}

interface ServiceOptions {}

export class Windows implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {isRafi} = params.query;
      const uid = await currentUid(this.app, params);
      const isGlobalScoringDisabled = await getSysConstNumeric(this.app, 'DISABLE_SCORING')
      const markingWindows = await dbRawRead(this.app, [uid], SQL_LEADER_MARKING_WINDOWS(!!isRafi));
      markingWindows.forEach(w => {
        w.isGlobalScoringDisabled = isGlobalScoringDisabled
      })
      return markingWindows;
    }
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
      // < 0.1 sec
     const record:any = await dbRawReadSingle(this.app, [id], SQL_MW_SIMPLE)
    
    // todo: standard fucntion for JSON defaults
    try {
      record.meta_config = JSON.parse(record.meta_config || '{}')
    }
    catch(e){
      record.meta_config = {}
    }
    
    try {
      record.items = await dbRawReadReporting(this.app, {mw_ids: [id]}, SQL_MW_ITEM_STRUCT)
      const batchAllocPolicyIds = [... new Set(record.items.map((r:any) => r.batch_alloc_policy_id))];
      if (batchAllocPolicyIds.length){
        record.scoringPolicies = await dbRawReadReporting(this.app, {batch_alloc_policy_ids: batchAllocPolicyIds}, SQL_MW_SCORING_POLICIES)
      }
      else {
        record.scoringPolicies = [];
      }
    }
    catch(e){
      record.items = []
      record.scoringPolicies = []
    }

    return record
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async validateWindowLock(id: Id, query:string){
    const isGlobalScoringDisabled = await getSysConstNumeric(this.app, 'DISABLE_SCORING');
    if (isGlobalScoringDisabled){
      throw new Errors.Forbidden('SCORING_DISABLED');
    }
    const mwStatus = await dbRawReadSingle(this.app, {id}, query)
    if (mwStatus.is_active==0 || mwStatus.is_scoring_disabled==1){
      throw new Errors.Forbidden('SCORING_DISABLED')
    }
  }

  async validateWindowLockByMarkingWindow(id: Id){
    return this.validateWindowLock(id, `
      select is_active, is_scoring_disabled
      from marking_windows mw
      where id = :id
    `)
  }

  async validateWindowLockByItem(id: Id){
    return this.validateWindowLock(id, `
      select mw.is_active, mw.is_scoring_disabled
      from marking_windows mw
      join marking_window_items mwi
        on mwi.marking_window_id = mw.id
      where mwi.id = :id
      limit 1
    `)
  }

  async validateWindowLockByTask(id: Id){
    return this.validateWindowLock(id, `
      select mw.is_active, mw.is_scoring_disabled
      from marking_windows mw
      join marking_window_items mwi
        on mwi.marking_window_id = mw.id
      join marking_item_marker_tasks mimt
        on mimt.marking_window_item_id = mwi.id
      where mimt.id = :id
        and mimt.is_removed = 0
      limit 1
    `)
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {prop, value, dateFrom, dateTo, featureToggles} = <any> data;
    if (prop === 'is_scoring_disabled'){
      await this.app.service('db/write/marking-windows').patch(id, {is_scoring_disabled: value})
    }
    if (prop === 'is_changing_date'){
      if(new Date(dateFrom) >= new Date(dateTo)){
        throw new Errors.BadRequest("END_DATE_TOO_EARLY")
      }
      await this.app.service('db/write/marking-windows').patch(id, {
        start_on: dateFrom,
        end_on: dateTo
      })
    }
    if (prop == 'is_toggle_features'){
      const featureToggleProps = [
        'is_scan_reassign_allowed'
        ,'is_scorer_annotation_allowed'
        , 'is_score_profile_single_focus'
        , 'is_paired_marking_allowed'
        , 'is_scorer_summary_view_allowed'
        , 'is_paired_marking_allowed'
        , 'is_group_marking'
        , 'is_scorings_inspect_access'
        , 'is_expert_score_edit'
        , 'is_second_read_recheck_disabled'
      ]
      if (!Object.keys(featureToggles).every((key) => featureToggleProps.includes(key) && [0, 1].includes(featureToggles[key]))){
        throw new Errors.BadRequest("INVALID_INPUT")
      }
      await this.app.service('db/write/marking-windows').patch(id, {
        ...featureToggles
      })
    }
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
