import { Id, NullableId, Paginated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbBulkInsert, dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';
import { LOCAL_SCORING_ACTIVE_MARKING_WINDOWS, LOCAL_SCORING_BATCH_ALLOCATION_POLICIES, LOCAL_SCORING_EXISTING_MARKER_TASK, LOCAL_SCORING_MARKER_CREATION_META, LOCAL_SCORING_MARKING_WINDOW_FLAG_OPTIONS, LOCAL_SCORING_MARKING_WINDOW_ITEM_MAP, LOCAL_SCORING_MARKING_WINDOW_SCORE_OPTIONS, LOCAL_SCORING_MTC_SCORES } from '../../../../sql/local-scoring';
import { DATETIME } from '../../../../types/db-types';
import { ISuppressionCondition } from '../../scor-scor/batches/claim/claim.class';

interface Data {}

interface ServiceOptions {}

interface IMarkingWindowScoreOptions {
   mwi_id: number,
   group_to_mwi_id : number,
   sync_batches_to_wmi_id : number,
   item_id : number,
   skill_code: string,
   slug: string,
   value  : number,
   mspo_id: number,
   batch_alloc_policy_id: number,
   is_non_scored_profile: number,
}

interface IMarkingWindowScoreFlags {
   mwi_id:  number,
   group_to_mwi_id :  number,
   sync_batches_to_wmi_id :  number,
   item_id :  number,
   skill_code:  string,
   slug:  string,
   value :  number,
   mspf_id:  number,
   batch_alloc_policy_id:  number,
   is_non_scored_profile :  number,
}

const META_CREATION = ["Marker_Number", "Max_Read_Level"]

interface ILocalScore {
   id: number;
   data: string;
   updated_on: DATETIME;
   updated_by_uid?: number;
   attempt_id: number;
   item_id: number;
   teacher_uid: number;
   taqr_id: number;
   schl_group_id: number;
   student_accommodation_cache : string;
}
interface IResponseScoreItem{ 
   tls_id: number | null;
   uid: number;
   item_id: number;
   taqr_id: number;
   mbap_id: number;
   mwi_id: number;
   mspo_id: number | null;
   mspf_id: number | null;
   value: number;
   school_group_id: number;
   attempt_id: number;
   student_accommodation_cache: string | null;
}
interface IResponseScore { 
   attempt_id?: number;
   items?: IResponseScoreItem[];
   success: boolean;
   hasEmptyScore: boolean;
}
interface IWindowItemInfoMap{
   taqr_id: number, 
   ta_id: number, 
   mwi_id: number, 
   item_id: number
}

export class LocalScoringPooling implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
     //Get Info
     const { markingWindowIds } = data;
     if (params == null || params.query == null) {
      throw new Errors.BadRequest();
      }
     const uid = await currentUid(this.app, params);
     // Get all Marking window items;
     const activeMarkingWindows = await dbRawRead(this.app, {markingWindowIds}, LOCAL_SCORING_ACTIVE_MARKING_WINDOWS);

     for (let window of activeMarkingWindows){
      await this.poolingLocalScores(window.id, uid, window.is_rescore)
     }

     return "SUCCESS";
  }

  async poolingLocalScores(markingWindowId: number, scoring_leader_uid: number, is_rescore: number){

   const progressLog:any[] = [{slug:'start', timeMs: 0}];
   const timestampStart = +(new Date())
   const newPoolingRecord = await this.app.service('db/write/marking-local-mark-pool-log').create({ marking_window_id: markingWindowId })
   const logProgress = async (slug:string, data?:any) => {
      const timeMs = (+(new Date())) - timestampStart
      progressLog.push({slug, data, timeMs})
      await this.app.service('db/write/marking-local-mark-pool-log').patch(newPoolingRecord.id, {
         full_output: JSON.stringify(progressLog), 
         updated_on: dbDateNow(this.app)
      })
   }
   const logCompletion = async (num_records:number) => {
      const offsetTime = (+(new Date())) - timestampStart
      await this.app.service('db/write/marking-local-mark-pool-log').patch(newPoolingRecord.id, {
         num_records, 
         is_completed: 1
      })
   }


   let localScores : ILocalScore[] = [];
   let localScorerUids :number[] = [];
   // Find tls match from Not Yet Claimed
   localScores = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_MTC_SCORES);
   
   const taqrLocalScoreMap = new Map();
   const taqrWithoutScores = [];
   for (let localScore of localScores){
      // Need to revisit if latest is fetched
      if(localScore.id && !taqrLocalScoreMap.get(localScore.taqr_id)){
         taqrLocalScoreMap.set(localScore.taqr_id, localScore)
      }
      if(!localScore.id){
         taqrWithoutScores.push(localScore.taqr_id)
      }
   }
   localScores = [...taqrLocalScoreMap.values()];
   // By matching the items, convert tls into mrs
   const { mbapMrsMap, mwiIds, windowItemsSupressionMap } = await this.getMbapMrsMap(localScores, markingWindowId)
   const filteredMbapMrsMap: Map<number, Map<number, { hasEmptyScore: Boolean, scoreItems: IResponseScoreItem[] }>> = new Map();
   // Clear records without scores because of no match questions
   for(const [mbap_id, attemptScore] of mbapMrsMap){
      filteredMbapMrsMap.set(mbap_id, new Map([...attemptScore].filter(([k, v]) => v.scoreItems.length > 0 )))
   }
   await logProgress('0-getMbapMrsMap')

   // 1.0 Create marked-claimed-batches-groups for score length > 0
   let newMcbgRecords = [];
   let newMcbgRecordsIds: number[] = [];
   for (const [mbap, newMrsMap] of filteredMbapMrsMap){
      for(const [attempt_id, value] of newMrsMap){
         await newMcbgRecords.push({
            //created_by_uid: scoring_leader_uid
            created_by_uid: value.scoreItems[0].uid
         })
         localScorerUids.push(value.scoreItems[0].uid)
      }
   }
   if (newMcbgRecords.length){
      await dbBulkInsert(this.app, 'marked_claimed_batches_groups', newMcbgRecords, 'id');
      //@ts-ignore
      newMcbgRecordsIds = newMcbgRecords.map(r => r.id)
   }
   await logProgress('1-insert-mcbg')

   // 2.0 Create marking-scorer-reads for every scores
   let newMarkerReadRecords = [];
   let newMarkerReadIds: number[] = [];
   for (const [mbap, newMrsMap] of filteredMbapMrsMap){
      for(const [key, value] of newMrsMap){
         newMarkerReadRecords.push({
            //created_by_uid: scoring_leader_uid
            created_by_uid: value.scoreItems[0].uid
         })
         localScorerUids.push(value.scoreItems[0].uid)
      }
   }
   if (newMarkerReadRecords.length){
      await dbBulkInsert(this.app, 'marking_scorer_reads', newMarkerReadRecords, 'id');
      //@ts-ignore
      newMarkerReadIds = newMarkerReadRecords.map(r => r.id)
   }
   await logProgress('2-insert-msr')

   const numReads = newMarkerReadIds.length;

   // 3.0 Create marking-claimed-batches for every scores
   let newMarkingClaimedBatches = [];
   let newMwiMcbMap = new Map();
   for (const [mbap, newMrsMap] of filteredMbapMrsMap){
      for(const [key, value] of newMrsMap){
      for(const mrs of value.scoreItems){
            newMarkingClaimedBatches.push({
               marking_window_item_id: mrs.mwi_id,
               batch_size: 1, //to be updated
               component_type: 'SCORING',
               is_marked: 1,
               started_on: dbDateNow(this.app),
               completed_on: dbDateNow(this.app),
               //uid:scoring_leader_uid,
               uid: mrs.uid
            })
            localScorerUids.push(mrs.uid)
         }
      }
   }
   if (newMarkingClaimedBatches.length){
      await dbBulkInsert(this.app, 'marking_claimed_batches', newMarkingClaimedBatches, 'id');
      
      await newMarkingClaimedBatches.map(
         (r) => {
            if(newMwiMcbMap.get(r.marking_window_item_id)){
               //@ts-ignore
               newMwiMcbMap.get(r.marking_window_item_id).push(r.id)
            }
            else{
               //@ts-ignore
               newMwiMcbMap.set((r.marking_window_item_id), [r.id])
            }
         }
      )
   }
   await logProgress('3-insert-mcb')

   // 4.0 Create marking-claimed-batch-responses for every scores
   let newMarkingClaimedBatchResponses = [];
   let newMrsMcbrMap = new Map<string, {id: number, is_scale_supressed: number}>();
   for (const [mbap, newMrsMap] of filteredMbapMrsMap){
      for(const [key, value] of newMrsMap){
      let hasEmptyScore = value.hasEmptyScore;
      const mcbgId =  newMcbgRecordsIds.pop();
      const markerReadId = newMarkerReadIds.pop();
         
         for(const mrs of value.scoreItems){
            const claimed_batch_id = newMwiMcbMap.get(mrs.mwi_id).pop();
            // Check accommodation
            const { is_scale_rescore_supressed, is_prorated } = await this.isSuppressed(windowItemsSupressionMap, mrs.mwi_id, mrs.student_accommodation_cache)
            newMarkingClaimedBatchResponses.push({
               claimed_batch_id: claimed_batch_id,
               claimed_batch_group_id: mcbgId,
               taqr_id: mrs.taqr_id,
               read_id: 1,
               marker_read_id: markerReadId,
               window_item_id: mrs.mwi_id,
               schl_group_id: mrs.school_group_id, //to be updated
               tls_id: mrs.tls_id,
               is_marked: ((mrs.mspo_id || mrs.mspf_id) && is_scale_rescore_supressed == 0)?1:0,
               marked_on: ((mrs.mspo_id || mrs.mspf_id) && is_scale_rescore_supressed == 0)?dbDateNow(this.app): null,
               is_scale_supressed: is_scale_rescore_supressed,
               is_scale_rescore_supressed: is_scale_rescore_supressed,
               is_prorated,
               is_rescore,
               is_revoked: hasEmptyScore? 1 : 0,
               is_invalid: hasEmptyScore? 1 : 0,
            })
         }
      }
   }
   if (newMarkingClaimedBatchResponses.length){
      await dbBulkInsert(this.app, 'marking_claimed_batch_responses', newMarkingClaimedBatchResponses, 'id');
      await newMarkingClaimedBatchResponses.map(
         (r) => {
            //@ts-ignore
            newMrsMcbrMap.set((r.taqr_id + "-" + r.window_item_id), {id: r.id, is_scale_supressed: r.is_scale_supressed})
         }
      )
   }
   await logProgress('4-insert-mcbr')

   // 5.0 Create marking-response-scores
   let newMarkingResponseScores = [];
   let newMarkingResponseScoreIds: number[] = [];
   for (const [mbap, newMrsMap] of filteredMbapMrsMap){
      for(const [key, value] of newMrsMap){
         for(const mrs of value.scoreItems){
            const mcbrId = await newMrsMcbrMap.get((mrs.taqr_id + "-" + mrs.mwi_id))?.id;
            const mcbrSuppressed = await newMrsMcbrMap.get((mrs.taqr_id + "-" + mrs.mwi_id))?.is_scale_supressed;
            if((mrs.mspo_id || mrs.mspf_id) && mcbrSuppressed == 0){
               newMarkingResponseScores.push({
                  uid: mrs.uid,
                  //uid: scoring_leader_uid,
                  batch_response_id: mcbrId,
                  score_option_id: mrs.mspo_id || null,
                  score_flag_id: mrs.mspf_id || null,
                  meta: mrs.mspf_id?JSON.stringify({"flagMessage":""}): null
               })
               localScorerUids.push(mrs.uid)
            }
         }
      }
   }
   if (newMarkingResponseScores.length){
      await dbBulkInsert(this.app, 'marking_response_scores', newMarkingResponseScores, 'id');
      //@ts-ignore
      newMarkingResponseScoreIds = newMarkingResponseScores.map(r => r.id)
   }
   await logProgress('4-insert-mrs')

   if(localScorerUids.length > 0){
      await this.createMarkerTaskForLocalScorer([...new Set(localScorerUids)], markingWindowId)
   }
   await logProgress('4-insert-mimt')

   await logCompletion(numReads)

   return;
}

   async createMarkerTaskForLocalScorer(uids : number[], markingWindowId: number){
      let newMarkerTasks = [];
      const markingWindowMetaData = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_MARKER_CREATION_META);
      const group_id = markingWindowMetaData[0].group_id;
      const markingWindowItemIds = markingWindowMetaData.map(data => data.mwi_id);
      const existingMimt = await dbRawRead(this.app, {markingWindowItemIds, uids, group_id}, LOCAL_SCORING_EXISTING_MARKER_TASK)

      for (const uid of uids){
      const existingItemIds =  existingMimt.filter(mimt=> mimt.uid == uid).map(mimt => mimt.marking_window_item_id);
      const itemCreations = markingWindowMetaData.filter(item => !existingItemIds.includes(item.mwi_id));
      for(const item of itemCreations){
         const lang = item.lang;
         let caption = 'Scoring'; //default en as language
         if(lang && lang == 'fr'){
               caption = 'Notation';
         }
         newMarkerTasks.push({
            marking_window_item_id: item.mwi_id,
            uid: uid,
            order: 1,
            is_default: 0,
            component_type: 'SCORING',
            caption: caption,
            status: "ACTIVE",
            ctrl_group_id: group_id,
            created_by_uid: uid
         })
      }
      }
      if (newMarkerTasks.length){
         await dbBulkInsert(this.app, 'marking_item_marker_tasks', newMarkerTasks, 'id');
      }
      let newUserMetas = [];
      const existingUserMeta = await dbRawRead(this.app, {markingWindowId, uids}, `
         select mwum.marker_uid
         , mwum.meta_key
         from marking_window_user_meta mwum 
            where marking_window_id in (:markingWindowId)
            and marker_uid in (:uids)
            and is_revoked = 0;
      `)
      for (const uid of uids){
         const existingMetaKey = existingUserMeta.filter(mwum=> mwum.marker_uid == uid).map(mwum => mwum.meta_key);
         const metaCreations = META_CREATION.filter(meta => !existingMetaKey.includes(meta))
         for(const metaCreation of metaCreations){
            newUserMetas.push({
               marking_window_id: markingWindowId,
               marker_uid: uid,
               meta_key: metaCreation,
               value: metaCreation == "Marker_Number"? "local_scor" + uid: 2
            })
         }
      }
      if (newUserMetas.length){
         await dbBulkInsert(this.app, 'marking_window_user_meta', newUserMetas, 'id');
      }
      return;
   }
  
   async getMbapMrsMap(localScores: any[], markingWindowId: number){
      const mbapMrsMap = new Map<number, Map<number, { hasEmptyScore: Boolean, scoreItems: IResponseScoreItem[] }>>();
      const mbapMwiMap = new Map<number, number[]>();
      const unmatchRuleTls = [];
      // Get all the marking window items, and marking score options
      const mbaps = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_BATCH_ALLOCATION_POLICIES)
      const rule_options_map: IMarkingWindowScoreOptions[] = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_MARKING_WINDOW_SCORE_OPTIONS)
      const rule_flags_map: IMarkingWindowScoreFlags[] = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_MARKING_WINDOW_FLAG_OPTIONS)
      const mwiItemMap:IWindowItemInfoMap[] = await dbRawRead(this.app, {markingWindowId}, LOCAL_SCORING_MARKING_WINDOW_ITEM_MAP)
      const mbapOptionFlagMap = new Map<number, { mbap_id: number, rules:(IMarkingWindowScoreOptions | IMarkingWindowScoreFlags)[] }>();
      const mwiIds :number[] = [...new Set(rule_options_map.map(r => r.mwi_id))]
      const windowItemsSupressionMap = await this.app.service('public/scor-scor/batches/claim').getSuppressionMap(mwiIds);
      for (const mbap of mbaps){
         mbapMrsMap.set(mbap.id, new Map<number, { hasEmptyScore: Boolean, scoreItems: IResponseScoreItem[] }>());
         mbapMwiMap.set(mbap.id, [...new Set(rule_options_map.filter(r=>r.batch_alloc_policy_id == mbap.id).map(r => r.mwi_id))]);
         mbapOptionFlagMap.set(mbap.item_id, {
            mbap_id: mbap.id,
            rules: [...rule_flags_map.filter(flag => flag.batch_alloc_policy_id == mbap.id), ...rule_options_map.filter(option => option.batch_alloc_policy_id == mbap.id)]
         })
      }
      
      for(const localScore of localScores){
         const rules = mbapOptionFlagMap.get(localScore.item_id)?.rules;
         const mbap_id = mbapOptionFlagMap.get(localScore.item_id)?.mbap_id;
         let mwi_ids :number[] = [];
         if(mbap_id){
            mwi_ids = mbapMwiMap.get(mbap_id) || [];
         }
         if(!rules){
            // If no item in marking window match local score, ignore it
            unmatchRuleTls.push(localScore)
         }
         else{
            const parsedResponseScore: IResponseScore = await this.convertLocalScoretoMarkingResponse(localScore, rules, mwi_ids, windowItemsSupressionMap)
            if(parsedResponseScore && mbap_id && parsedResponseScore.attempt_id && parsedResponseScore.items){
               const mrsMap: { hasEmptyScore: Boolean, scoreItems: IResponseScoreItem[] } | undefined = mbapMrsMap.get(mbap_id)?.get(parsedResponseScore.attempt_id);
               if(mrsMap){
                  mbapMrsMap.get(mbap_id)!.get(parsedResponseScore.attempt_id)!.scoreItems.push(...parsedResponseScore.items)
                  if(parsedResponseScore.hasEmptyScore){
                     mbapMrsMap.get(mbap_id)!.get(parsedResponseScore.attempt_id)!.hasEmptyScore = true;
                  }
               }
               else{
                  mbapMrsMap.get(mbap_id)?.set(parsedResponseScore.attempt_id, {
                     hasEmptyScore: parsedResponseScore.hasEmptyScore,
                     scoreItems: [...parsedResponseScore.items],
                  })
               }
            }
         }
      }

      for (const mbap of mbaps){
         //If missing score, fill with empty
         await this.fillMissingResponses(mbapMrsMap, mbapMwiMap, mbap.id, mwiItemMap)
      }
      return { mbapMrsMap, mwiIds, windowItemsSupressionMap };
  }

  async fillMissingResponses(mbapMrsMap: Map<number, Map<number, { hasEmptyScore: Boolean, scoreItems: IResponseScoreItem[] }>>, mbapMwiMap: Map<number, number[]>, mbap_id: number, mwiItemMap: IWindowItemInfoMap[]){
      
      const mwi_ids = mbapMwiMap.get(mbap_id);
      const mrsMap = mbapMrsMap.get(mbap_id);
      if(mrsMap && mwi_ids){
         for(const [attempt_id, mrsArray] of mrsMap){
            if(mrsArray.scoreItems.length < mwi_ids.length){
               mrsArray.hasEmptyScore = true;
               const mrsMwiList = mrsArray.scoreItems.map(mrs => mrs.mwi_id)
               for(const mwi_id of mwi_ids){
                  if(!mrsMwiList.includes(mwi_id)){
                     const test_attempt_id = mrsArray.scoreItems[0].attempt_id;
                     const {item_id, taqr_id} = this.getTaqrItemId(mwiItemMap, mwi_id, test_attempt_id)
                     mrsArray.scoreItems.push({
                        attempt_id: attempt_id,
                        tls_id: null,
                        uid: mrsArray.scoreItems[0].uid,
                        item_id: item_id,
                        taqr_id: taqr_id,
                        mbap_id: mrsArray.scoreItems[0].mbap_id,
                        mwi_id:  mwi_id,
                        mspo_id: null,
                        mspf_id: null,
                        value: 0,
                        school_group_id: mrsArray.scoreItems[0].school_group_id,
                        student_accommodation_cache: null
                     })
                  }
               }
            }
         }
      }
      
  }

  getTaqrItemId(mwiItemMap: IWindowItemInfoMap[], mwi_id: number, ta_id: number){
    const mwiItem = mwiItemMap.filter(mwiItem => mwiItem.mwi_id == mwi_id && mwiItem.ta_id == ta_id)[0];
    return {
      item_id: mwiItem.item_id,
      taqr_id: mwiItem.taqr_id
   }
  }
  
  async convertLocalScoretoMarkingResponse(localScore: any, rules: (IMarkingWindowScoreOptions | IMarkingWindowScoreFlags)[], mwi_ids: number[], windowItemsSupressionMap: Map<number, ISuppressionCondition>){
   const scores :IResponseScoreItem[] = []
   const data = await JSON.parse(localScore.data);
   const scales: object = data["scales"];
   const flags: object = data["flagScoreOption"];
   let hasEmptyScore = false;
   // Loop over marking window items, find local score of each item
   let hasFlag = false;
   let isMatchFlag =false;
   for (const mwi_id of mwi_ids){
      const matchWindowItems =  await rules.filter((rule: any) => rule.mwi_id == mwi_id && rule.item_id == localScore.item_id);
      if(matchWindowItems.length > 0){
         if(flags){
            hasFlag = true;
            const matchFlag = await matchWindowItems.filter((matchItem : any) => matchItem.slug == flags && matchItem.mspf_id) as IMarkingWindowScoreFlags[];
            const mspf_id = matchFlag[0]?.mspf_id || null;
               await scores.push({
                  tls_id: localScore.id,
                  uid: localScore.updated_by_uid,
                  attempt_id: localScore.attempt_id,
                  item_id: localScore.item_id,
                  taqr_id: localScore.taqr_id,
                  mbap_id: matchWindowItems[0].batch_alloc_policy_id,
                  mwi_id:  mwi_id,
                  mspo_id: null,
                  mspf_id: mspf_id,
                  value: 0,
                  school_group_id: localScore.schl_group_id,
                  student_accommodation_cache: localScore.student_accommodation_cache || null
               })
            if(matchFlag.length > 0){
               isMatchFlag = true;
            }
         }
         else{
            let matchScores = false;
            for (const [skill_code, score] of Object.entries(scales)){
               if(score){
               const slug = score["scoreCode"];
               const matchScore = await matchWindowItems.filter((matchItem : any) => matchItem.slug == slug && matchItem.skill_code == skill_code && matchItem.mspo_id) as IMarkingWindowScoreOptions[];
               if(matchScore.length > 0){
                  await scores.push({
                     tls_id: localScore.id,
                     uid: localScore.teacher_uid || localScore.updated_by_uid,
                     item_id: localScore.item_id,
                     taqr_id: localScore.taqr_id,
                     attempt_id: localScore.attempt_id,
                     mbap_id: matchWindowItems[0].batch_alloc_policy_id,
                     mwi_id:  mwi_id,
                     mspo_id: matchScore[0].mspo_id,
                     mspf_id: null,
                     value: matchScore[0].value,
                     school_group_id: localScore.schl_group_id,
                     student_accommodation_cache: localScore.student_accommodation_cache || null
                  })
                  matchScores = true;
               }
               }
            }
            if(!matchScores){
               await scores.push({
                  tls_id: localScore.id,
                  uid: localScore.teacher_uid || localScore.updated_by_uid,
                  item_id: localScore.item_id,
                  taqr_id: localScore.taqr_id,
                  attempt_id: localScore.attempt_id,
                  mbap_id: matchWindowItems[0].batch_alloc_policy_id,
                  mwi_id:  mwi_id,
                  mspo_id: null,
                  mspf_id: null,
                  value: 0,
                  school_group_id: localScore.schl_group_id,
                  student_accommodation_cache: localScore.student_accommodation_cache || null
               })
               const { is_scale_rescore_supressed } = await this.isSuppressed(windowItemsSupressionMap, mwi_id, localScore.student_accommodation_cache)
               // If neither is suppressed nor non scored profile, empty score
               if(matchWindowItems[0].is_non_scored_profile == 0 && !is_scale_rescore_supressed){
                  hasEmptyScore = true;
               }
            }
         }
      }
   }
   if(hasFlag && !isMatchFlag){
      hasEmptyScore = true;
   }
   return {
         attempt_id: localScore.attempt_id,
         items : scores,
         success: true,
         hasEmptyScore
      }
   }

   async isSuppressed(windowItemsSupressionMap: Map<number, ISuppressionCondition>, mwi_id: number, student_accommodation_cache: string | null){
      // Check accommodation
      let is_prorated = 0;
      let is_scale_rescore_supressed = 0;
      const supressionConditions = windowItemsSupressionMap.get(mwi_id);
      if(supressionConditions && student_accommodation_cache){
        is_prorated = await this.app.service('public/scor-scor/batches/claim').suppressionCheck(supressionConditions, student_accommodation_cache);
        if(is_prorated){
          is_scale_rescore_supressed = 1;
        }
      }
      return {is_scale_rescore_supressed, is_prorated};
   }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
     throw new Errors.MethodNotAllowed();
  }
}
