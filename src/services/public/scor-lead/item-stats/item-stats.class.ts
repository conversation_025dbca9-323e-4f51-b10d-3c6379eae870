import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { SQL_LEAD_ITEM_STATS, SQL_NUM_TQAR_BY_ITEM, SQL_STANDARD_CONFIRMING_ACCESS, STANDARD_CONFIRMING_POOL, STANDARD_CONFIRMING_SUMMARY } from '../../../../sql/scoring';
import { arrToMap } from '../../../../util/param-sanitization';
interface Data {}

interface ServiceOptions {}

const standardConfirmingStages = [
  "num_stage_1",
  "num_stage_2",
  "num_stage_3",
  "num_stage_4"
]

export class ItemStats implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  /** Return range finding group records */
  async get (marking_window_id: Id, params?: Params): Promise<Data> {
    // < 0.1 sec
    if (params && params.query){
    const uid = await currentUid(this.app, params);
    if(!await this.hasScoringWindowAccessForStandardConfirming(marking_window_id, uid)){
      throw new Errors.Forbidden("NO_ACCESS_TO_STANDARD_CONFIRMING")
    }
    const summaries = await dbRawRead(this.app, {marking_window_id}, STANDARD_CONFIRMING_SUMMARY());
    const isPooledSumamry = true;
    const pooledSummaries = await dbRawRead(this.app, {marking_window_id}, STANDARD_CONFIRMING_SUMMARY(isPooledSumamry));
    for (const summary of summaries){
        const pooledSummary = pooledSummaries.find(pooledSummary => pooledSummary.sync_id == summary.sync_id)
        if (pooledSummary){
          for(const standardConfirmingStage of standardConfirmingStages){
            const selected = pooledSummary[standardConfirmingStage]
            const total = summary[standardConfirmingStage]
            let summaryNum = `${selected} (of ${total})`
            if (selected == total) {
              summaryNum = `${selected}`
            }
            summary[standardConfirmingStage] = summaryNum
            summary[standardConfirmingStage + "_pooled"] = +pooledSummary[standardConfirmingStage]
          }
        }
        else {
          for(const standardConfirmingStage of standardConfirmingStages){
            summary[standardConfirmingStage] = `0 (of ${summary[standardConfirmingStage]})`
            summary[standardConfirmingStage + "_pooled"] = 0
          }
        }
      }
      return summaries;
    }
    return [];
  }

  async hasScoringWindowAccessForStandardConfirming(marking_window_id: Id | number, uid: number) {
    // If has scoring leader or supervisor return true
    // If has standard confirming role, check whether marking window is puased or not.
    const markingWindows = await dbRawRead(this.app, {uid, marking_window_id}, SQL_STANDARD_CONFIRMING_ACCESS);
    if(markingWindows.length > 0){
      return true;
    }
    return false;
  }

  async cacheItemTaqrTally(window_id:Id){
    const records = await dbRawRead(this.app, [window_id], SQL_LEAD_ITEM_STATS);
    const itemIds = [-1].concat(records.map(r => r.id));
    const responseTallyRecords = await dbRawRead(this.app, [itemIds], SQL_NUM_TQAR_BY_ITEM);
    await Promise.all(responseTallyRecords.map(responseRecord => {
      this.app.service('db/write/marking-window-items').patch(responseRecord.id, {
        cached_taqr_tally: responseRecord.num_responses
      })
    }))
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: { poolCount: number, sync_ids: number[], test_attempt_id: number, schl_foreign_id: number }, params?: Params): Promise<Data> {
    const { poolCount, sync_ids, test_attempt_id, schl_foreign_id }= data
    const marking_window_id = id;
    if(test_attempt_id) {
      await this.poolAttempts([test_attempt_id], marking_window_id as number)
      return { message: "success" };
    }
    if(schl_foreign_id) {
      await this.poolSchoolForeignId([schl_foreign_id], marking_window_id as number)
      return { message: "success" };
    }
    let count = +poolCount;
    if(!Number.isInteger(count)) {
      count = 2500;
    }
    for(const sync_id of sync_ids) {
      const mtcs = await dbRawRead(this.app, { marking_window_id, sync_id }, STANDARD_CONFIRMING_POOL(count))
      const mtc_ids = mtcs.map(mtc=>mtc.id);
      if(mtc_ids.length > 0){
        await dbRawWrite(this.app, {mtc_ids, marking_window_id},`
          UPDATE marking_taqr_cache
            SET is_standard_confirming = 1
          WHERE id in (:mtc_ids)
            AND marking_window_id =:marking_window_id
        `)
      }
    }
    
    return { message: "success" };
  }

  async poolAttempts(test_attempt_ids: number[], marking_window_id: number) {
    await dbRawWrite(this.app, {test_attempt_ids, marking_window_id},`
      UPDATE marking_taqr_cache
        SET is_standard_confirming = 1
      WHERE ta_id in (:test_attempt_ids)
        AND marking_window_id =:marking_window_id
    `)
  }

  async poolSchoolForeignId(schl_foreign_ids: number[], marking_window_id: number) {
    const schools = await dbRawRead(this.app, {schl_foreign_ids}, `
       select s.id 
        , s.group_id 
        from schools s where 
          s.foreign_id  in (:schl_foreign_ids)
      `)
    const schl_group_ids = schools.map(school=>school.group_id);
    await dbRawWrite(this.app, {schl_group_ids, marking_window_id},`
      UPDATE marking_taqr_cache
        SET is_standard_confirming = 1
      WHERE schl_group_id in (:schl_group_ids)
        AND marking_window_id =:marking_window_id
    `)
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
