import {
  Id,
  NullableId,
  Paginated,
  Params,
  ServiceMethods,
} from "@feathersjs/feathers";
import _ from "lodash";
import { Application } from "../../../../declarations";
import { Errors } from "../../../../errors/general";
import { dbDateNow } from "../../../../util/db-dates";
import { dbEscapeString, dbRawRead, dbRawWrite } from "../../../../util/db-raw";
import { currentUid } from "../../../../util/uid";
import { SQL_ITEM_EXP_ANS } from "./model/sql";

export interface IExpectedResponseData {
  total?: number;
  formatted_response: string;
  score: number;
  weight: number;
  entries?: any[];
  lang?: string;
}
interface Data {
  response_raw: any;
  test_question_id: number;
  item_set_id?: string;
  preProcessedResponse?: any;
  create_expected_response?: boolean;
  lang?: string
}

interface ServiceOptions {}

export class ExtractItemResponse implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) throw new Errors.BadRequest();

    const { test_question_id, test_window_id } = params.query;
    const expected_response = await dbRawRead(
      this.app,
      {test_question_id, test_window_id},
      SQL_ITEM_EXP_ANS(test_window_id),
    );

    return expected_response;
    // throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  extractResponse = async (data: Data) => {
    const { response_raw, test_question_id } = data;

    if(!test_question_id) throw new Errors.BadRequest("MISSING_QID");

    const response = {
      response_raw: response_raw,
      item_id: test_question_id,
    };

    // extract item-level response
    const subRecord: any = await this.app
      .service("public/test-ctrl/schools/student-attempts")
      .processResponseRaw({
        response,
        responseTypes: <{ [key: string]: boolean }>{},
      });

    return subRecord;
  };

  public async processResponse(response_raw:string, test_question_id:number){
    const subrecord = await this.extractResponse({
      response_raw,
      test_question_id,
    });
    const formatted_response = subrecord.map((record: any) => record.response).join(";");
    const { score, weight } = this.app
      .service("public/student/session-question")
      .aggregateScoreAndWeightFromResponseRaw(response_raw);
    return {
      formatted_response,
      score, 
      weight,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<any> {
    if (!data || !params) throw new Errors.BadRequest();

    const created_by_uid = await currentUid(this.app, params);

    const {
      response_raw,
      test_question_id,
      item_set_id,
      create_expected_response,
      preProcessedResponse,
      lang
    } = data;

    let formatted_response, score, weight;
    if (preProcessedResponse){
      formatted_response = preProcessedResponse.formatted_response
      score = preProcessedResponse.score
      weight = preProcessedResponse.weight
    }
    else if (response_raw != '{}') {
      const processedResponse = await this.processResponse(response_raw, test_question_id);
      formatted_response = processedResponse.formatted_response
      score = processedResponse.score
      weight = processedResponse.weight
    }
    else {
      throw new Error('INVALID_RESPONSE')
    }

    if (create_expected_response && formatted_response?.trim().length) {

      const previousRecords = await dbRawRead(this.app, {
        item_id: test_question_id,
        formatted_response,
        score,
        weight,
        lang
      }, `
        select id, formatted_response
        from test_question_expected_responses tqer
        where item_id = :item_id
          and BINARY formatted_response = :formatted_response
          and score = :score
          and weight = :weight
          and lang = :lang
          and is_revoked = 0
      `);

      if (previousRecords.length){
        for (let record of previousRecords){
          await this.app
            .service("db/write/test-question-expected-responses")
            .patch(record.id, {
              latest_confirmed_on: dbDateNow(this.app),
              latest_confirmed_by_uid: created_by_uid,
            });
        }
      }
      else {
        // add entry to the table
        await this.app
          .service("db/write/test-question-expected-responses")
          .create({
            item_id: test_question_id,
            created_by_uid,
            formatted_response,
            response_raw,
            score,
            weight,
            item_set_id,
            lang
          });
      }
    }

    const entries = await this.getEntryResponseID(test_question_id, lang);
    
    return {
      formatted_response,
      score,
      weight,
      lang,
      entries,
    };

    // throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: any, params?: Params): Promise<any> {
    const {
      patchMode, 
      test_question_id, 
      response_raw, 
      formatted_response, 
      lang, 
      coded_response,
      is_item_score_exceptions,
      score_override,

    } = data
    if (patchMode == 'CODED_RESPONSE'){
      const item_id = id;
      const recordsToUpdate = await dbRawRead(this.app, {item_id, formatted_response, lang}, `
        select id
        from test_question_expected_responses tqer 
        where item_id = :item_id
          and BINARY formatted_response = :formatted_response
          and lang = :lang
        and is_revoked = 0
      `)
      for (let record of recordsToUpdate){
        await this.app.service('db/write/test-question-expected-responses').patch(record.id, {coded_response}) // todo: leaving this in for now, but we need a log for this too
      }
      return {patchMode, coded_response}
    }
    else if (patchMode == 'SCORE_OVERRIDE'){
      const item_id = id;
      const recordsToUpdate = await dbRawRead(this.app, {item_id, formatted_response, lang}, `
        select id
        from test_question_expected_responses tqer 
        where item_id = :item_id
          and BINARY formatted_response = :formatted_response
          and lang = :lang
        and is_revoked = 0
      `)
      // todo: window bound?
      for (let record of recordsToUpdate){
        await this.app.service('db/write/test-question-expected-responses').patch(
          record.id, {
            is_item_score_exceptions,
            score_override,
          }) 
          // todo: leaving this in for now, but we need a log for this too
      }
      return {patchMode, is_item_score_exceptions, score_override}
    }
    else if (test_question_id && response_raw){
      const { formatted_response, score, weight } = await this.processResponse(response_raw, +test_question_id);
      return <any> {
        formatted_response,
        score,
        weight
      }
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<any> {
    if (!id || !params || !params.query) throw new Errors.BadRequest();
    
    const revoked_by_uid = await currentUid(this.app, params);
    const { formatted_response, score, lang } = params.query;

    if (formatted_response === undefined || score === undefined){
      throw new Errors.BadRequest("MISSING_DATA");
    }

    const remove_ids = await dbRawRead(this.app, {id, formatted_response}, `
      SELECT GROUP_CONCAT(tqer.id) AS ids
        FROM test_question_expected_responses tqer
        where tqer.is_revoked = 0
          and tqer.item_id = :id
          and BINARY tqer.formatted_response = :formatted_response
          and tqer.score like ${await dbEscapeString(score, true)}
          and ${lang ? `tqer.lang = '${lang}'` : 'tqer.lang is null'}
        GROUP BY tqer.lang, tqer.formatted_response, tqer.score
    ;`);

    let ids_removed;
    if(remove_ids && remove_ids.length && remove_ids[0].ids){
      const remove_ids_list = remove_ids[0].ids
        .split(",")
        .map((id: string) => Number(id))
        .filter((id: number) => id != null);

      ids_removed = await Promise.all(remove_ids_list.map((id: number) => {        
        return this.app.service('db/write/test-question-expected-responses').patch(id, {
          is_revoked: 1,
          revoked_on: dbDateNow(this.app), 
          revoked_by_uid,
        })
      }));
    }

    return ids_removed;
    // throw new Errors.MethodNotAllowed();
  }

  async getQuestionRecord(test_question_id:number){
    const questionRecord = await dbRawRead(this.app, {test_question_id}, `
      select q.id
            , q.question_label as label
            , q.config
            , q.question_set_id
      FROM test_questions q
      where q.id IN (:test_question_id)
    ;`);
    return questionRecord;
  }

  async getEntryResponseID(test_question_id:number, lang:string | undefined){
    type QuestionEntry = {
      response_id:string,
      entry_question_text:string,
      response_type:string
    }
    type RespContent = {
      response_id:string,
      response_code:number,
      response:string,
      response_text:string
    }
    const entries:QuestionEntry[] = [];
    const responsesContent:RespContent[] = [];
    const questionRecord = await this.getQuestionRecord(test_question_id);

    questionRecord.forEach(question => {
      const {
        id,
        label
      } = question;

      let entryIdRef: any;
      let question_text = '';      
      const MCQ_OPTION_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

      const processContent = function (el:any) {
        if (Array.isArray(el.content)) {
          el.content.forEach(processContent);
        }
        if (Array.isArray(el.advancedList)) {
          el.advancedList.forEach(processContent);
        }
        if (lang === 'en') {
          entryIdRef = el.entryId;
        }
        if (el.elementType == 'text'){
          // to do, this is not capturing more advanced forms of text, I think we do this in one of the print modes for the question (or script gen)
          if (question_text){ 
            question_text = question_text +'\n'; 
          }
          question_text += el.caption;
        } 
        else if (el.elementType == 'mcq'){
          // not handling all forms of option types (see script gen algo for pieces of more robust approach)
          const response_id = `i${id}.e${el.entryId}`;
          if (_.find(entries, e => e.response_id === response_id)) {
            return;
          }
          entries.push({ response_id, entry_question_text: '', response_type: 'mcq' })
          el.options.forEach((option:any, i:number) => {
            responsesContent.push({
              response_id,
              response_code: i,
              response: MCQ_OPTION_MAP[i],
              response_text: option.content
            })
          })
        } 
        else if (el.elementType === 'select_table') {
          const responseIdBase = `i${id}.e${entryIdRef}`;
          el.checkBoxRows.forEach((r:any,rowIdx:any) => {
            const response_id = `${responseIdBase}.r${rowIdx}`;
            if (_.find(entries, e => e.response_id === response_id)) {
              return;
            }
            const entry_question_text = '\n' + el.leftCol[rowIdx].content.caption //TODO
            entries.push({ response_id, entry_question_text, response_type: 'select_table' })
            r.forEach((box:any, boxIdx:any) => {
              responsesContent.push({
                response_id,
                response_code: boxIdx,
                response: MCQ_OPTION_MAP[boxIdx],
                response_text: el.topRow[boxIdx].content.caption
              })
            });
          });
        } else if (el.elementType === 'input') {
          const response_id = `i${id}.e${el.entryId}`;
          if (_.find(entries, e => e.response_id === response_id)) {
            return;
          }
          entries.push({ response_id, entry_question_text: el.caption || '', response_type: 'input'})
        } else {
          console.log('unknown elementType', el.elementType)
        }
      }

      let config:any = {};
      try { config = JSON.parse(question.config) } catch(e){}
      let content:any[] = config.content;
      const questions:any[] = []

      content.forEach(processContent);

      entries.forEach(qEntry => {
        const {response_id, entry_question_text, response_type} = qEntry;
        questions.push({
          id: id,
          lang,
          response_id,
          label,
          question_text: question_text,
          sub_question_text: entry_question_text,
          response_type,
        });
      })
    })
    return entries;
  }
}
