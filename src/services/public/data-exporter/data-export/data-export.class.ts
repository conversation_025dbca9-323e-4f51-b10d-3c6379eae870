import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import EqaoDataInstance from '../../../../util/eqao-data-instance';
import { camelify, snakeify } from '../../../../util/caseify';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { mapValues } from 'lodash';
import { dbRawRead, dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import { randHash } from '../../../../util/random';
import {  WHITELABEL_CONTEXT } from '../../../../constants/whitelabel';
import { DagAsset, DagAssetDependencyTypes, DependencySourcingType, TEMP_DAG_ASSETS } from './model/assets';
import { DagJobDef, TEMP_DAG_JOBS } from './model/jobs';
import {
  sourceDependencies,
  sourcePartDependencies,
  storeAsset,
  partitionOut,
  concatPartitionedAsset,
  repartitionOut,
  launchPackager,
  IRunState,
  IPartitonInstance,
} from './storage/_index';
import { validateConfig } from './config/validation'

import { IDagJobConfig, IDagJobState, JobType } from '../data-export-queries/types/type';
import type { AssetSlug } from '../data-export-queries/types/type';
import { ICanonicalTagCacheFilter } from './model/filters';


export enum DataExportStatus {
  WAITING = 'WAITING',
  RUNNING = 'RUNNING',
  COMPLETE = 'COMPLETE',
  ERROR = 'ERROR',
  CANCELLED = 'CANCELLED',
}
export interface Data {
  testWindowId: number,
  isMainExport?: boolean,
  triggeredFrom: string,
  exportApiBase?: string,
  jobName?: string,
  uidFilter?: number[],
  twtarFilter?: number[],
  failureReason?: string,
  lastStageCompleted?: string,
  status?: DataExportStatus,
  pid?: number,
  urls?: any,
  description?: string,
  startedOn?: any,
  includePsychRun?: boolean,
  enableBusinessRulesValidation?: number,
  tableFilterId?: number,
  config?: {[key: string]: any},
}


interface ServiceOptions {}

interface ISourceConfigDataFrame {
  type: "dataframe" | "df"
  assetSlug: AssetSlug,
  cols?: string[],
}

interface ISourceConfigAssetCol {
  type:  "asset-col",
  assetSlug: AssetSlug,
  col: string,
}

type ISourceConfig = ISourceConfigAssetCol | ISourceConfigDataFrame;

interface IExportJob  {
  id: number,
  test_window_id?: number,
  created_on: any,
  path_hash: string,
  is_bg: number
  created_by_uid: number,
  dag_job_name: string,
  description: string,
}

export class DataExport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  instance: EqaoDataInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.instance = new EqaoDataInstance(app);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (_params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const results = camelify(await this.app.service('db/write/data-export-jobs').get(id)) as Data;
    if (!results.urls) {
      results.urls = {};
    } else {
      results.urls = JSON.parse(results.urls);
    }
    results.urls = mapValues(results.urls, (value) => {
      if (value){
        return generateS3DownloadUrl(value.replace('s3://storage.mathproficiencytest.ca/',''))
      }
    })
    return results;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params: Params): Promise<Data> {

    if (Array.isArray(data)) {
      throw new Errors.BadRequest('only one export run can be created per http request');
    }

    const { testWindowId, jobName, isMainExport, description } = data;
    const created_by_uid = await currentUid(this.app, params);
    const availableJobs = Object.keys(TEMP_DAG_JOBS);

    // TODO: support asset-based requests
    if (!jobName) {
      throw new Errors.BadRequest("Must supply a jobName");
    }

    // Baseline configuration needs to be an object with test_window_ids
    // WARNING: if `testWindowIds` is defined, then `testWindowId` is ignored
    const config = data.config || {};
    if (!config.test_window_ids && testWindowId > 0) {
      config.test_window_ids = [testWindowId];
    }
    const isBg = !isMainExport

    // todo: how long do we need this temp logic in here?
    if (jobName == 'tc-attempts'){
      // todo: make this generalized test window call something that comes from another endpoint
      const dagJobName = await this.confirmTwDefaultJob(testWindowId, jobName);

      // fetch appropriate job type of the window
      const {export_id, path_hash} = await this.runJob(created_by_uid, {
        jobType: JobType.JOB,
        jobName: dagJobName,
        canonicalSlug: jobName,
        notes: description || '',
        isBg: true,
        testWindowId,
        config,
      })
      return <any> {export_id, path_hash}
    }

    if (availableJobs.includes(jobName)) { // Job with item responses included
      // fetch appropriate job type of the window
      const {export_id, path_hash, configWarnings} = await this.runJob(created_by_uid, {
        jobType: JobType.JOB,
        jobName: jobName,
        canonicalSlug: jobName,
        notes: description || '',
        isBg,
        testWindowId,
        config,
      })
      return <any> {export_id, path_hash, configWarnings}
    }

    throw new Errors.BadRequest("Unrecognized Job Name");
  }

  async confirmTwDefaultJob(testWindowId:number, defaultJobName:string){
    const records = await dbRawReadReporting(this.app, {testWindowId}, `
      select dettj.job_slug
      from test_windows tw
      join data_export_tw_type_jobs dettj
        on dettj.tw_type_slug  = tw.type_slug
        and dettj.is_revoked = 0
      where tw.id = :testWindowId
    `)
    if (records.length ){
      const record = records[0];
      if (record.job_slug){
        return record.job_slug;
      }
    }
    return defaultJobName;
  }

  async runJob(created_by_uid:number, jobConfig:IDagJobConfig){
    const SKIP_DB_WRITE = this.app.get("isDevMode") && (!this.app.get("testDbWrites"));
    if (SKIP_DB_WRITE) {
      console.log("Running in dev mode (no db writes)")
    } else {
      console.log("WARNING: there will be database writes to data-export-jobs")
    }

    // TODO: determine how to handle asset-based runs in the DB
    if (!jobConfig.jobName){ throw new Errors.BadRequest('MISSING_JOB_NAME') }

    const jobName = jobConfig.jobName || '*ASSET?'
    const path_hash = randHash(16)
    const canonicalSlug = jobConfig.canonicalSlug || undefined

    // NOTE: job configuration happens before launching, so that malformed jobs send immediate feedback to the user.
    const jobDef:DagJobDef = TEMP_DAG_JOBS[jobConfig.jobName];

    // Check for required configs;
    for (let k in jobDef.pipeline_config_req) {
      if (!jobConfig.config || jobConfig.config[k] === undefined) {
        throw new Errors.BadRequest("Missing required configuration")
      }
    }
    // Use defaults, and user-provided values.
    let pipeline_config = {
      ...jobDef.pipeline_config,
      ...jobConfig.config,
    }

    // Prepare the sequence
    let sequence: DagAsset[] = []
    try {
      sequence = buildAssetSequence(jobDef, TEMP_DAG_ASSETS);
    } catch (err) {
      let message = "Failed to build data processing sequence."
      if (err instanceof Error) {
        // TODO: (jladan) I don't think we should emit the original error message to the client
        // message = message + " :: "+ err.message
        console.error("DAG :: failed to build asset sequence :: " + err.message)
      }
      if (err instanceof Errors.Unprocessable) {
        throw err;
      }
      throw new Errors.Unprocessable(message)
    }

    const {errors, warnings} = await validateConfig(sequence, pipeline_config);
    if (errors.length > 0) {
      throw new Errors.Unprocessable('Errors found in asset configuration', {errors, warnings, sequence});
    }

    // Create the export job record
    let exportJob: IExportJob = {
        id: -1,
        test_window_id: jobConfig.testWindowId,
        created_on: dbDateNow(this.app),
        path_hash,
        is_bg: jobConfig.isBg ? 1 : 0,
        created_by_uid,
        dag_job_name: jobName,
        description: jobConfig.notes,
      }
    if (!SKIP_DB_WRITE) {
      exportJob = await this.app.service('db/write/data-export-jobs').create({
        test_window_id: jobConfig.testWindowId,
        created_on: dbDateNow(this.app),
        path_hash,
        is_bg: jobConfig.isBg ? 1 : 0,
        created_by_uid,
        dag_job_name: jobName,
        description: jobConfig.notes,
      });
    } else {
      console.log(`Executing export on ${jobConfig.testWindowId}, hash: ${path_hash}`);
      console.log(exportJob);
      console.log(pipeline_config);
    }

    const basePath = `data-jobs/${WHITELABEL_CONTEXT}/${exportJob.id}/${path_hash}/`;

    const jobStartToFinish = async () => {
      const timestampStart = +(new Date())

      // setup state

      // 0a. Setting up execution state

      const state:any = {};
      const partitions: {[key:string]: IPartitonInstance} = {};
      const dfCache: Map<string, any> = new Map();
      const assetRef: Map<string, any> = new Map();

      const runState: IRunState = {
        state,
        pipeline_config,
        storagePath: basePath,
        assetRef,
        dfCache,
        partitions,
      };

      // 0. Setting up execution state

      for (let asset of sequence){
        state[asset.slug] = {
          isReady: true,
          isStarted: false,
          isComplete: false,
          progress: 0,
          filepath: null,
          size: 0,
          timeMs: 0,
        }
        assetRef.set(asset.slug, asset);
      }
      // Set up dependencies and caching
      for (let asset of sequence){
        for (let dep of asset.dependencySourcings) {
          if (dep.type === DependencySourcingType.ASSET_COL || dep.type === DependencySourcingType.DATAFRAME) {
            state[asset.slug].isReady = false;
            const depAsset: DagAsset = assetRef.get(dep.config.asset || '');
            // Don't bother caching if asset only needed by partitioned assets
            if (!asset.partitionBy) {
              depAsset.storeCache = depAsset.storeCache ?? false;
            }
          }
        }
      }
      const dagConfig = {
        v:2,
        jobDef: jobDef,
        jobConfig: jobConfig,
        pipeline_config,
        sequence,
      };

      await this.trackStart(exportJob, dagConfig, state, SKIP_DB_WRITE);
      let isCancelled = false;
      try {
        for (let seq of sequence){
          // TODO: update readiness
          isCancelled = await this.checkCancelled(exportJob);
          if (isCancelled) {
            break;
          }
          await this.runStep(seq, runState, exportJob, dagConfig, SKIP_DB_WRITE);
        }
        // 2. Complete
        if (!isCancelled) {
          // await this.launchPackager(exportJob.id); // Make them press a button if needed
          await this.trackCompletion(exportJob, sequence, state, SKIP_DB_WRITE)
          await this.replaceCanonicalTagCache(canonicalSlug, exportJob, {test_window_id: jobConfig.testWindowId}, SKIP_DB_WRITE)
        } else {
          console.log("Job cancelled by user")
        }
      } catch (errMsg) {
        await this.trackError(errMsg, exportJob, dagConfig, sequence, state, SKIP_DB_WRITE)
      }
      console.log("Job execution time", +(new Date()) - timestampStart);
    }

    jobStartToFinish();
    return {export_id: exportJob.id, path_hash, configWarnings: warnings}

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Partial<Data>, params?: Params): Promise<any> {
    if (id && params?.query?.action && params?.query?.action == "cancel") {
      // first check if the job is already complete
      const exportJob = await this.app.service('db/read/data-export-jobs').get(id, {query: {$select: ['status']}});
      if (exportJob.status == DataExportStatus.COMPLETE) {
        return {alreadyComplete: true, cancelled: false}
      }
      if (exportJob.status == DataExportStatus.CANCELLED) {
        return {alreadyComplete: true, cancelled: true}
      }
      return await this.app.service('db/write/data-export-jobs').patch(id, {status: DataExportStatus.CANCELLED})
    }

    // TODO: I don't think this "Error out all executing jobs at once" logic should be supported
    if(params?.query?.action && params?.query?.action == "cancel") {
      const running = await dbRawRead(this.app, [], `
        SELECT *
        FROM data_export_jobs
        WHERE status = "RUNNING"
      `);

      if(running.length == 0) {
        return {noneRunning: 1}
      }

      return await dbRawWrite(this.app, [], `
        UPDATE data_export_jobs
          SET status = "ERROR"
        WHERE status = "RUNNING"
        ;
      `);
    }



    if (!id || !data) {
      throw new Errors.BadRequest;
    }

    delete (data as any).id
    return await this.app.service('db/write/data-export-jobs').patch(id, snakeify(data))
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // 0b. Standard Functions

  /**
   * Track start of a job
   *
   * TODO: define types for the exportJob and dagConfig
    */
  async trackStart (exportJob: IExportJob, dagConfig: any, state: IDagJobState, SKIP_DB_WRITE: boolean) {
    if (!SKIP_DB_WRITE) {
      await this.app.service('db/write/data-export-jobs').patch(exportJob.id, {
        started_on: dbDateNow(this.app),
        status: 'IN_PROGRESS',
        dag_job_name: exportJob.dag_job_name,
        dag_config: JSON.stringify(dagConfig),
        dag_state: JSON.stringify({
          v: dagConfig.v,
          state,
        }),
      })
    } else {
      console.log("Patching job started (db-write skipped)")
    }
  }

  async checkCancelled(exportJob: IExportJob) {
    const jobStatus = await dbRawReadReporting(this.app, {exportJobId: exportJob.id}, `
        SELECT status
        FROM data_export_jobs
        WHERE id = :exportJobId
      `);
    if(jobStatus.length == 0) {
      return false // NOTE: in dev mode, there is no db record to track
    }
    if (jobStatus[0].status == DataExportStatus.COMPLETE) {
      return true
    }
    return false
  }

  /**
   * Track progress of a job
   */
  async trackProgress (exportJob: IExportJob, dagConfig: any, state: IDagJobState, SKIP_DB_WRITE: boolean) {
    if (!SKIP_DB_WRITE) {
      await this.app.service('db/write/data-export-jobs').patch(exportJob.id, {
        updated_on: dbDateNow(this.app),
        dag_config: JSON.stringify(dagConfig),
        dag_state: JSON.stringify({
          v: dagConfig.v,
          state,
        }),
      })
    } else {
      console.log("Patching jobs progress (db-write skipped)")
    }
  }

  async trackCompletion (exportJob: IExportJob, sequence: any[], state: any[], SKIP_DB_WRITE = false) {
    const urls:any = {};
    for (let asset of sequence){
      urls[asset.slug] = state[asset.slug].filepath
    }
    if (!SKIP_DB_WRITE) {
      await this.app.service('db/write/data-export-jobs').patch(exportJob.id, {
        completed_on: dbDateNow(this.app),
        status: 'COMPLETE',
        urls: JSON.stringify(urls),
      })
    } else {
      console.log('Export Job Complete')
      console.log(urls)
      console.log("please clean up after yourself")
    }
  }

  async trackError(errMsg: unknown, exportJob: IExportJob, dagConfig: any, sequence: any[], state: any[], SKIP_DB_WRITE: boolean) {
    const urls: any = {};
    for (let asset of sequence) {
      // todo: determine which asset was running when error occured
      urls[asset.slug] = state[asset.slug].filepath
    }
    let failure_reason: string;
    if (errMsg instanceof Error) {
      failure_reason = JSON.stringify({ 'msg': errMsg.toString(), 'trace': errMsg.stack })
    } else {
      failure_reason = String(errMsg);
    }
    // TODO: add the error as part of the dag_state.
    const patch = {
      updated_on: dbDateNow(this.app),
      status: DataExportStatus.ERROR,
      urls: JSON.stringify(urls),
      dag_state: JSON.stringify({
        v: dagConfig.v,
        state,
      }),
      failure_reason
    };
    if (!SKIP_DB_WRITE) {
      await this.app.service('db/write/data-export-jobs').patch(exportJob.id, patch)
    } else {
      console.log('Export Job Error')
      console.log(patch)
      console.log("please clean up after yourself")
    }
  }

  async replaceCanonicalTagCache (canonicalSlug: string|undefined, exportJob: IExportJob, canonicalTagFilter:ICanonicalTagCacheFilter, SKIP_DB_WRITE = false) {
    if (!SKIP_DB_WRITE) {
      // replace canonical cache (this can be set as part of the export setting, but often would be done post-hoc)
      if (canonicalSlug){
        const tagSlug = canonicalSlug;
        const {
          test_window_id,
          twtar_id,
          schl_group_id,
          schl_dist_group_id,
        } = canonicalTagFilter
        const latestJobTagRecord = await this.app.service('db/write/data-export-job-tags').create({
          export_id: exportJob.id,
          slug: tagSlug,
          test_window_id,
          twtar_id: twtar_id,
          schl_group_id: schl_group_id,
          schl_dist_group_id: schl_dist_group_id,
          created_by_uid: exportJob.created_by_uid,
          created_on: dbDateNow(this.app),
        })
        // revoke older versions
        await dbRawWrite(this.app,
          {
            slug: tagSlug,
            test_window_id,
            twtar_id,
            schl_group_id,
            schl_dist_group_id,
            id: latestJobTagRecord.id,
            created_by_uid: exportJob.created_by_uid
          }, `
          UPDATE data_export_job_tags
          SET is_revoked = 1
            ,revoked_by_uid = :created_by_uid
            ,revoked_on = now()
          WHERE is_revoked = 0
            and id != :id
            and slug = :slug
            ${test_window_id ? 'and test_window_id = :test_window_id' : 'and test_window_id is null' }
            ${twtar_id ? 'and twtar_id = :twtar_id' : 'and twtar_id is null' }
            ${schl_group_id ? 'and schl_group_id = :schl_group_id' : 'and schl_group_id is null' }
            ${schl_dist_group_id ? 'and schl_dist_group_id = :schl_dist_group_id' : 'and schl_dist_group_id is null' }
        `)
      }
    } else {
      console.log("Skipped replacing the canonical tag cache")
    }
  }

  async runStep (stepDef: DagAsset, runState: IRunState, exportJob: any, dagConfig: any, SKIP_DB_WRITE = false) {
    const { state } = runState;
    const timestampStart = +(new Date())
    console.log(`DAG JOB :: ${exportJob.id} ; ${exportJob.dag_job_name} :: running step`, stepDef.slug)
    state[stepDef.slug].isStarted = true;
    state[stepDef.slug].startedOn = timestampStart;
    await this.trackProgress(exportJob, dagConfig, state, SKIP_DB_WRITE)
    let numRecords:number = 0;
    if (stepDef.partitionBy) {
      let {filepath, size, partitionInfo} = await this.processPartitionedData(runState, stepDef, stepDef.partitionBy.key);
      // Add total number of records to the asset
      numRecords = 0;
      for (let part of partitionInfo.partitions) {
        numRecords += part.length
      }
      state[stepDef.slug].numRecords = numRecords
      state[stepDef.slug].timeMs = +(new Date()) - timestampStart;
      state[stepDef.slug].size = size;
      partitionInfo.filepath = filepath;
      state[stepDef.slug].partitions = {[partitionInfo.partKey]: partitionInfo};
      state[stepDef.slug].progress = 0.8;
      // TODO: determine how to track progress during partitioned (and chunked) operations
      await this.trackProgress(exportJob, dagConfig, state, SKIP_DB_WRITE)
      // Concatenate the partitioned data if specified
      if (stepDef.partitionBy.concatOut) {
        const {filepath, size } = await concatPartitionedAsset(runState, stepDef);
        state[stepDef.slug].size = size;
        state[stepDef.slug].filepath = filepath;
      }
      // Re-partition if specified
      if (stepDef.partitionOut) {
        // TODO: decide how to save the path of the partition description
        const {partitionInfo} = await repartitionOut(runState, stepDef);
        state[stepDef.slug].partitions[partitionInfo.partKey] = partitionInfo;
        runState.partitions[partitionInfo.partKey] = partitionInfo;
      }
    } else {
      const props = await sourceDependencies(runState, stepDef);
      const records = await this.processData(stepDef, props);
      numRecords = records.length;
      if (stepDef.storeCache) {
        runState.dfCache.set(stepDef.slug, records)
      }
      // Create partition if required
      if (stepDef.partitionOut) {
        // TODO: decide how to save the path of the partition description
        const {partitionInfo} = await partitionOut(runState, stepDef, records);
        state[stepDef.slug].partitions = {[partitionInfo.partKey]: partitionInfo};
        runState.partitions[partitionInfo.partKey] = partitionInfo;
      }
      state[stepDef.slug].timeMs = +(new Date()) - timestampStart;
      const recordsJson = JSON.stringify(records)
      state[stepDef.slug].size = recordsJson.length;
      // Add the number of records to dag_state
      state[stepDef.slug].numRecords = records.length;
      state[stepDef.slug].progress = 0.8;
      await this.trackProgress(exportJob, dagConfig, state, SKIP_DB_WRITE)
      console.log(`    numRecords ${numRecords} ; size ${recordsJson.length / 1024} kiB`);
      state[stepDef.slug].filepath = await storeAsset(runState, stepDef.slug, recordsJson)
    }
    state[stepDef.slug].timeMs = +(new Date()) - timestampStart;
    state[stepDef.slug].progress = 1;
    state[stepDef.slug].numRecords = numRecords;
    state[stepDef.slug].isComplete = true;
    await this.trackProgress(exportJob, dagConfig, state, SKIP_DB_WRITE)
  }

  async runTransforms(stepDef: DagAsset, sequenceData: {[key:string]: any}) {
    const sequence = stepDef.methodConfig.sequence;

    let stepIndex = 0;
    for (let tStep of sequence) {
      stepIndex++;
      try {
          const outName = tStep.df_output;
          const records = await this.app.service('public/data-exporter/data-export-queries').runTransform(tStep, sequenceData);
          sequenceData[outName] = records;
      } catch (err) {
        if (err instanceof Error) {
          err.message = `${stepDef.slug} :: step ${stepIndex}: ` + err.message
        }
        throw err
      }
    }
    return sequenceData[stepDef.methodConfig.output];
  }

  async processPartitionedData(runState: IRunState, stepDef: DagAsset, partKey: string) {
    const partInfo = runState.partitions[partKey];
    if (!partInfo) {
      throw new Errors.Unprocessable(`partition ${partKey} does not exist`);
    }
    let numRecordsTotal = 0;
    const partitionInfo: IPartitonInstance = {partKey, field: partInfo.field, partitions: []};
    const numPartitions = partInfo.partitions.length;
    for (let [idx, part] of partInfo.partitions.entries()) {
      const partName = part.name;
      const partSlug = `${stepDef.slug}/${partName}`;
      const props = await sourcePartDependencies(runState, stepDef, partKey, partName);
      const partRecords = await this.processData(stepDef, props);
      numRecordsTotal += partRecords.length;
      const recordsJson = JSON.stringify(partRecords);
      const partPath = await storeAsset(runState, partSlug, recordsJson);
      partitionInfo.partitions.push({name: partName, path: partPath, partSlug, length: partRecords.length, size: recordsJson.length});
      console.log(`      step ${stepDef.slug} : Part ${idx+1} of ${numPartitions}`)
      // TODO: track progress here
    }
    const jsonPartitionInfo = JSON.stringify(partitionInfo);
    const filepath = await storeAsset(runState, `${stepDef.slug}/${partKey}`, jsonPartitionInfo);
    return {
      filepath,
      numRecordsTotal,
      size: jsonPartitionInfo.length,
      partitionInfo,
    }
  }

  async processData(stepDef: DagAsset, props: any) {
    let records: any[];
    // TODO: Confirm that the  following will work given partitioned data in props
    switch (stepDef.method) {
      case "query":
      case "query-chunked":
        records = await this.app.service('public/data-exporter/data-export-queries').runQuery({
          serviceName: stepDef.method,
          queryName: stepDef.methodConfig.querySlug,
          props,
          chunkedParam: stepDef.methodConfig.chunkedParam,
          chunkSize: stepDef.methodConfig.chunkSize,
          makeDistinct: stepDef.methodConfig.makeDistinct,
          categories: stepDef.methodConfig.categories
        });
        break;
      case "api-request":
      case "api-request-chunked":
        records = await this.app.service('public/data-exporter/data-export-queries').runApiRequest({
          serviceName: stepDef.method,
          endpoint: stepDef.methodConfig.endpoint,
          method: stepDef.methodConfig.method,
          props: {}, // todo
          data: {...stepDef.methodConfig.data, ...props}, // TODO: smarter dependency injection
          chunkedParam: stepDef.methodConfig.chunkedParam,
          chunkSize: stepDef.methodConfig.chunkSize,
        });
        break;
      case "api-method":
      case "api-method-chunked":
        records = await this.app.service('public/data-exporter/data-export-queries').runApiMethod({
          serviceName: stepDef.method,
          slug: stepDef.methodConfig.slug,
          endpoint: stepDef.methodConfig.endpoint,
          method: stepDef.methodConfig.method,
          // TODO: the specific arguments to pass should be based on the method config
          data: {...stepDef.methodConfig.data, ...props}, // TODO: smarter dependency injection
          chunkedParam: stepDef.methodConfig.chunkedParam,
          chunkSize: stepDef.methodConfig.chunkSize,
        });
        break;
      case "transforms":
        records =  await this.runTransforms(stepDef, props);
        break;
      default:
        console.log(`${stepDef.slug} :: Unrecognized dag-method: ${stepDef.method}`);
        throw new Errors.Unprocessable(`${stepDef.slug} :: Unrecognized dag-method: ${stepDef.method}`);
    }
    return records;
  }

  async launchPackager(exportId: any) {
    await launchPackager(exportId)
  }
}

const buildAssetSequence = (jobDef: DagJobDef, allAssets: DagAsset[]): DagAsset[] => {
  // temp: very simple function to just force a correct order into a flat sequence

  const targetAssetSlugs = jobDef.assets;
  const scopedAssets = allAssets.filter( (asset) => !asset.scopes || asset.scopes.length == 0 || asset.scopes.includes(jobDef.scope));
  const missingAssets: string[] = [];

  // build a quick asset reference map
  const allAssetRef = new Map();
  for (const asset of scopedAssets) {
    allAssetRef.set(asset.slug, asset);
  }

  // define recursive function
  const prioritizedAssets:string[] = [];
  const parentAssets:string[] = []
  const addSlugToPrioList = (slug:string) => {
    if (!prioritizedAssets.includes(slug)) {
      prioritizedAssets.push(slug);
    }
  }
  const prioritizeDependentAssets = (assetSlug:string, parentAssets:string[]) => {
    if (!parentAssets.includes(assetSlug)){
      parentAssets.push(assetSlug)
      const asset = allAssetRef.get(assetSlug);
      if (!asset) {
        missingAssets.push(assetSlug);
        return
      }
      for (const depSourcing of asset.dependencySourcings) {
        if (DagAssetDependencyTypes.includes(depSourcing.type)) {
          // TODO: would be better to exclude config than require adding all dependency types to list
          prioritizeDependentAssets(depSourcing.config.asset, parentAssets)
        }
      }
      addSlugToPrioList(assetSlug)
    }
  }
  for (let slug of targetAssetSlugs){
    prioritizeDependentAssets(slug, parentAssets);
  }

  // create sequence based on prioritized assets
  const sequence = [];
  for (let assetSlug of prioritizedAssets){
    sequence.push(allAssetRef.get(assetSlug))
  }

  if (missingAssets.length > 0) {
    throw new Errors.Unprocessable(`Missing assets for job definition`, {missingAssets});
  }

  return sequence;
}

