import { Application } from "@feathersjs/express";
import { dbRawReadReporting } from "../../../../../util/db-raw";
import { DataJobLogger } from "./logger";

interface IServiceQuerySimple { 
    queryName:string, 
    query:string, 
    categories?:string[],
    props:{
        [name:string]: string | number | string[] | number[]
    }, 
    logger: DataJobLogger,
}

export const serviceQuerySimple = async (app:Application, config:IServiceQuerySimple) => {

    const {
        queryName,
        query,
        props,
        categories
    } = config;

    if(categories) {
        props['categories'] = categories;
    }
    
    const timeStart = +(new Date())

    const queryTagged = `/*${queryName}*/ ${query}`

    const records = await dbRawReadReporting(app, props, queryTagged)

    const timeDiff =  ((+(new Date()) - timeStart)/1000).toFixed(3)
    console.log('query-simple ::', queryName, 'end', timeDiff)

    return records
    

}