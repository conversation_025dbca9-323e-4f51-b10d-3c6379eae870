import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES:IExportQueryDef = {
    requiredInputs: [
        'item_ids'
    ],
    queryGen: (config:IQueryConfig) => `
        WITH ranked_responses AS (
            SELECT
                tqer.id AS tqer_id,
                tqer.item_id,
                tqer.lang,
                tqer.created_by_uid,
                tqer.created_on,
                tqer.formatted_response,
                tqer.coded_response,
                tqer.score,
                tqer.weight AS score_max,
                tqer.is_item_score_exceptions,
                tqer.score_override,
                tqer.is_from_admin AS tqer_is_from_admin,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                    tqer.item_id,
                    tqer.lang,
                    tqer.score,
                    tqer.weight,
                    tqer.formatted_response,
                    tqer.is_item_score_exceptions,
                    tqer.score_override,
                    tqer.is_from_admin
                    ORDER BY 
                    CASE WHEN tqer.coded_response IS NOT NULL THEN 0 ELSE 1 END,
                    tqer.created_on DESC
                ) AS rn
            FROM test_question_expected_responses tqer
            WHERE tqer.item_id IN (:item_ids)
                AND tqer.is_revoked = 0
        )

        SELECT
            tqer_id,
            item_id,
            lang,
            created_by_uid,
            created_on,
            formatted_response,
            coded_response,
            CASE 
            WHEN is_item_score_exceptions = 1 THEN score_override
            ELSE score
            END AS score,
            score_max,
            is_item_score_exceptions,
            score AS score_pre_override,
            tqer_is_from_admin
        FROM ranked_responses
        WHERE rn = 1;
    `
}
