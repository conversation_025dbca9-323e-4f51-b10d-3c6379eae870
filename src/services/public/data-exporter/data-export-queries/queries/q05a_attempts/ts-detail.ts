import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_assessments: boolean,
}

export const SQL_05A_ATTEMPTS_TS_DETAIL:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:IQueryConfig) => `
      select * from (
        select ts.id ts_id 
             , s.name s_name
             , s.foreign_id s_code
             , sc.name sc_name
             , scts.slug assessment_code
             , ts.date_time_start 
             , ts.is_closed
             , count(distinct ta.uid) num_students
             , count(distinct case when ta.started_on is not null then ta.uid else null end) num_students_started
             , count(distinct case when (ta.started_on is not null and ta.is_closed = 1) then ta.uid else null end) num_students_submitted
             , ur.uid teacher_uid
             , a.email teacher_email
        from school_class_test_sessions scts  
        join school_classes sc 
          on sc.id = scts.school_class_id 
        join test_sessions ts 
          on ts.id = scts.test_session_id 
          and ts.is_cancelled = 0 
          and ts.test_window_id in (:tw_ids)
        left join test_attempts ta 
          on ta.test_session_id = ts.id 
          and ta.is_invalid = 0
        left join user_roles ur 
          on ur.group_id = sc.group_id 
          and ur.role_type = 'schl_teacher'
          and ur.is_revoked = 0
        left join auths a 
          on a.uid = ur.uid
        join schools s 
          on s.group_id  = sc.schl_group_id 
          and s.is_not_reported  = 0
        join school_districts sd 
          on sd.group_id = s.schl_dist_group_id
        join test_window_td_alloc_rules twtar 
            on twtar.type_slug = scts.slug 
            and twtar.test_window_id = ts.test_window_id
        where sd.is_sample = 0  
            ${ config.include_sample_assessments ? '' : 'and twtar.is_sample = 0' }
        group by ts.id
        order by ts.date_time_start  desc 
      ) t
      where not (is_closed = 1 and num_students_started = 0)
    `
}