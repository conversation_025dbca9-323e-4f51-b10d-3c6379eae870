import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    include_sample_assessments: boolean,
    include_not_reported_schools: boolean,
}

export const SQL_05A_ATTEMPTS_DETAIL_FROM_TA_ID:IExportQueryDef = {
    requiredInputs: [
        'ta_ids',
    ],
    optionalInputs: [
      'include_not_reported_schools', // config only
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */ s.name s_name
            , s.foreign_id s_code
            , sd.foreign_id sd_code
            , sd.name sd_name
            , sc.name sc_name
            , sc.id sc_id
            , scts.slug assessment_code
            , ts.id ts_id
            , ta.uid student_uid
            , ta.id attempt_id
            , twtar.form_code
            , um_sin.value student_gov_id
            , u.first_name student_fname
            , u.last_name student_lname
            , u.last_name student_lname
            , (UNIX_TIMESTAMP(max(case when taqr.is_not_seen = 0 then taqr.updated_on end)) - UNIX_TIMESTAMP(min(case when taqr.is_not_seen = 0 then taqr.created_on end)))/60 time_spent_min
            , count(distinct taqr.id) num_screens_accessed
            , round(SUM(taqr.score), 2) machine_score
            , SUM(taqr.weight) machine_weight
            , max(taqr.updated_on) last_touch_on
            , ta.started_on
            , DATE_FORMAT(ta.started_on, '%Y-%m-%d') started_on_date
            , case when ta.started_on is not null then 1 else 0 end is_started
            , ta.is_submitted
            , DATE_FORMAT(ta.closed_on, '%Y-%m-%d') closed_on_date
        from school_class_test_sessions scts  
        join school_classes sc 
            on sc.id = scts.school_class_id 
        join test_sessions ts 
            on ts.id = scts.test_session_id 
            and ts.is_cancelled = 0 
        join test_attempts ta  
            on ta.test_session_id = ts.id  
            and ta.is_invalid = 0
        left join user_metas um_sin 
            on um_sin.uid = ta.uid 
            and um_sin.key = 'StudentIdentificationNumber' -- todo:WHITELABEL
        join test_window_td_alloc_rules twtar 
            on twtar.id = ta.twtdar_id 
        join test_attempt_question_responses taqr 
            on taqr.test_attempt_id = ta.id 
        join schools s 
            on s.group_id  = sc.schl_group_id 
            and s.is_sandbox = 0
             ${ config.include_not_reported_schools ? '' : 'and s.is_not_reported = 0'}
        join users u on u.id = ta.uid 
        join school_districts sd 
            on sd.group_id = s.schl_dist_group_id
        where ta.id IN (:ta_ids)
        group by ta.id
        order by taqr.updated_on desc 
        limit 500000
    `
}