import { IExportQueryDef } from "../../types/type";

export const SQL_04B_STUDENT_EXCEPTIONS: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
        select tes.id
            , tes.test_window_id
            , tes.uid
            , tes.twtar_type_slug
            , tes.school_group_id
            , tes.twtdar_id
            , tes.school_class_id
            , tes.category
            , tes.action_config
            , tes.is_pended
            , tes.test_attempt_id
        from tw_exceptions_students tes
        where tes.test_window_id in (:tw_ids)
            and tes.is_revoked = 0
            and tes.category in (:categories)
    `
}
