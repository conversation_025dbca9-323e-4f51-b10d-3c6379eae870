import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  include_sample_school_districts: boolean,
}

export const SQL_04A_GROUPS_SCHOOL_ADMINS:IExportQueryDef = {
    requiredInputs: [
        'include_sample_school_districts'
    ],
    queryGen: (config:IQueryConfig) => `
        select s.name s_name
            , s.foreign_id s_code
            , u.first_name
            , u.last_name
            , u.contact_email
            , u.is_claimed
            , ur.created_on
        from  schools s
        join school_districts sd
          on sd.group_id = s.schl_dist_group_id
        left join user_roles ur
          on s.group_id  = ur.group_id
          and ur.role_type = 'schl_admin'
          and ur.is_revoked = 0
        left join users u
          on u.id = ur.uid
          and u.contact_email not like '%@vretta.com'
        where s.is_not_reported = 0
          and s.is_sandbox  = 0
          ${ !config.include_sample_school_districts ? ' and sd.is_sample = 0' : ''}
        order by s.name
              , u.is_claimed desc
              , u.first_name
              , u.last_name  desc
        limit 1000
    `
}