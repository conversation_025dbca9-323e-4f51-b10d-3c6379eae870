import { IExportQueryDef } from "../../types/type";

export const SQL_06_MARKING_WINDOW_LOCAL_MARKING_MISSING_SCORE: IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:{}) => `
        select mw.id mw_id
        , tw.id tw_id
        , tw.title test_window
        , mtc.ta_id
        , mcbr.tls_id
        , mcbr.is_prorated
        , mcb.uid marker_uid
        , u.contact_email
        , sc.name class_name
        , ta.uid student_uid
        from test_windows tw 
        join marking_window_test_window mwtw 
        on tw.id = mwtw.test_window_id 
        join marking_windows mw
        on mw.id = mwtw.marking_window_id 
        join marking_window_items mwi 
        on mwi.marking_window_id = mw.id 
        join marking_claimed_batch_responses mcbr 
        on mcbr.window_item_id = mwi.id
        join marking_taqr_cache mtc 
            on mtc.taqr_id = mcbr.taqr_id 
            and mtc.marking_window_id = mw.id
        join marking_claimed_batches mcb 
            on mcb.id = mcbr.claimed_batch_id 
        join users u 
            on u.id = mcb.uid
        join test_attempts ta 
            on ta.id = mtc.ta_id 
        join school_class_test_sessions scts 
            on scts.test_session_id = ta.test_session_id 
        join school_classes sc 
            on sc.id = scts.school_class_id 
        where tw.id  in (:tw_ids) 
            and mcbr.tls_id is not null
            and mcbr.is_revoked = 1
        group by mtc.ta_id;
    `
}
