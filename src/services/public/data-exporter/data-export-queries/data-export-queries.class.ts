import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { ExporterQueryRef } from './queries/_index';
import {
  dataExportApiServices,
  dataExportApiMethodServices,
  dataExportQueryServices,
  dataExportTransformServices, IServiceTransformJoin
} from './services/_index';

interface Data {}

interface ServiceOptions {}

interface IQueryConfig {
  serviceName: string,
  queryName: string,
  props: any,
  chunkedParam?: string,
  chunkSize?: number,
  makeDistinct?: boolean,
  categories?: string[]
}

interface IApiConfig {
  serviceName: string,
  endpoint: string,
  method: string,
  props?: any,
  data: any,
  chunkedParam?: string,
  chunkSize?: number,
}

interface IApiMethodConfig {
  serviceName: string,
  endpoint: string,
  slug?: string,
  method: string,
  props?: any,
  data: any,
  chunkedParam?: string,
  chunkSize?: number,
}

interface ITransformJoinConfig {
  kind: "join",
  config: IServiceTransformJoin,
}

type ITransformConfig = ITransformJoinConfig;


interface IExtQueryConfig extends IQueryConfig {
  kind: "query",
}

interface IExtApiConfig extends IApiConfig {
  kind: "api-request"
}

type IExtConfig = IExtQueryConfig | IExtApiConfig;

export class DataExportQueries implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const tdata = <IExtConfig> data;
    switch (tdata.kind) {
      case "query":
        return this.runQuery(tdata)
      case "api-request":
        return this.runApiRequest(tdata)
      default:
        throw new Errors.BadRequest("invalid query kind");
    }
  }

  async runQuery(config: IQueryConfig){
    const queryGenerator = ExporterQueryRef[config.queryName]
    const service = dataExportQueryServices[config.serviceName]
    const query = queryGenerator.queryGen({... config.props})

    const records = await service(this.app, { // some props being passed through may not be relevant to all services
      ... config,
      query,
    })

    return records;
  }

  async runApiRequest(config: IApiConfig) {
    const service = dataExportApiServices[config.serviceName];
    const records = await service(this.app, config)

    return records;
  }

  async runApiMethod(config: IApiMethodConfig) {
    const service = dataExportApiMethodServices[config.serviceName];
    const records = await service(this.app, config)

    return records;
  }

  async runTransform(transformConfig: ITransformConfig, sequenceData: any) {
    const service = dataExportTransformServices[transformConfig.kind];
    if (!service) {
        throw new Errors.Unprocessable(`Unrecognized transform: ${transformConfig.kind}`);
    }

    // TODO: may want to rename to "arguments"' so that it's clear what level of configuration we're in.
    const records = await service(this.app, transformConfig.config, sequenceData);
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
