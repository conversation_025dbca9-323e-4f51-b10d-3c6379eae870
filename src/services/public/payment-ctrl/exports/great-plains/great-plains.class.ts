import { Id, NullableId, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { GreatPlainsExportRow, PurchaseMethods } from '../../data/types';

interface ServiceOptions {}

interface ExportParams extends Params {
  query: {};
}

interface ExportConfig {
  purchase_methods: PurchaseMethods[];
  start_date: string;
  end_date: string;
}

interface GreatPlainsExport {
  dataRows: GreatPlainsExportRow[];
  dataColumns: ExportColumn[];
  config: ExportConfig;
}

interface ExportColumn {
  prop: string;
  caption: string;
}

const exportColumns: ExportColumn[] = [
  {prop: 'school_contact', caption: 'School Contact'},
  {prop: 'school_name', caption: 'School Name'},
  {prop: 'school_mident', caption: 'School Mident'},
  {prop: 'school_address', caption: 'Address1'},
  {prop: 'school_address2', caption: 'Address2'},
  {prop: 'school_city', caption: 'City'},
  {prop: 'school_state', caption: 'State'},
  {prop: 'school_zip', caption: 'ZipCode'},
  {prop: 'school_country', caption: 'Country'},
  {prop: 'school_phone', caption: 'Phone#1'},
  {prop: 'school_phone2', caption: 'Phone#2'},
  {prop: 'school_email', caption: 'Email'},

  {prop: 'student_attempts_purchased', caption: 'Student Attempts Purchased'},
  {prop: 'unit_cost', caption: 'UnitCost'},
  {prop: 'total_cost', caption: 'TotalCost'},
  {prop: 'invoice_number', caption: 'InvoiceNo'},
  {prop: 'purchased_on', caption: 'Purchased On'},
  {prop: 'assessment_name', caption: 'Assessment Name'},

  {prop: 'recovery_value', caption: 'Recovery Value'},
  {prop: 'purchase_method', caption: 'Purchase Method'},
  {prop: 'payment_status', caption: 'Payment Status'},
  {prop: 'transaction_number', caption: 'Transaction Number'},
  {prop: 'class_name', caption: 'Class/Group Name'},
  {prop: 'admin_window', caption: 'Administration Window'},
  {prop: 'used_students_attempts', caption: 'Used Students Attempts'},
]

export class GreatPlains implements ServiceMethods<any> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params: ExportParams) {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params: Params) {
    throw new Errors.MethodNotAllowed();
  }

  async create(exportConfig: ExportConfig, params: ExportParams): Promise<GreatPlainsExport> {
    const { purchase_methods, start_date, end_date } = exportConfig;

    if(!purchase_methods || purchase_methods.length === 0) {
      throw new Errors.BadRequest('Purchase methods must be specified');
    }

    if(!start_date || !end_date) {
      throw new Errors.BadRequest('Start and end dates must be specified');
    }

    const knex = this.app.service('db/read/test-session-purchases').knex
    const dataRows: GreatPlainsExportRow[] = await knex('test_session_purchases as tsp').select({
        school_contact: knex.raw('CONCAT(u.first_name, " ", u.last_name)'),
        school_name: 'schl.name',
        school_mident: 'schl.foreign_id',
        school_address: 'schl.address',
        school_address2: 'schl.address2',
        school_city: 'schl.city',
        school_state: 'schl.province',
        school_country: 'schl.country',
        school_zip: 'schl.postal_code',
        school_phone: 'schl.phone_number',
        school_phone2: 'schl.fax_number',
        school_email: 'u.contact_email',

        student_attempts_purchased: 'tsp.num_student_attempts_purchased',
        unit_cost: 'ap.price_per_student',
        total_cost: 'tsp.purchased_total',
        invoice_number: knex.raw('IFNULL(tsp.invoice_id, CONCAT(YEAR(tsp.ordered_on), "-", tsp.id))'),
        purchased_on: knex.raw('DATE(tsp.approved_on)'),
        assessment_name: 'sc.group_type',

        recovery_value: 'tsp.refuneded_total',
        purchase_method: 'tsp.purchase_method_id',
        payment_status: 'aps.status',
        transaction_number: knex.raw('CASE WHEN tsp.purchase_method_id = 1 THEN tsp.stripe_id ELSE tsp.purchase_trans_id END'),
        class_name: 'sc.name',
        admin_window: 'tsp.test_window_id',
        used_students_attempts: knex.raw('count(sap.id)'),
      })
      .join('alternative_payment_status as aps', 'aps.id', 'tsp.alternative_status')
      .join('school_classes as sc', 'sc.group_id', 'tsp.class_group_id')
      .join('schools as schl', 'schl.group_id', 'tsp.schl_group_id')
      .leftJoin('student_attempt_purchases as sap', function() {
        this.on('sap.ts_purchase_id', 'tsp.id')
          .on('sap.is_revoked', '!=', knex.raw(1))
          .onNotNull('sap.assigned_ta_id')
          .onNotNull('sap.used_on')
      })
      .joinRaw('join assessment_prices as ap on ap.test_window_id = tsp.test_window_id and ap.is_revoked = 0')
      .leftJoin('users as u', 'u.id', 'tsp.purchase_by_uid')
      .leftJoin('users as u2', 'u2.id', 'tsp.refunded_by_uid')
      .where('tsp.is_revoked', '!=', knex.raw(1))
      .whereIn('tsp.purchase_method_id', purchase_methods)
      .where('tsp.ordered_on', '>=', start_date)
      .where('tsp.ordered_on', '<=', end_date)
      .groupBy('tsp.id')
      .orderBy('tsp.id', 'desc');


    return {
      dataRows,
      dataColumns: exportColumns,
      config: exportConfig,
    };
  }

  async update(id: NullableId, data: any, params: Params) {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: any, params: Params) {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params: Params) {
    throw new Errors.MethodNotAllowed();
  }
}
