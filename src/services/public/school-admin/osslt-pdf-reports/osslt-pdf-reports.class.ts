import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { getSysConstString } from '../../../../util/sys-const-string';
import { maxReportGenerateMinutes } from '../../school-admin/pj-reports/pj-reports.class';
import { dbRawReadReporting, dbRawWrite } from '../../../../util/db-raw';
import axios, { AxiosInstance } from 'axios';
import { ENTRY_UR } from '../../dist-admin/osslt-report/osslt-report.class';
import _ from 'lodash';

interface Data {}

interface ServiceOptions {}

export enum ISR_FILE_TYPES {
  SCHOOL_ISR_PDF = 'SCHOOL_ISR_PDF',
  SINGLE_ISR_PDF = 'SINGLE_ISR_PDF',
  SINGLE_ISR_PDF_IMG = 'SINGLE_ISR_PDF_IMG',
}

export class OssltPdfReports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;
  
  // setup files needed for ISR and its associate s3 folder name
  ISR_FILE_TYPES_S3_FOLDER: Record<ISR_FILE_TYPES, string> = {
    [ISR_FILE_TYPES.SCHOOL_ISR_PDF]: 'osslt_isrs',
    [ISR_FILE_TYPES.SINGLE_ISR_PDF]: 'osslt_single_isr',
    [ISR_FILE_TYPES.SINGLE_ISR_PDF_IMG]: 'osslt_single_isr_img'
  };


  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = app.get('eqaoScanningService');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  /**
   * Get Student OSSLT PDF Image
   * @param params 
   * @returns 
   */
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { schl_group_id, testWindowId, clientDomain, bypassAdminSignOff, isSecreteUser, getAdminISR, isVersion2, singleStudentUid} = (<any>params).query;

    const uid = await currentUid(this.app, params);
    const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord
    const is_get_admin_isr = getAdminISR ? JSON.parse(getAdminISR) : false
    const csv_ver2 = isVersion2 ? JSON.parse(isVersion2) : false
    
    //get studentData
    const studentData = await this.app.service('public/dist-admin/osslt-report').gerReport(
      ENTRY_UR.SCHL_ADMIN, 
      schl_group_id, 
      testWindowId, 
      isBypassDomain, 
      bypassAdminSignOff, 
      isValidSecreteUser, 
      singleStudentUid, 
      is_get_admin_isr, 
      csv_ver2
    );

    const bulk_student_results_reports = await this.getBulkStudentResultsReportsGeneratingRecords(schl_group_id, testWindowId)

    // Attach isr image path to each student in studentData
    // And fetch distinct test window into distinctTestWindows array
    if(bulk_student_results_reports.length){
      const report_gen_on = bulk_student_results_reports[0].report_generate_on
      studentData.forEach((sd:any) => {
        const timestamp = this.extraTimeStampFromLink(bulk_student_results_reports[0].s3_report_link);
        const single_report_file_path =  this.getISRFilePath(ISR_FILE_TYPES.SINGLE_ISR_PDF_IMG, testWindowId, +timestamp, sd.uid)
        sd.image_url = generateS3DownloadUrl(single_report_file_path);
        sd.report_gen_on = report_gen_on
        sd.test_window = testWindowId
      })

      //LogSchoolAdminAccess OSSLT Report Record when there's report
      await this.app.service("public/school-admin/reports").logUserAccessRecord(testWindowId, uid, schl_group_id, params)
    }
    return studentData;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // Get student OSSLT ISR PDF for school in test window
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {
      schl_group_id,
      tw_id,
      assessment_slug,
      bypassAdminSignOff,
      singleStudentUid,
      clientDomain,
      isSecreteUser,
      getAdminISR,
      isVersion2
    } = params.query;

    const created_by_uid = await currentUid(this.app, params);
    const maxReportGenerateMS = maxReportGenerateMinutes*60000;
    const bulk_student_results_reports = await this.getBulkStudentResultsReportsGeneratingRecords(schl_group_id, tw_id)
    const generating_reports = bulk_student_results_reports.filter( bsrrg => bsrrg.report_generate_on == null && (new Date()).getTime() - (new Date(bsrrg.created_on)).getTime() < maxReportGenerateMS)
    const long_generate_reports = bulk_student_results_reports.filter( bsrrg => bsrrg.report_generate_on == null && (new Date()).getTime() - (new Date(bsrrg.created_on)).getTime() > maxReportGenerateMS)
    const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord
    const is_get_admin_isr = getAdminISR ? JSON.parse(getAdminISR) : false
    const csv_ver2 = isVersion2 ? JSON.parse(isVersion2) : false

    //revoke long_gereate_reports
    const long_generate_report_ids = long_generate_reports.map(lgr => {return lgr.id})
    await this.revokebulkdStudentResultReport(created_by_uid, long_generate_report_ids)

    //regenerate report
    if(long_generate_reports.length > 0){
      const is_regenerate = long_generate_reports.length > 0
      return this.generateReport(ENTRY_UR.SCHL_ADMIN, created_by_uid, schl_group_id, tw_id, assessment_slug, is_regenerate, bypassAdminSignOff, isBypassDomain, isValidSecreteUser, is_get_admin_isr, csv_ver2)
    }

    //throw error if reports are generating
    if(bulk_student_results_reports.length > 0 && long_generate_reports.length == 0 && generating_reports.length > 0){
      throw new Errors.BadRequest("REPORT_GENERATING");
    }

    // get PDF path on S3
    if(bulk_student_results_reports.length>0 && bulk_student_results_reports[0].report_generate_on != null){
      if(!singleStudentUid) {
        return {message: 'REPORT_GENERATED', reportURL:generateS3DownloadUrl( bulk_student_results_reports[0].s3_report_link)}
      } else {
        const timestamp = this.extraTimeStampFromLink(bulk_student_results_reports[0].s3_report_link);
        const single_report_file_path =  this.getISRFilePath(ISR_FILE_TYPES.SINGLE_ISR_PDF, tw_id, +timestamp, singleStudentUid)
        return {message: 'INDIVIDUAL_REPORT_GENERATED', reportURL:generateS3DownloadUrl(single_report_file_path)}
      }
    }
    throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  // Generate student OSSLT ISR PDF for school in test window for school admin
  async create (data: Data, params?: Params): Promise<Data> {
    return this.createOSSLTPDFGeneration(ENTRY_UR.SCHL_ADMIN, data, params)
  }

  // Generate student OSSLT ISR PDF for school in test window for school admin/Issue Reivewer
  async createOSSLTPDFGeneration(entry:ENTRY_UR, data: Data, params?: Params, callBack = (err:any, result:any)=>{}){
    if(!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {
      schl_group_id,
      tw_id,
      assessment_slug,
      bypassAdminSignOff,
      clientDomain,
      isSecreteUser,
      getAdminISR,
      isVersion2,
      new_bulk_isr_pdf_generations_process_id
    } = params.query;

    const created_by_uid = await currentUid(this.app, params);
    const maxReportGenerateMS = maxReportGenerateMinutes*60000;
    const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord
    const is_get_admin_isr = getAdminISR ? JSON.parse(getAdminISR) : false
    const csv_ver2 = isVersion2 ? JSON.parse(isVersion2) : false

    //get curernt pdf generation status
    const bulk_student_results_reports_generating = await this.getOngoingBulkStudentResultsReportsGeneratingRecords(schl_group_id, tw_id)

    //revoke long generate_reports
    const long_generate_reports = bulk_student_results_reports_generating.filter( bsrrg => new Date().getTime() - (new Date(bsrrg.created_on)).getTime() > maxReportGenerateMS)
    const long_generate_report_ids = long_generate_reports.map(lgr => {return lgr.id})
    await this.revokebulkdStudentResultReport(created_by_uid, long_generate_report_ids)  

    //throw error if there currently reports generating
    if(bulk_student_results_reports_generating.length - long_generate_reports.length > 0){
      throw new Errors.BadRequest("REPORT_GENERATING");
    }

    const is_regenerate = long_generate_reports.length > 0

    return this.generateReport(entry, created_by_uid, schl_group_id, tw_id, assessment_slug, is_regenerate, bypassAdminSignOff, isBypassDomain, isValidSecreteUser, is_get_admin_isr, csv_ver2, callBack, new_bulk_isr_pdf_generations_process_id)
  }

  /**
   * Get School ISR PDF generation records in the test window
   */
  async getBulkStudentResultsReportsGeneratingRecords(schl_group_id: number, tw_id:number){
    const bulk_student_results_reports = await dbRawReadReporting(this.app, {schl_group_id, tw_id}, `
      -- 94ms
        select bsrr.id
            , bsrr.school_groud_id
            , bsrr.test_window_id
            , bsrr.created_on
            , bsrr.s3_report_link
            , bsrr.report_generate_on
            , bsrr.is_revoked
            , bsrr.bulk_isr_pdf_generation_id
         from bulk_student_results_reports bsrr
        where bsrr.school_groud_id = :schl_group_id
          and bsrr.test_window_id = :tw_id
          and bsrr.is_revoked != 1
     order by bsrr.report_generate_on DESC
    ;`)
    return bulk_student_results_reports
  }

  /**
   * Revoke Bulk dStudent Result Report
   */
  async revokebulkdStudentResultReport(created_by_uid:number, bsrr_ids:number[]){
    //Need bench mark
    if(bsrr_ids.length > 0){
      await dbRawWrite(this.app, [created_by_uid, bsrr_ids], `
        -- 544ms
        update bulk_student_results_reports bssr
           set bssr.is_revoked = 1
             , bssr.revoked_by_uid = ?
             , bssr.revoked_on = now()
         where bssr.id in (?) 
      ;`)
    }
  }

  /** 
   * Get ongoing school ISR PDF generation records in the test window
  */
  async getOngoingBulkStudentResultsReportsGeneratingRecords(schl_group_id: number, tw_id:number){
    const bulk_student_results_reports_generating = (await dbRawReadReporting(this.app, {schl_group_id, tw_id }, `
      -- 94ms 
      select bsrr.id
           , bsrr.created_on
        from bulk_student_results_reports bsrr
       where bsrr.school_groud_id = :schl_group_id
         and bsrr.test_window_id = :tw_id
         and bsrr.report_generate_on is null
         and bsrr.is_revoked != 1
    ;`))
    return bulk_student_results_reports_generating
  }

  /**
   * Run the createReportPDF synchronize.
   * @param data 
   * @param params 
   * @returns 
   */
  async createOSSLTPDFGenerationSync(entry:ENTRY_UR, data:any, params:any){
    return new Promise((resolve, reject) => {
      this.createOSSLTPDFGeneration(entry, data, params, (err:any, result:any) => {
        if (err) {
          reject(err);
        } else {
          resolve(result);
        }
      })
      .catch(reject)
    });
  }

  //Generate OSSLT PDF report
  async generateReport(entry:ENTRY_UR, created_by_uid:any, schl_group_id:any, tw_id:any, assessment_slug:any, is_regenerate:any, bypassAdminSignOff:any, isBypassDomain:boolean, isValidSecreteUser:boolean, is_get_admin_isr:boolean, csv_ver2:boolean, callBack = (err:any, result:any) => {}, new_bulk_isr_pdf_generations_process_id = 0){
    const singleStudentUid = undefined; // optional in function generateStudentData()

    // get studentData
    let studentData = await this.app.service('public/dist-admin/osslt-report').gerReport(
      entry, 
      schl_group_id, 
      tw_id, 
      isBypassDomain, 
      bypassAdminSignOff, 
      isValidSecreteUser, 
      singleStudentUid, 
      is_get_admin_isr, 
      csv_ver2
    );

    //handle studentData length is 0
    if(studentData.length < 1){
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }
    
    studentData = _.orderBy(studentData, ['LastName', 'FirstName', 'StudentOEN'], ['asc', 'asc', 'asc'])

    const school_mident = studentData[0].SchMident
    const school_lang = studentData[0].school_lang
    const timestamp:number = Date.now();
    const bulk_isr_pdf_generation_id = new_bulk_isr_pdf_generations_process_id > 0 ? new_bulk_isr_pdf_generations_process_id: null
    const not_applicable_test_private_schL_board = await this.app.service('public/translation').getOneBySlug("lbl_not_applicable_2", school_lang) 
    const genericSchoolName = await this.app.service('public/translation').getOneBySlug("lbl_report_secondary_school", school_lang)

    for(let stu_data of studentData){
      stu_data.sch_year = await this.getReportYear(stu_data.tw_date_end, school_lang);
      stu_data.stu_name = this.toUpperCaseWithoutAffectingSpecialChars(`${stu_data.FirstName} ${stu_data.LastName}`, school_lang ),
      stu_data.stu_oen =  `${this.maskOENWithFiveStars(stu_data.StudentOEN)}`,
      stu_data.school =  stu_data.is_section23 ? genericSchoolName : `${stu_data.SchName} (${stu_data.SchMident})`
      stu_data.sch_board = +stu_data.is_private_schl==1 ? `${not_applicable_test_private_schL_board}`:`${stu_data.school_board_name}`
      stu_data.sch_midant = `${stu_data.SchMident}`
      stu_data.lang = stu_data.school_lang
      stu_data.test_window = `${tw_id}`
      stu_data.uid = `${stu_data.uid}`
      stu_data.timestamp = `${timestamp}`

      //Apply NPO text
      const getNPOOutcomeText = await this.getNPOOutcomeText(+stu_data.Result, school_lang)
      if(getNPOOutcomeText && getNPOOutcomeText.length > 0){
        stu_data.getNPOOutcomeText = getNPOOutcomeText
      }

      //Apply Score
      //only show scale score if the result is 1 (Success) or 2(not yet successs)
      if(+stu_data.Result === 1){
        stu_data.score_success = stu_data.OSSLTScaleScore
      }
      if(+stu_data.Result === 2){
        stu_data.score_NYS = stu_data.OSSLTScaleScore
      }

      //Apply NPO text
      const attemptedQuestionTextAndVariableName = await this.getAttemptedQuestionTextAndVariableName(+stu_data.Result, +stu_data.questions_answered,+stu_data.total_questions, school_lang)
      if(attemptedQuestionTextAndVariableName){
        stu_data[attemptedQuestionTextAndVariableName.resultVariable] = attemptedQuestionTextAndVariableName.resultText
      }
    }

    //save bulkpdflink to db table
    const bulk_student_results_reports:any = await dbRawWrite(this.app, [schl_group_id, tw_id, created_by_uid, bulk_isr_pdf_generation_id], `
      -- 94ms
      insert into bulk_student_results_reports(school_groud_id,test_window_id,created_by_uid, bulk_isr_pdf_generation_id) values (?, ?, ?, ?)
    ;`)
    
    const new_bulk_student_results_report_id = ''+bulk_student_results_reports.insertId
    //start generate report DPF        
    this.generatePdf(studentData, new_bulk_student_results_report_id , timestamp)
      .then(async res => {
        // update s3_report_link, report_generate_on in bulk_student_results_reports table
        const s3_report_link = this.getISRFilePath(ISR_FILE_TYPES.SCHOOL_ISR_PDF, tw_id, timestamp, school_mident)
        await dbRawWrite(this.app, [s3_report_link, new_bulk_student_results_report_id], `
          -- 94ms
          update bulk_student_results_reports bssr
            set bssr.s3_report_link = ?
              , bssr.report_generate_on = now()
          where bssr.id = ?
        ;`)
        const error = null  
        callBack(error, res)
      })
      .catch( (error)=> {
        const res = null  
        callBack(error, res)
      });

    return is_regenerate? {message: 'REPORT_REGENERATING', bsrr_id: bulk_student_results_reports.id }: {message: 'REPORT_GENERATING', bsrr_id: bulk_student_results_reports.id}
  }

  //get the S3 file path
  getISRFilePath(file_type:ISR_FILE_TYPES, tw_id:number, timestamp:number, entity_id:number){
    const s3_file_path = [
      this.ISR_FILE_TYPES_S3_FOLDER[file_type],
      `tw_${tw_id}`,
      `timestamp_${timestamp}`,
    ].join('/');

    let s3_file_name
    switch(file_type){
      case ISR_FILE_TYPES.SINGLE_ISR_PDF_IMG:
        s3_file_name = `/uid_${entity_id}.jpeg`;
        break
      case ISR_FILE_TYPES.SINGLE_ISR_PDF:  
        s3_file_name = `/uid_${entity_id}.pdf`;
        break
      case ISR_FILE_TYPES.SCHOOL_ISR_PDF:
      default:
        s3_file_name = `/mident_${entity_id}.pdf`;
        break
    }

    const fullPath = s3_file_path + s3_file_name
    return fullPath
  }

  //get the timestamp from the link
  extraTimeStampFromLink(s3_path_link: string){
    // S3 link sample : osslt_isrs/tw_126/timestamp_1731525137909/mident_2020202222.pdf
    // Split S3 link by '/'
    const pathSegments = s3_path_link.split('/');

    // The timestampString is in the third segment (index 2)
    const timestampString = pathSegments[2]

    // split timestampString by '_'
    const timestampSegments = timestampString.split('_')
    
    //The timestamp is the decond segment (index 1)
    const timestamp = timestampSegments[1]
    return timestamp
  }

  // Call Generate PDF service and start generate the PDF
  async generatePdf(studentData:any, bsrr_id:any, timestamp:number){
    const isStressTest = !!this.app.get('isStressTest')  
    const use_testing_s3_folder = isStressTest
    const res = await this.eqaoScanningService.post(`/generate_osslt_isr`, {studentData, bsrr_id, use_testing_s3_folder, timestamp});
    return res
  }

  /**
   * Get appropriate text according to report.Result value
   * For outcome 1 and 2: No text is needed
   * For outcome 3 - 11: The text is stored in translation
   */
  async getNPOOutcomeText(result_state:number, lang:string){
    const translation = this.app.service('public/translation');
    let resultText = ''
    switch(+result_state){ 
      case 3:
        resultText = await translation.getOneBySlug("sa_report_npo_3", lang)
        break;
      case 4:
        resultText = await translation.getOneBySlug("sa_report_npo_4", lang)
        break;
      case 5:
        resultText = await translation.getOneBySlug("sa_report_npo_5", lang)
        break;
      case 6:
        resultText = await translation.getOneBySlug("sa_report_npo_6", lang)
        break;
      case 10:
        resultText = await translation.getOneBySlug("sa_report_npo_10", lang)
        break;
      case 11:
        resultText = await translation.getOneBySlug("sa_report_npo_11", lang)
        break;
      default:
        resultText = "" 
        break
    }
    
    return resultText
  }

  /**
   * Get appropriate text for attempted questions and the variable used in scan service(different variable is used to control the position of the attempted questions in PDF)
   * Only show attempted question count if the result is 1 (Success) or 2(not yet successs) or 11(not sufficient evidence)
   * Other show not applicable
   */
  async getAttemptedQuestionTextAndVariableName(result_state:number, attempted_question_count:number, total_question_count:number, lang:string){
    const translation = this.app.service('public/translation');
    let result = undefined
    const of_word_translation = await translation.getOneBySlug("sa_report_unsuccessful_attempts_of", lang)
    switch(+result_state){ 
      case 1: // Success
        result = {
          resultVariable:  'attemptedQuestionTextSuccess',
          resultText: `${attempted_question_count}${of_word_translation} ${total_question_count}`
        }
        break;
      case 2: // Not Yet Success
        result = {
          resultVariable: 'attemptedQuestionTextNYS',
          resultText: `${attempted_question_count}${of_word_translation} ${total_question_count}`
        }
        break;
      case 11:// Not sufficient evidence
        result = {
          resultVariable: 'attemptedQuestionTextNPO',
          resultText: `${attempted_question_count}${of_word_translation} ${total_question_count}`
        }
        break;
      default: // NPO
        result = {
          resultVariable: 'attemptedQuestionTextNPO',
          resultText: await translation.getOneBySlug("lbl_not_applicable_2", lang) 
        }
        break
    }
    return result
  }

  /**
   * Mask the student OEN
   */
  maskOENWithFiveStars(oen:string){
    let oenArr = oen.split('');
    for(let i =1; i < oenArr.length-3; i++){
      oenArr[i] = "*";
    }
    return oenArr.join('')
  }

  /**
   * Get report year and season: "SPRING" or "FALL" to be displayed on ISR
   * @param date 
   * @returns {string} example "FALL 2024"
   */
  async getReportYear(date:Date, lang:string):Promise<string> {
    const translation = this.app.service('public/translation');
    const reportWrittenOn = date
    const written_on = new Date (reportWrittenOn)
    const writtenYear = written_on.getFullYear()
    const writtenMonth = written_on.getMonth() + 1
    const writtenSemester = writtenMonth < 7 ? await translation.getOneBySlug('lbl_osslt_report_spring', lang) : await translation.getOneBySlug('lbl_osslt_report_term_fall', lang)
    return writtenSemester + ' ' + writtenYear;
  }

  /**
   * Convert to uppercase considering French locale, but keep special characters unchanged
   */
  toUpperCaseWithoutAffectingSpecialChars(nameString:string, lang:string){
    const newNameString = nameString.split('').map(char => {
      // Check if the character is alphabetic before applying the uppercase transformation
      return char.match(/[a-zA-Z\u00C0-\u00FF]/) ? char.toLocaleUpperCase(lang) : char;
    })
    .join('');
    return newNameString
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
