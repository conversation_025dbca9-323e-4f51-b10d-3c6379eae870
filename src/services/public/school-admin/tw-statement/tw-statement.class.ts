import { dbRawRead, dbRawReadReporting } from './../../../../util/db-raw';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { TWUM_KEYS } from '../student-tw/util/util.class';
import { dbDateNow } from '../../../../util/db-dates';
import { currentUid } from '../../../../util/uid';
import logger from '../../../../logger';
import { TW_STATEMENT_ASSESSMENT_COMPONENT_RECORD, TW_STATEMENT_REPORTED_ISSUE, TW_STATEMENT_STUDENT_ASMT_REG_STATUS, TW_STATEMENT_STUDENT_TW_META, TW_STUDENTS_ASMT_STATUS } from '../../../../sql/tw-statement';

const IS_AUTO_CLEAN = true;
const IS_DEBUG_OVERVIEW = false;

interface Data {}

interface ServiceOptions {}

interface IConfig {
  test_window_id:number, 
  schl_group_id?:number, 
  isProvince?:boolean
}

interface IAsmtComponent {
  id:number,
  asmt_type_slug:string, 
  caption_short:string, 
  meta:string, 
  isFrOnly?:boolean,// todo:DB_DATA_MODEL 
}

interface IStudent {
  uid:number,
  first_name? :string,
  last_name? :string,
  lang :string,
  dob?:string,
  student_gov_id:string,
  s_code?:string,
  asmt_status?: {
    [asmt_type_slug:string]: {
      is_expected: boolean,
      is_in_ts: boolean,
      is_submitted: boolean,
      is_absent: boolean,
      is_unexpected_submission?: boolean,
      report_num: number
    }
  }
}

// interface IPrincKitMeta {
//   [asmt_type_slug:string]: {
//       overall_issues_no: boolean,
//       overall_issues_yes: boolean,
//       is_submitted: boolean,
//       overall_issues_notes: string,
//       [student_uid:string]: {
//           is_absent: boolean,
//           is_excused: boolean,
//           is_other_class: boolean,
//           is_anomaly: boolean,
//           notes: string,
//       }
//   } 
// }

export class TwStatement implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query) {
      throw new Errors.BadRequest("REQ_PARAMS_MISSING")
    }
    const { action, uid, test_window_id, asmt_type_slug } = params.query;
    if(action == 'reported-issue'){
      const issues = await dbRawRead(this.app, {uid, test_window_id, asmt_type_slug}, TW_STATEMENT_REPORTED_ISSUE);
      return issues;
    }
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const test_window_id = +id;
    const schl_group_id = params?.query?.schl_group_id;
    return this.getTwStatement({test_window_id, schl_group_id});
  }

  async getTwStatementAllDetails(test_window_id:number){
    const twStatement = await this.getTwStatement({test_window_id, isProvince: true})
    for (let student of twStatement.students){
      let isAnyStatus = false
      for (let components of twStatement.asmtComponents){
        const asmt_status:any = student.asmt_status ? student.asmt_status[components.asmt_type_slug] : {};
        let status = 'NOT REGISTERED';
        if (asmt_status.is_absent){
          status = 'ABSENT';
          isAnyStatus = true
        }
        else if (asmt_status.is_submitted){
          status = 'SUBMITTED'
          isAnyStatus = true
        }
        else if (asmt_status.is_expected){
          status = 'REGISTERED'
          isAnyStatus = true
        }
        (<any> student)[components.asmt_type_slug] = status; // todo: use a different object... this is overloading and making messy this object
      }
      (<any> student).isAnyStatus = isAnyStatus
    }
    return twStatement.students.filter(student => (<any> student).isAnyStatus)
  }

  async getFlaggedCaseRecords(test_window_id:number, isUidSpecific:boolean, uids?:number[]){
    return await dbRawReadReporting(this.app, {uids, test_window_id}, `
        select uid, twtar_type_slug 
        from tw_exceptions_students tes 
        where tes.category = 'LOS_CONFIRM'
          and tes.is_revoked = 0
          and tes.test_window_id = :test_window_id
          ${ isUidSpecific ? `and tes.uid in (:uids)` : ''}
      `)
  }

  async getTwStatement(options:IConfig){

    const {test_window_id, schl_group_id} = options;
    const queryProps = {test_window_id, schl_group_id}

    const assessmentComponentsRecords:IAsmtComponent[]  = await dbRawRead(this.app, queryProps, TW_STATEMENT_ASSESSMENT_COMPONENT_RECORD)
    const asmtComponentBySlug = new Map()
    const asmtComponents:IAsmtComponent[] = [];
    for (let record of assessmentComponentsRecords){
      const asmtComponent: IAsmtComponent = {
        ... record,
        caption_short: (record.caption_short || '').replace('\\n', '\n'),
        meta: undefined,
        ... JSON.parse(record.meta || '{}')
      }
      asmtComponentBySlug.set(asmtComponent.asmt_type_slug, asmtComponent)
      asmtComponents.push(asmtComponent)
    }

    const students = await this.loadStudents(options)

    const uids = students.map(student => student.uid)
    let flaggedCaseRecords = []
    if (uids.length > 0){
      flaggedCaseRecords = await this.getFlaggedCaseRecords(options.test_window_id, true, uids)
    }

    const studentRegMap:any = {}
    try {
      const uids = [... new Set(students.map(r => r.uid))]// todo: risky as the list might be long, but we are fine with 5000 or less
      const student_asmt_reg_status = await dbRawRead(this.app, {uids, test_window_id}, TW_STATEMENT_STUDENT_ASMT_REG_STATUS)
      for (let record of student_asmt_reg_status){
        studentRegMap[record.uid+';'+record.type_slug] = true
      }
    }
    catch (e) {}

    const studentByUid:Map<string, IStudent> = new Map()
    for (let student of students){
      studentByUid.set(`${student.uid}${student.s_code}`, student)
      student.asmt_status = {}
      for (let ac of asmtComponents){
        let is_expected = false;
        // if PAT todo:DB_DATA_MODEL !(ac.isFrOnly && student.lang !== 'fr'), // 
        // if DIP
        is_expected = studentRegMap[student.uid+';'+ac.asmt_type_slug]
        student.asmt_status[ac.asmt_type_slug] = {
          is_expected,
          is_in_ts: false,
          is_submitted: false,
          is_absent: false,
          report_num: 0,
        }
      }
    }

    const students_asmt_status:{
      uid:number,
      asmt_type_slug:string,
      is_in_ts:number,
      is_submitted:number,
      report_num: number,
      s_code?:string,
    }[] = await dbRawRead(this.app, queryProps, TW_STUDENTS_ASMT_STATUS(!!options.isProvince));
    
    for (let asmt_status of students_asmt_status){
      const student = studentByUid.get(`${asmt_status.uid}${asmt_status.s_code}`)
      if (student && student.asmt_status && student.asmt_status[asmt_status.asmt_type_slug]){ // should always be defined
        const status = student?.asmt_status[asmt_status.asmt_type_slug];
        status.is_in_ts = true;
        status.is_submitted = (asmt_status.is_submitted==1);
        status.report_num = asmt_status.report_num;
        if (status.is_submitted && !status.is_expected){
          status.is_unexpected_submission = true; 
        }
      }
    }

    const students_tw_meta:{
      uid:number,
      asmt_type_slug:string,
      key_namespace:string,
      key:string,
      value:string,
      s_code?: string,
    }[] =  await dbRawRead(this.app, queryProps, TW_STATEMENT_STUDENT_TW_META(options.isProvince));
    for (let tw_meta of students_tw_meta){
      const student = studentByUid.get(`${tw_meta.uid}${tw_meta.s_code}`)
      if (student && student.asmt_status && student.asmt_status[tw_meta.asmt_type_slug]){ // should always be defined
        const status = student?.asmt_status[tw_meta.asmt_type_slug];
        if (tw_meta.key === TWUM_KEYS.ABSENT){
          status.is_absent = (tw_meta.value=='1');
        }
      }
    }

    if (queryProps.schl_group_id){
      const schl_tw_sign_off = await this.getCleanedSchlTwSignOff(queryProps.test_window_id, queryProps.schl_group_id, options.isProvince)
      
      return {
        asmtComponents,
        students,
        schl_tw_sign_off,
        flaggedCaseRecords,
      }
    }
    else {
      throw new Errors.BadRequest('MISSING_SCHL_GROUP_ID')
    }

  }

  async getCleanedSchlTwSignOff(test_window_id:number, schl_group_id:number, isProvince?:boolean){
    let schl_tw_sign_off =  {meta:'{}'}
    const schl_tw_sign_offs:{id:number, meta:string | any}[] =  await dbRawRead(this.app, {test_window_id, schl_group_id}, `
      select stss.id
          , stss.meta 
      from school_tw_student_signoffs stss
      where stss.test_window_id = :test_window_id
        ${isProvince ? '' : 'and stss.schl_group_id = :schl_group_id'}
        and stss.is_revoked = 0
    `);
    if (!isProvince && schl_tw_sign_offs.length > 0){
      schl_tw_sign_off = await this.consolidateTwKits(schl_tw_sign_offs)
    }
    else {
      schl_tw_sign_off = schl_tw_sign_offs[0] || {meta:'{}'}
      schl_tw_sign_off.meta = JSON.parse(schl_tw_sign_off.meta || '{}')
    }
    return schl_tw_sign_off
  }

  async consolidateTwKits(schl_tw_sign_offs:{id:number, meta:string}[]){
    let records = []
    const recordToKeepId = schl_tw_sign_offs[0].id;
    const recordsToRevokeIds = []
    for (let record of schl_tw_sign_offs){
      try {
        const meta = JSON.parse(record.meta)
        if (record.id !== recordToKeepId){
          recordsToRevokeIds.push(record.id)
        }
        records.push(meta)
      }
      catch(e){}
    }

    const recordRef = records[0] || {}
    const recordRefCache = JSON.stringify(recordRef)

    const mergesMade:any[] = [];
    const mergeBooleanProps = ['overall_issues_no', 'overall_issues_yes', 'is_submitted', 'is_absent', 'is_excused', 'is_other_class', 'is_anomaly']
    const mergeStringProps = ['notes', 'overall_issues_notes']
    const mergeNumberProps = ['submitted_by']
    // if it was ever true, treat final save as true
    const mergeBooleans = (src:any, dest:any, asmt_type_slug?:string, studentUid?:string) => {
        for (let prop of mergeBooleanProps){
            if (src[prop] && !dest[prop]){
              mergesMade.push({type:'bool', src:src[prop], dest:dest[prop], prop, asmt_type_slug, studentUid, })
              dest[prop] = src[prop]
            }
        }
    }
    const stringContains = (haystack:string, needle:string) => {
      const parts = haystack.split(needle)
      if (parts.length <= 1){
        return false
      }
      else {
        return true
      }
    }
    const mergeStrings = (src:any, dest:any, asmt_type_slug?:string, studentUid?:string) => {
        for (let prop of mergeStringProps){ 
            if (src[prop]){
                if (!dest[prop]){
                  mergesMade.push({type:'str:missing', src:src[prop], dest:dest[prop], prop, asmt_type_slug, studentUid, })
                    dest[prop] = src[prop]
                }
                else if ( stringContains(dest[prop], src[prop]) ){
                    // do nothing
                }
                else if ( stringContains(src[prop], dest[prop]) ){
                  mergesMade.push({type:'str:subsumed', src:src[prop], dest:dest[prop], prop, asmt_type_slug, studentUid, })
                    dest[prop] = src[prop]
                }
                else {
                    mergesMade.push({type:'str:concat', src:src[prop], dest:dest[prop], prop, asmt_type_slug, studentUid, })
                    dest[prop] = dest[prop] + '\n\n\n' + src[prop]
                }
            }
        }
    }
    const mergeNumbers = (src:any, dest:any, asmt_type_slug?:string, studentUid?:string) => {
      for (let prop of mergeNumberProps){
        if (src[prop] && dest[prop] && src[prop] !== dest[prop]){
          mergesMade.push({type:'number', src:src[prop], dest:dest[prop], prop, asmt_type_slug, studentUid, })
          dest[prop] = src[prop]
        }
    }
    }
    for (let i=1 /* first is used as the ref */; i<records.length; i++){
        const record = records[i]
        const asmt_type_slugs = [... Object.keys(record)]
        for (let asmt_type_slug of asmt_type_slugs){
            if (!recordRef[asmt_type_slug]){
              recordRef[asmt_type_slug] = record[asmt_type_slug]
            }
            else {
                const src = record[asmt_type_slug]
                const dest = recordRef[asmt_type_slug]
                const studentUids = [... Object.keys(src.studentMeta)]
                mergeBooleans(src, dest, asmt_type_slug)
                mergeStrings(src, dest, asmt_type_slug)
                mergeNumbers(src, dest, asmt_type_slug)
                for (let studentUid of studentUids){
                    if (!dest.studentMeta[studentUid]){
                        dest.studentMeta[studentUid] = src.studentMeta[studentUid]
                    }
                    else {
                        const _src = src.studentMeta[studentUid]
                        const _dest = dest.studentMeta[studentUid]
                        mergeBooleans(_src, _dest, asmt_type_slug, studentUid)
                        mergeStrings(_src, _dest, asmt_type_slug, studentUid)
                    }
                }
            }
        }
    }
    
    // backups and logs
    logger.info('TwStatement merge backup',{id: recordToKeepId, MERGE_BACKUP: JSON.parse(recordRefCache)});
    recordRef['__MERGES'] = mergesMade;
    recordRef['__RECORDS_MERGED'] = recordsToRevokeIds;

    // clean up
    await this.app.service('db/write/school-tw-student-signoffs').patch(recordToKeepId, {
      meta: JSON.stringify(recordRef),
    })
    for (let id of recordsToRevokeIds){
      await this.app.service('db/write/school-tw-student-signoffs').patch(id, {
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
      })
    }
    await this.getUserEmails(recordRef);
    return {
      id: recordToKeepId,
      meta: recordRef,
    };
  }

  async getUserEmails(recordRef: any){
    const submissionUids: number[] = [];
    const submissionUidEmailMap: Map<number ,string> = new Map();
    for (const record in recordRef){
      if(recordRef[record].submitted_by){
        submissionUids.push(recordRef[record].submitted_by)
      }
    }
    if(submissionUids.length == 0) return;
    const users = await dbRawRead(this.app, {submissionUids}, `
      select a.uid
      , a.email 
      from auths a
      where a.uid in (:submissionUids)
      and a.enabled = 1`)

    users.map((user)=>{
      submissionUidEmailMap.set(user.uid, user.email)
    })

    for (const record in recordRef){
      if(recordRef[record].submitted_by && submissionUidEmailMap.get(recordRef[record].submitted_by)){
        recordRef[record].submitted_by_email = submissionUidEmailMap.get( recordRef[record].submitted_by)
      }
    }
    return recordRef;
  }

  async loadTwStatementSummary(test_window_id:number){
    const twStatements = await dbRawRead(this.app, {test_window_id}, `
      select stss.id
          , stss.meta 
          , s.lang 
          , s.id s_id
          , s.name s_name
          , s.foreign_id s_code
          , sd.foreign_id sd_code
      from school_tw_student_signoffs stss
      join schools s 
        on s.group_id  = stss.schl_group_id 
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
        and sd.is_sample = 0
      where stss.test_window_id = :test_window_id
        and stss.is_revoked = 0
    `);
    for (let statement of twStatements){
      try {
        const meta = JSON.parse(statement.meta)
        statement['suspected_contravention'] = meta['overall_issues_yes'] ? 'Yes' : ''
        statement['securely_administered'] = meta['overall_issues_no'] ? 'Yes' : ''
        statement['notes'] = meta['overall_issues_notes']
      }
      catch(e) {}
    }
    return twStatements;
  }

  async getStudentsInSchools(test_window_id:number, schl_group_ids:any[], isProvince:boolean = false){
    if(!schl_group_ids.length) {
      schl_group_ids.push(-1)
    }

    return  dbRawRead(this.app, {test_window_id, schl_group_ids}, `
      select ur.uid
          , s.lang 
          , s.foreign_id s_code
          , s.id s_id
          , sd.foreign_id sd_code
          , ur.is_revoked
      from schools s 
      join school_districts sd
        on sd.group_id = s.schl_dist_group_id
        ${isProvince ? `and sd.is_sample = 0` : ''} 
      join school_classes sc 
        on sc.schl_group_id = s.group_id 
        -- and sc.is_active = 1
      join school_semesters ss 
        on ss.id = sc.semester_id 
      join user_roles ur 
        on ur.group_id = sc.group_id 
        and ur.role_type = 'schl_student'
        and ur.is_revoked = 0 -- (keep student even removed from class)
      where ss.test_window_id  = :test_window_id
        ${isProvince ? '' : `and s.group_id in (:schl_group_ids)`} 
      group by s.id, ur.uid -- selection is safe to do by student uid (not actually joining any tables that are intended to be separate)
    `)
  }

  async loadStudents(options:IConfig){
    if (!options.test_window_id){
      throw new Errors.BadRequest('MISSING_TW');
    }
    if (!options.schl_group_id && !options.isProvince){
      throw new Errors.BadRequest('NON_MA_STUDENTS');
    }
    const queryProps = {
      test_window_id: options.test_window_id,
      schl_group_id: options.schl_group_id,
    }
    const studentsInSchools:IStudent[] =  await this.getStudentsInSchools(options.test_window_id, [options.schl_group_id], options.isProvince);
    const studentsWithTestAttempt:IStudent[] =  await dbRawRead(this.app, queryProps, `
    select ta.uid
        , s.lang 
        , s.foreign_id s_code
        , sd.foreign_id sd_code
    from test_attempts ta
    join school_class_test_sessions scts 
      on ta.test_session_id = scts.test_session_id 
    join school_classes sc
      on scts.school_class_id = sc.id 
    join school_semesters ss 
      on ss.id = sc.semester_id
    join schools s
      on sc.schl_group_id = s.group_id 
    join school_districts sd
      on sd.group_id = s.schl_dist_group_id
      ${options.isProvince ? `and sd.is_sample = 0` : ''} 
    where ss.test_window_id  = :test_window_id
    and ta.started_on is not null
      ${options.isProvince ? '' : `and s.group_id = :schl_group_id`} 
    group by s.id, ta.uid
    `)
  // Find distinct uids
  let students = studentsInSchools.concat(studentsWithTestAttempt.filter(studentWithTestAttempt =>
    !studentsInSchools.some(student => student.uid === studentWithTestAttempt.uid)
  ));
  const uids = students.map(student => student.uid);

  if(uids.length > 0){
    const studentMetas:IStudent[] =  await dbRawRead(this.app, {uids}, `
    select 
    u.id uid
    , u.first_name 
    , u.last_name 
    , um_dob.value dob
    , um_sin.value student_gov_id
    from users u 
    left join user_metas um_dob 
      on um_dob.uid = u.id 
      and um_dob.key = 'DateofBirth' -- todo:WHITELABEL
    left join user_metas um_sin 
      on um_sin.uid = u.id 
      and um_sin.key = 'StudentIdentificationNumber' -- todo:WHITELABEL
    where u.id in (:uids)
    `)
    const studentsMetaMap: Map<number, IStudent> = new Map();
    studentMetas.map(studentMeta => studentsMetaMap.set(studentMeta.uid, studentMeta));
    students = students.map(student => {
      const studentMeta = studentsMetaMap.get(student.uid)
      return {...student, ...studentMeta}
    })
    students.sort((a: IStudent,b: IStudent)=>{
      if(!a.first_name || !a.last_name || !b.first_name || !b.last_name){
        return 1;
      }
      if(a.last_name == b.last_name){
        (a.first_name > b.first_name?  1 : -1)
      }
      else{
        return (a.last_name > b.last_name?  1 : -1)
      } 
      return 1;
    })
    return students;
  }
    return [];
}

  async getAllSchools(test_window_id:number, district_id?: number){
    const registeredParticipatingSchools = await this.app
    .service('public/test-ctrl/test-window/schools-allowed')
    .getRegisteredParticipatingSchools(test_window_id, district_id);

  const actualParticipatingSchools = await this.app
    .service('public/test-ctrl/test-window/schools-allowed')
    .getActualParticipatingSchools(test_window_id, district_id);

  const principalStatements = await this.getAllSchoolPrincipalStatements(
    test_window_id,
    district_id
  );

    // determine full school set
    const schoolsMeta:Map<number, any> = new Map()
    const schoolMetaDatasets = [ // order matters, the most recent will override
      registeredParticipatingSchools,
      actualParticipatingSchools,
      principalStatements
    ]
    // get base set of schools
    for (const schools of schoolMetaDatasets){
      for (const school of schools){
        const {s_id} = school
        if (!schoolsMeta.has(s_id)){
          schoolsMeta.set(s_id, {s_id})
        }
        const schoolMeta = schoolsMeta.get(s_id)
        for (const prop in school){
          schoolMeta[prop] = school[prop]
        }
      }
    }
    return Array.from(schoolsMeta.entries()).map(([s_id, schoolMeta]) => schoolMeta);
  }

  async getAllSchoolPrincipalStatements(test_window_id:number, district_id?: number){
    const props: any = { test_window_id };
    if (district_id) {
      props.district_id = district_id;
    }

    const records = await dbRawRead(this.app, props, `
      select sd.is_sample 
          , sd.foreign_id sd_code
          , sd.name sd_name
          , s.foreign_id s_code
          , s.name s_name 
          , s.group_id s_group_id
          , s.id s_id 
          , 0 num_flagged
          , stss.id 
          , stss.schl_group_id
          , stss.meta 
          , stss.is_revoked 
          , stss.updated_on 
          , stss.is_confirmed 
          , stss.confirmed_on 
          , stss.confirmed_by_uid 
          , stss.created_on
          , count(distinct stss.id) num_multiples
      from school_tw_student_signoffs stss 
      join schools s 
        on s.group_id = stss.schl_group_id 
      join school_districts sd 
        on sd.group_id = s.schl_dist_group_id 
      where stss.test_window_id = :test_window_id
        and stss.is_revoked  = 0
        ${district_id ? 'and sd.group_id = :district_id' : ''}
        -- and s.group_id = testing -- 57392
      -- group by stss.id -- temp 
      group by s.id  
      order by s.id, stss.created_on  
    `);

    const twtarRecords = await dbRawRead(this.app, {test_window_id}, `
      select distinct type_slug 
      from test_window_td_alloc_rules twtar 
      where test_window_id = :test_window_id
      and is_sample = 0 
    `)
    const type_slugs = twtarRecords.map(r =>r.type_slug);

    const s_group_ids = records.map(r => r.s_group_id)
    let schoolEnrolments = await this.getStudentsInSchools(test_window_id, s_group_ids);

    const flaggedCaseRecords = await this.getFlaggedCaseRecords(test_window_id, false);
    const studentAsmtStatus = await this.getStudentAssessmentSubmissionStatus(test_window_id);
    const studentAsmtCaseFlagRef:Map<string, boolean> = new Map();
    for (let flagRecord of flaggedCaseRecords){
      const {uid, twtar_type_slug} = flagRecord;
      const key = [uid, twtar_type_slug].join(';')
      studentAsmtCaseFlagRef.set(key, true)
    }

    for (let record of records){
      
      
      if (record.num_multiples > 1){
        record.is_duplicate_saves = true;
        if (IS_AUTO_CLEAN){
          await this.getCleanedSchlTwSignOff(test_window_id, record.schl_group_id)
        }
      }
      
      const students = schoolEnrolments.filter( r => r.s_id == record.s_id );
      const enrolledStudentRef = new Map();
      for (let student of students){
        enrolledStudentRef.set(student.uid, true);
      }

      record.asmtCodes = []
      record.anomalyStatus = {}
      record.submissionStatus = {}
      const flaggedResolved:any[] = []
      const flaggedUnresolved:any[] = []
      const flaggedUnenrolled:any[] = []
      const locallyResolvedStudents = new Map();
      let num_flagged_pre = 0; 

      try {
        const meta = JSON.parse(record.meta)
        for (const asmtCode in meta){
          const asmtStatus = meta[asmtCode];
          if (asmtStatus && (typeof asmtStatus === 'object') ){
            record.asmtCodes.push(asmtCode);
            record.submissionStatus[asmtCode] = asmtStatus.is_submitted
            let isAnyAnomalies = false;
            for (const studentUid in asmtStatus.studentMeta){
              const isEnrolled = !! enrolledStudentRef.get(+studentUid)
              if (isEnrolled){
                const studentStatus = asmtStatus.studentMeta[studentUid]
                const submissionStatus = studentAsmtStatus[studentUid] ? studentAsmtStatus[studentUid][asmtCode]: undefined
                const key = [studentUid, asmtCode].join(';')
                let isStudentFlagged = studentAsmtCaseFlagRef.get(key) || false;
                if (isStudentFlagged){
                  if (this.isStudentFlagPending(studentStatus, submissionStatus)){
                    flaggedUnresolved.push({studentUid, asmtCode})
                    num_flagged_pre ++; // temp: should be doing this by cycling back through the list of students, but this is going to catch anyone who has started their principal kits

                  }
                  else { 
                    flaggedResolved.push({studentUid, asmtCode})
                    locallyResolvedStudents.set(key, true)
                    // studentAsmtCaseFlagRef.set(key, false)
                  }
                }
                if (studentStatus?.is_anomaly){
                  isAnyAnomalies = true;
                  record.is_any_anomalies = true;
                }
              }
              else {
                const key = [studentUid, asmtCode].join(';')
                let isStudentFlagged = studentAsmtCaseFlagRef.get(key) || false;
                if (isStudentFlagged){
                  flaggedUnenrolled.push({studentUid, asmtCode})
                }
              }
            }
            record.anomalyStatus[asmtCode] = isAnyAnomalies;
          }
        }
      }
      catch(e:any){
        record.is_malformed = true
        record.malformed_error = e?.message || 'JSON_PARSE?'
      } 

      let num_flagged = 0; 
      for (let student of students){
        for (let asmtCode of type_slugs){
          const key = [student.uid, asmtCode].join(';')
          const isStudentFlagged = studentAsmtCaseFlagRef.get(key) || false;
          const isLocallyResolved = locallyResolvedStudents.get(key)
          if (isStudentFlagged && !isLocallyResolved){
            num_flagged ++;
          }
        }
      }

      record.num_flagged = num_flagged
      record.num_flagged_pre = num_flagged_pre // this is just a check 

      if (IS_DEBUG_OVERVIEW){
        record.flaggedResolved = flaggedResolved
        record.flaggedUnresolved = flaggedUnresolved
        record.flaggedUnenrolled = flaggedUnenrolled
      }
    }
    return records
  }

  isStudentFlagPending(studentMeta:any, submissionStatus: any){
      if (studentMeta){
        if ((submissionStatus && submissionStatus['is_submitted']) || studentMeta['is_absent'] || studentMeta['is_excused'] || studentMeta['is_transferred'] || studentMeta['is_anomaly']){
          return false;
        }
      }
      return true
  }

  async getStudentAssessmentSubmissionStatus(test_window_id: number) {
    const isProvince = true;
    const students_asmt_status:{
      uid:number,
      asmt_type_slug:string,
      is_in_ts:number,
      is_submitted:number,
      report_num: number,
      s_code?:string,
    }[] = await dbRawRead(this.app, {test_window_id}, TW_STUDENTS_ASMT_STATUS(isProvince));

    const studentAsmtStatus: any = {}
    
    for (let asmt_status of students_asmt_status){
        if(!studentAsmtStatus[asmt_status.uid]) studentAsmtStatus[asmt_status.uid] = {};
        if(!studentAsmtStatus[asmt_status.uid][asmt_status.asmt_type_slug])studentAsmtStatus[asmt_status.uid][asmt_status.asmt_type_slug] = {};
        const status = studentAsmtStatus[asmt_status.uid][asmt_status.asmt_type_slug]
        status.is_submitted = (asmt_status.is_submitted==1);
    }
    return studentAsmtStatus;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const test_window_id = id;
    const {schl_group_id, schl_tw_sign_off} = <any> data;
    if (params?.query){
      const uid = await currentUid(this.app, params)
      if (schl_tw_sign_off.id){
        await this.app.service('db/write/school-tw-student-signoffs').patch(schl_tw_sign_off.id, {
          meta: JSON.stringify(schl_tw_sign_off.meta),
          updated_on: dbDateNow(this.app),
          updated_by_uid: uid,
        })
        return {id: schl_tw_sign_off.id}
      }
      else {
        const newRecord = await this.app.service('db/write/school-tw-student-signoffs').create({
          schl_group_id,
          test_window_id,
          meta: JSON.stringify(schl_tw_sign_off.meta),
          updated_on: dbDateNow(this.app),
          updated_by_uid: uid,
        })
        return {id: newRecord.id}
      }
    }
    else {
      throw new Errors.BadRequest()
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
