export const SQL_SCHL_SESSIONS = (isStrictSessions:boolean) => `
-- SQL_SCHL_SESSIONS
  select scts.school_class_id
  , ts.is_closed
  , ts.closed_on
  , scts.test_session_id
  , scts.slug
  , scts.caption
  , ts.date_time_start
  , sc.name
  , sc.group_id
  , sc.is_active sc_is_active
  , count(distinct usr.uid) as num_students
  , count(distinct ta.uid) as num_attempts
  , ut.first_name
  , ut.last_name
  , twtdar.test_design_id
  , twtdar.is_field_test
  , tw.type_slug as "tw_slug"
  , twtdar.resp_sheet_config
  , ts.is_cancelled
  , ts.cancelled_on
  , ts.name_custom
  , sc.access_code
  from school_class_test_sessions scts
  join test_sessions ts
  on ts.id = scts.test_session_id
  join test_windows tw
  on tw.id = ts.test_window_id
  join school_classes sc
  on sc.id = scts.school_class_id
  join user_roles urt
  on urt.group_id = sc.group_id
  left join test_window_td_alloc_rules twtdar on twtdar.test_window_id = ts.test_window_id and twtdar.slug = scts.slug
  left join user_roles usr
  on usr.group_id = sc.group_id
  and usr.role_type ='schl_student'
  ${isStrictSessions ? 'and usr.is_revoked != 1' : ''}
  left join test_attempts ta
  on ta.test_session_id = ts.id
  and ta.uid = usr.uid
  ${isStrictSessions ? 'and ta.id is not null' : ''}
  left join users ut
  on ut.id = urt.uid
  where ts.test_window_id = :test_window_id
  and sc.schl_group_id = :schl_group_id
  and urt.role_type = "schl_teacher"
  and urt.is_revoked = 0
  ${isStrictSessions ? '' : 'and not (usr.is_revoked = 1 and ta.id is null)'}
  group by test_session_id
`

// {test_design_ids}
export const SQL_NUM_QUESTION_REQUIRING_SCAN = `
  -- SQL_NUM_QUESTION_REQUIRING_SCAN, < 0.5 sec
  SELECT tqr.test_design_id
  , count(distinct tqsi.item_id) as num_scans
  FROM test_question_register tqr
  LEFT JOIN test_question_scoring_info tqsi 
    ON tqr.tqsi_id = tqsi.id 
    AND tqsi.is_paper_response = 1 
  WHERE tqr.test_design_id in (:test_design_ids) 
  GROUP BY tqr.test_design_id;
`

// todo: does not incoroporate paper vs. online toggle 
export const SQL_TS_PENDING_SCANS = `
  -- SQL_TS_PENDING_SCANS
  SELECT ta.test_session_id
       , count(distinct ta.uid) n_students
       , count(distinct taqr.id) n_scans_expected
       , count(distinct tasr.id) n_scans_received
  FROM test_attempts ta 
  JOIN test_window_td_alloc_rules twtar 
  	on ta.twtdar_id = ta.id
  JOIN test_question_register tqr
  	on twtar.test_design_id = tqr.test_design_id 
  JOIN test_question_scoring_info tqsi 
    ON tqr.tqsi_id = tqsi.id 
    AND tqsi.is_paper_response = 1 
    AND tqsi.lang = tqr.lang
  JOIN test_attempt_question_responses taqr 
    on taqr.test_question_id = tqr.question_id 
    and taqr.test_attempt_id = ta.id
    and taqr.is_invalid = 0
  LEFT JOIN test_attempt_scan_responses tasr
    on tasr.taqr_id = taqr.id
    and tasr.is_discarded != 1
  WHERE ta.test_session_id in (:test_session_ids)
  GROUP BY ta.test_session_id;
  `
  // tqr.test_design_id in (:test_design_ids) 