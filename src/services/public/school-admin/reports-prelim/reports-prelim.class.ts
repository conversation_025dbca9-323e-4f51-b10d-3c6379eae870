import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawReadReporting } from '../../../../util/db-raw';
import PDFDocument from 'pdfkit';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import moment from 'moment';

const IS_CONSTRAIN_FIRST_FIVE_REPORTS = false;

interface Data {}

interface ServiceOptions {}

export class ReportsPrelim implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    
    const test_window_id = +id;
    if (!test_window_id || !params || !params.query) {
      throw new Errors.BadRequest("MISSING REQUIRED ID OR PARAMS")
    }
    const { schl_group_id, lang } = params.query;

    const students = await this.getStudentsForSchoolTw(schl_group_id, test_window_id);
    const testWindowIds = this.getAssocTestWindowIds(test_window_id);
    const pdfBase64 = await this.createPdf(students, testWindowIds); 
    
    return {
      test_window_id,
      pdfBase64,
    }
    
  }

  getAssocTestWindowIds(test_window_id:number){
    // todo: group by academic year
    return [test_window_id]
  }

  async createPdf(students:any[], testWindowIds:number[]) {
    const doc = new PDFDocument();

    if (IS_CONSTRAIN_FIRST_FIVE_REPORTS){
      students = students.slice(0, 5) // temp
    }

    const pageWidth = doc.page.width;
    const margin = 50;
    const sectionWidth = 140;
    const availableWidth = pageWidth - margin * 2 - sectionWidth;
    const scoreTextWidth = 50;
    const gaugeWidth = availableWidth - scoreTextWidth - 20;
    const startX = margin;
    const sectionX = startX;
    const scoreX = sectionX + sectionWidth;
    const gaugeX = scoreX + scoreTextWidth + 10;
    const legendWidth = 150;
    const legendHeight = 50;
    const barHeight = 15;
    const legendSize = 10;
    const lineHeight = 10;

    for (let i = 0; i < students.length; i++) {
      const student = students[i];

      if (i > 0) {
        doc.addPage();
      }

      const {
        uid,
        first_name,
        last_name,
        title_persistent,
        student_gov_id,
        date_start,
        school_name,
        sd_name,
      } = student

      const responses = await dbRawRead(this.app, {uid, testWindowIds}, `
        select ta_id
              , type_slug
              , name
              , name_sub
              , item_id
              , test_window_id
              , response_raw
              , lang
              , score
              , maxScore
        from ( 
          select ta.id ta_id 
                , tqr.question_id item_id 
                , twtar.id
                , twtar.lang -- todo could be multiple langs in alloc rule
                , twtar.test_window_id
                , twtar.type_slug
                , twtt.caption_short name
                , twtt.part_description_short name_sub
                , taqr.response_raw
                , IFNULL(taqr.score, 0) as score
                , GREATEST(COALESCE(taqr.weight, 0), COALESCE(tqr.score_points, 0)) as maxScore
          from test_attempts ta
          join test_window_td_alloc_rules twtar 
            on twtar.id = ta.twtdar_id
            and twtar.is_secured = 1
            and twtar.is_marking_req = 0
          join test_window_td_types twtt 
            on twtt.type_slug = twtar.type_slug 
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
          join test_question_register tqr 
            on tqr.test_design_id  = IFNULL(twtar.tqr_ovrd_td_id, twtar.test_design_id) 
          left join test_attempt_question_responses taqr 
            on taqr.test_attempt_id = ta.id
            and taqr.test_question_id = tqr.question_id 
          WHERE ta.uid = :uid
            and twtar.test_window_id in (:testWindowIds)
            and ta.is_invalid = 0
            and ta.started_on IS NOT NULL
        ) t 
      `)

      for (let response of responses) {
          if (response.response_raw){
            const {formatted_response} = await this.app
              .service('public/student/extract-item-response')
              .processResponse(response.response_raw, response.item_id);
            response.responseText = formatted_response;
          }
      }
      await this.app
        .service('public/educator/session-report')
        .applyTqerScoreOverrides(responses)

      let sections:any[] = []
      let sectionRef = new Map();
      for (let response of responses) {
        const {
                ta_id
              , type_slug
              , name
              , name_sub
              , item_id
              , test_window_id
              , response_raw
              , lang
              , score
              , maxScore
        } = response;
        const key = `${ta_id}`;

        if (!sectionRef.has(key)) {
          const section = {
                ta_id
              , type_slug
              , name
              , name_sub
              // , item_id
              // , test_window_id
              // , response_raw
              // , lang
              , score: 0
              , maxScore: 0
          }
          sections.push(section);
          sectionRef.set(key, section);
        }
        const section = sectionRef.get(key);
        section.score += +(score || 0);
        section.maxScore += +(maxScore || 0);
      }

      const sectionMap:any = {}
      for (let section of sections){
        if (section.name_sub){
          section.name = section.name + ' (' + section.name_sub + ')'
        }

        // Filter out duplicates (choose highest score)
        if (!sectionMap[section.type_slug]) {
          sectionMap[section.type_slug] = section
        }
        if (sectionMap[section.type_slug].score < section.score) {
          sectionMap[section.type_slug] = section;
        }

      }

      sections = Object.values(sectionMap);

      const gaugeData = {
        firstName: first_name,
        lastName: last_name,
        asn: student_gov_id,
        date: moment(date_start).format('MMMM YYYY'),
        assessment: JSON.parse(title_persistent).en,
        school: school_name,
        schoolAuth: sd_name,
        sections,
      }

      const maxScore = Math.max(...gaugeData.sections.map((section:any) => section.maxScore));

      doc.fontSize(20).text('Preliminary Individual Student Results', { align: 'center' });
      
      doc.moveDown(0.5).fontSize(16).text(`${gaugeData.assessment} - ${gaugeData.date}`, { align: 'center' });
    
      const infoStartY = doc.y + 1.5 * doc.currentLineHeight();
      const infoX = pageWidth / 2 - 200;
      const infoWidth = 225; 

      const infoItems = [
        { label: 'Student Name:', value: `${gaugeData.firstName} ${gaugeData.lastName}` },
        { label: 'Alberta Student Number:', value: gaugeData.asn },
        { label: 'School of Writing:', value: gaugeData.school },
        { label: 'School Authority:', value: gaugeData.schoolAuth }
      ];
      
      // Loop through each info item and add it to the PDF
      infoItems.forEach((item, index) => {
        const y = infoStartY + index * (doc.currentLineHeight() + 15); // Calculate y position
      
        doc.fontSize(10).fill('black').text(item.label, infoX, y, { width: infoWidth, align: 'left' });
        doc.text(item.value, infoX + infoWidth, y, { width: infoWidth, align: 'left' });
      });

      // Draw divider line
      doc.moveDown(1).moveTo(margin, doc.y).lineTo(pageWidth - margin, doc.y).stroke();

      doc.moveDown(1);

      // Student and Max labels on the same y-level
      const labelsY = doc.y;
      doc.fontSize(6).text('Student', scoreX - 25, labelsY, { width: scoreTextWidth, align: 'center'});
      doc.fontSize(6).text('Max', scoreX + scoreTextWidth - 40, labelsY, { width: scoreTextWidth, align: 'center' }); // Adjusted x position for alignment

      // Score labels on the same y-level
      const scoreLabelsY = labelsY + 10; // Adjusted y position
      doc.fontSize(6).text('Score', scoreX - 25, scoreLabelsY, { width: scoreTextWidth, align: 'center' });
      doc.fontSize(6).text('Score', scoreX +  scoreTextWidth - 40, scoreLabelsY, { width: scoreTextWidth, align: 'center' }); // Adjusted x position for alignment

      gaugeData.sections.forEach((section:any) => {
        const y = doc.y + lineHeight;
    
        doc.fontSize(12).fill('black').text(section.name, sectionX, y, { width: sectionWidth });
        const textHeight = doc.heightOfString(section.name, { width: sectionWidth });
    
        doc.fontSize(10).fill('black').text(`${section.score} / ${section.maxScore}`, scoreX, y + 2, { width: scoreTextWidth });

        const barLength = (section.maxScore / maxScore) * gaugeWidth;
        const scoreLength = (section.score / maxScore) * gaugeWidth;
        doc.rect(gaugeX, y, barLength, barHeight).fillOpacity(0.7).fill('lightgray');
        doc.rect(gaugeX, y, scoreLength, barHeight).fillOpacity(1).fill('gray');
        doc.y += textHeight;
      });
      
      doc.fill('black').moveDown(3).font('Helvetica-Bold').text("Note to Parents: ", sectionX, doc.y, {width: pageWidth - margin})
      doc.moveDown(0.5).font("Helvetica").text("These are preliminary results, based only on the machine-scored portion of each test, without taking into account accommodations students used that affected scoring", {width: pageWidth - margin})
      doc.moveDown(0.5).font('Helvetica-Bold').text("The results will not be considered final until provincial marking is complete.")
      doc.moveDown(0.5).font("Helvetica").text("Once results are finalized, an electric copy of the Individual Student Profile for your child will be available on myPass or the school your child attended. The final results will show your child's performance in relation to provincial standards.")
      // const legendX = pageWidth - margin - legendWidth;
      // const legendY = doc.y + 20;
      // doc.rect(legendX, legendY, legendWidth, legendHeight).stroke();
      // doc.rect(legendX, legendY, legendWidth, 20).fillOpacity(0.2).fill('lightgray');
      // doc.fontSize(12).fillOpacity(1).fill('black').text('Graph Legend', legendX + 5, legendY + 5);

      // const legendContentY = legendY + 20;
      // doc.rect(legendX + 10, legendContentY, legendSize, legendSize).fillOpacity(1).fill('gray');
      // doc.fontSize(10).fill('black').text('Student Score', legendX + 25, legendContentY);
      // doc.rect(legendX + 10, legendContentY + lineHeight, legendSize, legendSize).fillOpacity(0.2).fill('lightgray');
      // doc.fontSize(10).fillOpacity(1).fill('black').text('Max Score', legendX + 25, legendContentY + lineHeight);
    }

    doc.end();

    const chunks:any[] = [];
    doc.on('data', (chunk) => {
      chunks.push(chunk);
    });
    const pdfBase64 = await new Promise((resolve, reject) => {
      doc.on('end', () => {
        const buffer = Buffer.concat(chunks);
        const base64String = buffer.toString('base64');
        resolve(base64String);
      });
      doc.on('error', reject);
    });

    console.log('reports')

    return pdfBase64;
  }

  async getStudentsForSchoolTw(schl_group_id:number, test_window_id:number){
    // todo: rather than a big join, us standard function for pulling student meta data based on list of uids
    return dbRawReadReporting(this.app, {schl_group_id, test_window_id}, `
        select
            u.id uid
          , u.first_name 
          , u.last_name 
          , um_dob.value dob
          , um_sin.value student_gov_id
          , s.name as school_name
          , sd.name as sd_name
          , tw.title
          , tw.title_persistent
          , tw.date_end
        from school_classes sc
        join user_roles ur
       		on ur.group_id = sc.group_id
       		and ur.role_type = "schl_student"
       		and ur.is_revoked = 0
       		and ur.is_removed = 0	
       	left join users u
        	on u.id = ur.uid
        left join user_metas um_dob 
          on um_dob.uid = u.id 
          and um_dob.key = 'DateofBirth'
        left join user_metas um_sin 
          on um_sin.uid = u.id 
          and um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber')
        left join schools s on s.group_id = sc.schl_group_id
        left join school_districts sd on sd.group_id = s.schl_dist_group_id
        join school_semesters ss on ss.id = sc.semester_id
        join test_windows tw on tw.id = ss.test_window_id
        where tw.id = :test_window_id
          and sc.schl_group_id = :schl_group_id
        GROUP BY u.id
        order by u.last_name, u.first_name 
      `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
