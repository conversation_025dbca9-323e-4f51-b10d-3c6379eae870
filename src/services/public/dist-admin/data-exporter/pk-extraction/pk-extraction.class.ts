import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawReadReporting } from '../../../../../util/db-raw';
import { left_join } from '../../../data-exporter/data-export-queries/transforms/merge';
import { cast_col } from '../../../data-exporter/data-export-queries/transforms/cast-col';

type value = number | string | boolean
type IRow = {[key: string]: value}

interface Data {}

interface ServiceOptions {}

const fieldsCommon = [
  'test_window_id',
  's_code',
  's_name',
  's_group_id',
  's_id',
]

export class PkExtraction implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async load_pk_data(tw_ids: number[], include_sample_schools: boolean) {
    const query = `
      select /*+ MAX_EXECUTION_TIME(1200000) */ sd.is_sample
            , sd.foreign_id sd_code
            , sd.name sd_name
            , s.foreign_id s_code
            , s.name s_name
            , s.group_id s_group_id
            , s.id s_id
            , stss.id
            , stss.schl_group_id
            , stss.meta
            , stss.is_revoked
            , stss.updated_on
            , stss.is_confirmed
            , stss.created_on
            , stss.test_window_id
        from school_tw_student_signoffs stss
        join schools s
            on s.group_id = stss.schl_group_id
        join school_districts sd
            on sd.group_id = s.schl_dist_group_id
        where stss.test_window_id in (:tw_ids)
            and stss.is_revoked  = 0
            ${include_sample_schools ? '' : 'and sd.is_sample = 0'}
        group by s.id
        order by s.id, stss.created_on
    `
    return dbRawReadReporting(this.app, {tw_ids}, query);
  }

  async processPrincipalKitData(pkData: any[]) {
    const schools: any[] = [];
    const schoolAsmts: any[] = [];
    const schoolAsmtStus: any[] = [];

    // Updates the above arrays in place
    const parsePrincipalKitMeta = (jsonMeta: string, commonProps: any) => {
        try {
            const principalKitDocument = JSON.parse(jsonMeta);
            for (let asmt_code in principalKitDocument) {
                if (asmt_code.substring(0, 2) !== '__') {
                    const schoolAsmtRecord = { ...commonProps, asmt_code };
                    const stuAsmtRecordBase = { ...schoolAsmtRecord };
                    const asmtDetail = principalKitDocument[asmt_code];
                    for (let asmtDetailProp in asmtDetail) {
                        switch (asmtDetailProp) {
                            case 'studentMeta':
                                const studentMeta = asmtDetail[asmtDetailProp];
                                for (let student_uid in studentMeta) {
                                    const student = studentMeta[student_uid];
                                    schoolAsmtStus.push({
                                        ...stuAsmtRecordBase,
                                        student_uid,
                                        ...student,
                                    });
                                }
                                break;
                            default:
                                schoolAsmtRecord[asmtDetailProp] = asmtDetail[asmtDetailProp];
                                break;
                        }
                    }
                    schoolAsmts.push(schoolAsmtRecord);
                }
            }
        } catch (e) {
            console.error("Error parsing meta:", e);
        }
    };

    for (let item of pkData) {
        item.s_group_id = item.schl_group_id;
        parsePrincipalKitMeta(item.meta, {
            test_window_id: item.test_window_id,
            s_code: item.s_code,
            s_name: item.s_name,
            s_group_id: item.s_group_id,
            s_id: item.s_id,
        });
        schools.push(item);
    }

    return { schools, schoolAsmts, schoolAsmtStus }; // TODO: assign explicit nulls for missing values.
  }

  async getPrincipalKitMeta(tw_ids: number[], include_sample_schools: boolean) {

    const pk_data = await this.load_pk_data(tw_ids, include_sample_schools) // Can be changed to take in the load asset.

    let { schools } = await this.processPrincipalKitData(pk_data);

    // The fields to keep in the transformed output
    const fields = [
      'id',
      'is_sample',
      'sd_code',
      'sd_name',
      's_code',
      's_name',
      's_group_id',
      's_id',
      'updated_on',
      'created_on',
      'test_window_id',
    ];

    const filteredSchools = schools.map((school: any) => {
      const filtered: any = {};
      for (const field of fields) {
          filtered[field] = school[field];
      }
      return filtered;
    });

    return filteredSchools
  }

  async getPrincipalKitSchoolAsmt(tw_ids: number[], include_sample_schools: boolean) {
    const pk_data = await this.load_pk_data(tw_ids, include_sample_schools) // Can be changed to take in the load asset.

    let { schoolAsmts } = await this.processPrincipalKitData(pk_data);

    // The fields to keep in the transformed output
    const fields = [
      ... fieldsCommon,
      'asmt_code',
    ]

    const filteredschoolAsmts = schoolAsmts.map((school: any) => {
      const filtered: any = {};
      for (const field of fields) {
          filtered[field] = school[field];
      }
      return filtered;
    });

    return filteredschoolAsmts
  }

  async getPrincipalKitStudents(tw_ids: number[], include_sample_schools: boolean) {

    const pk_data = await this.load_pk_data(tw_ids, include_sample_schools) // Can be changed to take in the load asset.

    let { schoolAsmtStus } = await this.processPrincipalKitData(pk_data);

    // The fields to keep in the transformed output
    const fields = [
      ... fieldsCommon,
      'asmt_code',
      'student_uid',
      'is_absent',
      'is_other_class',
      'is_excused',
      'is_transferred',
      'is_anomaly',
      'notes',
  ]

    let filteredschoolAsmtStus = schoolAsmtStus.map((school: any) => {
      const filtered: any = {};
      for (const field of fields) {
          filtered[field] = school[field] ?? null;
      }
      return filtered;
    });

    filteredschoolAsmtStus = cast_col(filteredschoolAsmtStus, [{ col_target: "student_uid", target_type: "number" }])
    
    return filteredschoolAsmtStus
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
