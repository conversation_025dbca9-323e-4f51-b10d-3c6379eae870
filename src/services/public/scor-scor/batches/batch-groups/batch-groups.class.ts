import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../../util/db-raw';
import { AssignedItemComponentType } from '../../summary/types/assigned-tasks';
import { SQL_AVAIL_MARKERS, SQL_BATCHES_INFO, SQL_BATCH_GROUP_INFO, SQL_ITEM_RULES, SQL_LEAD_SCOR_FLAGED, SQL_LEAD_SCOR_FLAG_SENSI_BG, SQL_LEAD_SCOR_NOT_YET_CLAIMED, SQL_LEAD_SCOR_NOT_YET_CLAIMED_TEST_CENTRE, SQL_LEAD_SCOR_SCORED, SQL_SCORER_HISTORY, SQL_SCORER_SCORING_TASK, SQL_HAS_MCBR_SCAN_RESPONSES, SQL_LEAD_SCOR_STATS_UIDS_SUPERVISOR, SQL_ITEMS_BATCH_POLICIES} from '../../../../../sql/scoring';
import { ensureLockedDomain } from '../../../../../util/domain-lock';
import failedLoginService from '../../../../auth/failed-login/failed-login.service';
const _ = require('lodash');

interface Data {}

interface ServiceOptions {}

enum ScorerMenuView {
  SCORINGS = 'scorings',
  S2S = 's2s',
  S2S_CIN = 's2s_cin',
  NOT_YET_CLAIMED = 'not_yet_claimed',
}

export const DEFAULT_NYC_LIMIT = 50000 // +(params.query.limit || 50000);

export class BatchGroups implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data> | any> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    let markingWindowId:number | null = null;
    if(!params.query){
      throw new Errors.BadRequest();
    }

    if(params.query && params.query.markingWindowId) {
      markingWindowId = params.query.markingWindowId;
    }
    let is_test_centre = false;
    const limit = DEFAULT_NYC_LIMIT;
    
    const isSupervisorView: boolean = !!+params.query?.isSupervisorView;
    let isUidGroupRestricted: boolean = false;
    if (markingWindowId){
      const mw = await dbRawReadSingle(this.app, {markingWindowId}, `
      select id
      , is_active 
      , domain_lock 
      , is_test_centre
      , is_group_marking
      from marking_windows mw 
      where id = :markingWindowId
      ;`);
      ensureLockedDomain(params, mw.domain_lock)
      is_test_centre = mw.is_test_centre == 1;

      if (isSupervisorView && mw.is_group_marking){
        isUidGroupRestricted = true;
      }
    }

    const isGetTabCounts = params.query.isGetTabCounts;

    const view: ScorerMenuView = params.query?.scorerMenuView;


    const assignmentItemCode: string = params.query?.assignmentItemCode
    let uids = [];
    if (isUidGroupRestricted){
      const current_uid = await currentUid(this.app, params);
      const userRecords = await dbRawRead(this.app, {window_id: markingWindowId, supervisor_uid: current_uid},  SQL_LEAD_SCOR_STATS_UIDS_SUPERVISOR)
      uids = userRecords.map(r => r.uid)
    }
    const isZeroResponses = (isUidGroupRestricted && !uids.length);

    if (isGetTabCounts && markingWindowId){
      if (isZeroResponses){
         const counts = {
          [ScorerMenuView.SCORINGS]: 0, 
          [ScorerMenuView.S2S]: 0
         }
         return counts;
      }
      return this.getTabCounts(markingWindowId, is_test_centre, isSupervisorView, isUidGroupRestricted, uids, assignmentItemCode)
    } else {
      if (isZeroResponses){
        return [];
      }
    }

    let records:any[] = [];

    const applyMarkerMetaData = async (uidProp:string) => {
      if (markingWindowId){
        return await this.app.service('public/scor-lead/accounts').applyMarkerMetaData(records, +markingWindowId, uidProp)
      }
      return records;
    }

    //In supervisor views, limit scorer uids
    if(view == ScorerMenuView.SCORINGS) {
      records = await dbRawRead(this.app, {markingWindowId, uids, assignmentItemCode}, SQL_LEAD_SCOR_SCORED({isSupervisorView, isUidGroupRestricted}));
      records = await applyMarkerMetaData('max_valid_read_uid');
    } 
    //In supervisor views, limit scorer uids
    else if(view == ScorerMenuView.S2S) {
      records = await dbRawRead(this.app, {markingWindowId, uids, assignmentItemCode, isCIN: 0}, SQL_LEAD_SCOR_FLAGED({isSupervisorView, isUidGroupRestricted}));
      records = await applyMarkerMetaData('max_valid_read_uid');
    } 
    else if(view == ScorerMenuView.S2S_CIN) {
      const bgRecords = await dbRawRead(this.app, {markingWindowId}, SQL_LEAD_SCOR_FLAG_SENSI_BG);
      const claimed_batch_group_ids = [-1, ... bgRecords.map(r => r.claimed_batch_group_id)];
      records = await dbRawRead(this.app, {markingWindowId, isCIN: 1, claimed_batch_group_ids}, SQL_LEAD_SCOR_FLAGED({isBatchGroupFilter: true}));
      records = await applyMarkerMetaData('max_valid_read_uid');
    } 
    else if(view == ScorerMenuView.NOT_YET_CLAIMED) {
      if(is_test_centre){
        records = await dbRawRead(this.app, {markingWindowId}, SQL_LEAD_SCOR_NOT_YET_CLAIMED_TEST_CENTRE(limit));
      }
      else {
        records = await dbRawRead(this.app, {markingWindowId}, SQL_LEAD_SCOR_NOT_YET_CLAIMED(limit));
      }
    } 
    else {
      records = await dbRawRead(this.app, {markingWindowId, uids, assignmentItemCode}, SQL_LEAD_SCOR_SCORED({isSupervisorView, isUidGroupRestricted}));
      records = await applyMarkerMetaData('max_valid_read_uid');
    }

    return records;
  }

  /** Get the number of records that would be returned in each tab */
  async getTabCounts(markingWindowId: number, isTestCentre = false, isSupervisorView:boolean = false, isUidGroupRestricted: boolean = false, uids?: number[], assignmentItemCode?: string){
    const wrapInCount = (queryStr: string) => {
      return `
      select count(*) as count from (${queryStr.split(";")[0]}) q;
      `
    }

    let notYetClaimedQuery = '';
    if (isTestCentre){
      notYetClaimedQuery = SQL_LEAD_SCOR_NOT_YET_CLAIMED_TEST_CENTRE(DEFAULT_NYC_LIMIT)
    }
    else {
      notYetClaimedQuery = SQL_LEAD_SCOR_NOT_YET_CLAIMED(DEFAULT_NYC_LIMIT)
    }
    const bgRecords = await dbRawRead(this.app, {markingWindowId}, SQL_LEAD_SCOR_FLAG_SENSI_BG);
    const claimed_batch_group_ids = [-1, ... bgRecords.map(r => r.claimed_batch_group_id)];
    const counts = {
      [ScorerMenuView.SCORINGS]: (await dbRawReadSingle(this.app, {markingWindowId, uids, assignmentItemCode}, wrapInCount(SQL_LEAD_SCOR_SCORED({isSupervisorView, isUidGroupRestricted}))))?.count,
      [ScorerMenuView.S2S]: (await dbRawReadSingle(this.app, {markingWindowId, uids, assignmentItemCode, isCIN: 0}, wrapInCount(SQL_LEAD_SCOR_FLAGED({isSupervisorView, isUidGroupRestricted}))))?.count,
      [ScorerMenuView.S2S_CIN]: (await dbRawReadSingle(this.app, {markingWindowId, isCIN: 1, claimed_batch_group_ids}, wrapInCount(SQL_LEAD_SCOR_FLAGED({isBatchGroupFilter: true}))))?.count,
      [ScorerMenuView.NOT_YET_CLAIMED]: (await dbRawReadSingle(this.app, {markingWindowId}, wrapInCount(notYetClaimedQuery)))?.count,
    }
    return counts
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const batchGroup = await dbRawReadSingle(this.app, [id], SQL_BATCH_GROUP_INFO)

    if(!batchGroup) {
      throw new Errors.BadRequest('INC_BATCH_GROUP_ID')
    }
    if(!params || !params.query || !params.query.markingWindowId) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    let markerReadId: number;
    if(params.query.markerReadId) {
      markerReadId = params.query.markerReadId
    } else {
      markerReadId = batchGroup.min_marker_read_id
    }

    const markingWindowId = params.query.markingWindowId;

    const availableMarkers = await dbRawRead(this.app, {markingWindowId}, SQL_AVAIL_MARKERS)

    const batches = await dbRawRead(this.app, {claimed_batch_group_id:id, markerReadId:markerReadId, markingWindowId: markingWindowId}, SQL_BATCHES_INFO)
    const mwiIds = batches.map((batch) => batch.marking_window_item_id);
    if(!mwiIds || mwiIds.length <= 0) {
      throw new Errors.GeneralError('NO_BATCHES_FOUND')
    }

    const marking_window_id = batches[0].marking_window_id

    const scoringPolicyRef = new Map();
    try {
      const scoringPolicyRecords = await dbRawReadReporting(this.app, {mwi_ids: mwiIds}, SQL_ITEMS_BATCH_POLICIES)
      for (let sp of scoringPolicyRecords){
        scoringPolicyRef.set(+sp.mwi_id, JSON.parse(sp.read_rules));
      }
    }
    catch(e){
      console.log('Failed to pull scoring policies')
    }
    


    const parsedBatchInfo = await Promise.all(mwiIds.map(async (itemId) => {
      const mwiBatch = batches.find((batch) => batch.marking_window_item_id == itemId);
      const responses = await dbRawRead(this.app, [id, itemId, markerReadId], `
        select mcbr.id
             , mcbr.id mcbr_id -- dupe
             , mcb.marking_window_item_id mwi_id
             , mcbr.taqr_id
             , mcbr.is_marked
             , mcbr.marked_on 
             , mcbr.from_read_rule
             , mcbr.is_scale_supressed
        from marking_claimed_batch_responses mcbr 
        join marking_claimed_batches mcb 
          on mcb.id = mcbr.claimed_batch_id
        where mcbr.claimed_batch_group_id = ?
          and mcb.marking_window_item_id = ?
          and mcbr.marker_read_id = ?
          and mcbr.is_revoked = 0
        order by mcbr.id desc
          limit 1
          ;
        ;
      `);
      
      const mcbrIds = responses.map(r => r.id)
      const responseScan = await dbRawReadSingle(this.app, {mcbrIds}, SQL_HAS_MCBR_SCAN_RESPONSES);
      const hasResponseScan = !!responseScan;
      const currResponse = responses[0];
      const scores = await dbRawRead(this.app, {id, itemId, markerReadId, responseId: currResponse.id}, `
        SELECT mrs.*
        FROM marking_claimed_batch_responses mcbr
        join marking_response_scores mrs 
          on mrs.batch_response_id = mcbr.id
        join marking_claimed_batches mcb
          on mcb.id = mcbr.claimed_batch_id 
        where mcbr.claimed_batch_group_id = :id
          and mcb.marking_window_item_id = :itemId
          and mcbr.id = :responseId
          and mcbr.marker_read_id = :markerReadId
          and mcbr.is_revoked = 0
        order by mrs.id desc
        limit 1
        ;
      `);
      const scorerHistory = await dbRawRead(this.app, {id, itemId, markingWindowId}, SQL_SCORER_HISTORY);
      await this.app.service('public/scor-lead/accounts').applyMarkerMetaData(scorerHistory, +marking_window_id, 'uid')
      await this.app.service('public/scor-lead/accounts').applyMarkerMetaData(scorerHistory, +marking_window_id, 'inspected_by_uid', 'inspected_by_')
      
      const scoreOptions = await dbRawRead(this.app, [itemId], `
        select mspo.id
             , mspo.order
             , mspo.is_offset
             , mso.slug
             , mso.value
             , mso.caption
             , mso.color
             , mspo.rubric_text
             , mso.is_pre_score_flag
             , mso.slug_code
             , msp.is_non_scored_profile
        from marking_window_items mwi
        join marking_score_profiles msp 
          on msp.id = mwi.score_profile_id
        join marking_score_profile_options mspo
          on mspo.score_profile_id = mwi.score_profile_id
          and mspo.is_revoked = 0
        join marking_score_options mso
          on mso.id = mspo.score_option_id
        where mwi.id = ?
        order by mspo.order
        ;
      `);

      try {
        const scoringPolicyRules = scoringPolicyRef.get(+itemId)
        const scales_map = scoringPolicyRules?.scales_map
        if (scales_map){
          for (let so of scoreOptions){
            so.slug = scales_map[so.slug] || so.slug
          }
        }
      }
      catch(e){
        console.log('Failed to parse slug remappings from scoring policy')
      }

      const isNonScoredProfile =  scoreOptions.filter(option => option.is_non_scored_profile == 1).length > 0;
      const itemRules = await dbRawRead(this.app, [itemId], SQL_ITEM_RULES);

      const flagOptions = await dbRawRead(this.app, [itemId], `
        select mspf.id, mspf.order, mso.slug, mso.caption, mso.report_msg, mso.is_top_flag, mso.is_comment_req, mso.is_pre_score_flag, mso.slug_code
        from marking_window_items mwi
        join marking_score_profile_flags mspf
          on mspf.score_profile_id = mwi.score_profile_id
          -- and mspf.is_revoked = 0
        join marking_score_options mso
          on mso.id = mspf.score_option_id
        where mwi.id = ?
        order by mspf.order
        ;
      `);

      const scoringTask = await dbRawReadSingle(this.app, [batchGroup.uid, itemId, AssignedItemComponentType.SCORING], SQL_SCORER_SCORING_TASK);

      return {
        task_id: scoringTask?.id,
        windowItemId: itemId,
        currentBatch: mwiBatch,
        item_slug: mwiBatch.item_slug,
        mspg_order: mwiBatch.mspg_order,
        msp_order: mwiBatch.msp_order,
        responses,
        scores,
        scoreOptions,
        flagOptions,
        scorerHistory,
        availableMarkers,
        itemRules,
        isNonScoredProfile,
        hasResponseScan
      }
    }));

    let assignedItems = parsedBatchInfo;
    assignedItems = _.orderBy(assignedItems, ['item_slug', 'mspg_order', 'msp_order'])

    return assignedItems;
  }

  async create (data: Data, params?: Params): Promise<Data> {    
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    let uid = await currentUid(this.app, params);

    const {mcb_id} = <any> data
    if (mcb_id){
      const mcbRecord = await dbRawReadSingle(this.app, {mcb_id}, `
        select id, uid 
        from marking_claimed_batches mcb 
        where mcb.id = :mcb_id
      `)
      if (mcbRecord && mcbRecord.uid){
        uid = mcbRecord.uid
      }
    }

    return await this.app.service('db/write/marked-claimed-batches-groups').create({created_by_uid: uid});
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Called when a response batch is submitted, and updates the batch groups availability.
   * Assigns the designated batch group ID to an MCBR record, only if the response does not already have a batch group ID.
   * @param id MCBR ID to patch
   * @param data claimed_batch_group_id: The batch group ID to assign
   * @param params 
   * @returns Patch info
   */
  async patch (id: number, data: {claimed_batch_group_id: number}, params?: Params): Promise<Data> {
    const response = await this.app.service('db/read/marking-claimed-batch-responses').get(id);
    if(!response) {
      throw new Errors.NotFound('RESPONSE_NOT_FOUND');
    }
    if(response.claimed_batch_group_id == null) {
      response.claimed_batch_group_id = data.claimed_batch_group_id
    } else {
      await this.app.service('db/write/marked-claimed-batches-groups').patch(response.claimed_batch_group_id, {
        is_available_to_scorers: 0
      })
    }

    return await this.app.service('db/write/marking-claimed-batch-responses').patch(id, response)
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
