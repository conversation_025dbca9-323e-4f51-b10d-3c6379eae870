import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../../util/db-raw';
import { SQL_AVAIL_SCORE_RESPONSE, SQL_ITEM_SCORER_VALIDITY_RESPONSES, SQL_SCORER_SCORING_TASK, SQL_ITEM_SYNC_BATCH, SQL_RESCORE_RESPONSE, SQL_ITEM_GROUP_TO_TAQR_ID, SQL_EXISTING_BATCH_GROUP, SQL_GET_MWI, SQL_TAQR_TO_ITEM_GROUP_TO_TAQR_ID, SQL_MCBR_ALREADY_CLAIMED, SQL_SCORE_PROFILE_INIT_SUPRESSION } from '../../../../../sql/scoring';
import { AssignedItemComponentType, AssignedItemStatus } from '../../summary/types/assigned-tasks';
import { currentUid } from '../../../../../util/uid';
import { ISchResp } from '../available/available.class'
import { Errors } from '../../../../../errors/general';
import { TEMP_DISABLE_VALIDITY_CLAIM } from '../../../../../constants/site-flags';
import { GeneralError } from '@feathersjs/errors';
import { READ_RULE_ADJ } from '../../../scor-lead/process-scoring-fourth-reads/process-scoring-fourth-reads';
const _ = require('lodash');

interface IMarkingWindow{
  is_rescore : number;
}

interface Data {}

interface ServiceOptions {}

export interface IExistingBatchGroup {
  id: number
, window_item_id: number
, claimed_batch_group_id : number
, is_scale_rescore_supressed: number
, read_rule_type?: string
, read_id: number
}

interface ISubmitRule{
  score_rule: number, 
  flag_rule:number
}
interface IMwiMarked{
  mwi_marked: number[],
  mwi_flagged: number[]
}
export interface ISuppressionCondition{
  accommodation_slug: string[]
}
export class Claim implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: number, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    // const uid = await currentUid(this.app, params);
    // const responses = await this.findClaimBatches(id, uid);
    // const numResponses = responses.length
    // const numBatches = Math.ceil(numResponses / 2) // todo: this should be pulling based on number of batches
    // return {numResponses, numBatches}
    return {numResponses:10, numBatches:1} // todo: make more efficient before restoring
  }

  async create (data: Data, params?: Params): Promise<Data> {
    // claim new batches
    if (params) {
      const uid = await currentUid(this.app, params);
      const {window_item_id, num_batch_claim, batchSizeOverride} = <any> data;
      return this.claimBatches(num_batch_claim, window_item_id, uid, batchSizeOverride);
    }
    // identify batches available to claim
    return data;
  }

  async findClaimBatches(window_item_id:number, uid:number) {
    const mwi = await dbRawReadSingle(this.app, {mwi_id: window_item_id}, SQL_GET_MWI);
    const marking_window_id = mwi.marking_window_id;
    let responses = await dbRawRead(this.app, {uid:uid, mwi_id:window_item_id, marking_window_id}, SQL_AVAIL_SCORE_RESPONSE);
    return responses;
  }

  async claimBatches(num_batch_claim:number, window_item_id:number, uid:number, batchSizeOverride?:number){
    let isAlreadyClaimed = false;
    let loopCount = 0;
    const scoringTasks = await dbRawRead(this.app, [uid, window_item_id, AssignedItemComponentType.SCORING], SQL_SCORER_SCORING_TASK);
    const scoringTask = scoringTasks[0];
    const { isPairedMarking, activeUid, readOnlyUids} = await this.app.service('public/scor-scor/summary').getPairedMarkingStatus([window_item_id], scoringTask.marking_pair_id, uid)
    const pairingUids = [activeUid, ...readOnlyUids];
    // Calls the sync_batches mwi's, which is why the UID gets a random one. 
    
    do {
      loopCount++;
      if(loopCount == 3) {
        throw new Errors.GeneralError('MAX_CLAIM_RETRY_REACHED');
      }    

      await this.app.service('public/scor-scor/batches/available').checkScorerAccess(uid, window_item_id, false);
      
      const batchesAvail = await this.app.service('public/scor-scor/batches/available').calculateNewBatchesAvailable(uid, window_item_id)
      const {max_batch_num_claim, batchesRemaining, respBySchMap, dissalowedSchools, batch_validity_num} = batchesAvail;
      let batch_size = batchesAvail.batch_size;
      if (batchSizeOverride){
        batch_size = batchSizeOverride
      }
      num_batch_claim = Math.min(Math.min(num_batch_claim, max_batch_num_claim), batchesRemaining);
      if (num_batch_claim == 0){
        throw new Errors.Forbidden('NO_BATCHES_REMAINING')
      }

      type TaqrID = number;
      type ResponseOption = {taqr_id:TaqrID, schl_group_id?:number, student_accommodation_cache?: string, response_selection_id?:number, is_validity?:number, is_invalid?:number, is_nr?:number, claimed_batch_group_id?: number, is_rescore_indic?: number, is_scale_rescore_supressed?:number, is_scale_supressed?:number, read_rule_type?:string};
      const mwi = await dbRawReadSingle(this.app, {mwi_id: window_item_id}, SQL_GET_MWI);
      const marking_window_id = mwi.marking_window_id;
      const marking_window  : IMarkingWindow = await dbRawReadSingle(this.app, {marking_window_id}, `
        select mw.is_rescore 
        from marking_windows mw
        where mw.id = :marking_window_id
        `);
      const is_rescore = marking_window.is_rescore;
      let responsesOrdered:ResponseOption[] = await dbRawRead(this.app, {uid:uid, mwi_id:window_item_id, schl_group_id:dissalowedSchools, marking_window_id}, SQL_AVAIL_SCORE_RESPONSE);
      responsesOrdered = responsesOrdered.filter(r => (!r.is_invalid && !r.is_nr))
      if (responsesOrdered.length == 0){
        throw new Errors.Forbidden('NO_BATCHES_REMAINING')
      }
      const responses:ResponseOption[] = _.shuffle(responsesOrdered);

      // pull validity items
      let validityResponses:ResponseOption[] = [];
      if (!TEMP_DISABLE_VALIDITY_CLAIM){
        let validityResponses:ResponseOption[] = await dbRawRead(this.app, [window_item_id, uid], SQL_ITEM_SCORER_VALIDITY_RESPONSES);
        const validityGap = (batch_validity_num * num_batch_claim) - validityResponses.length;
        if (validityGap > 0){
          // to do: reset if cant find any more unclaimed 
          const additionalValidity:any[] = [];
          // pull again
          validityResponses = validityResponses.concat(additionalValidity)
        }
        validityResponses = _.shuffle(validityResponses);
      }

      // going to keep it simple and just pre-allocate all the batches and assign all at once
      const batches:ResponseOption[][] = [];
      let currentBatch:ResponseOption[] = [];
      for (let i=0; i<responses.length; i++){
        const response = responses[i];
        if (currentBatch.length === 0){
          for (let j=0; j<Math.min(validityResponses.length, batch_validity_num); j++){
            const validityResponse = validityResponses[0];
            validityResponses.splice(0,1);
            currentBatch.push({
              taqr_id: validityResponse.taqr_id, 
              response_selection_id: validityResponse.response_selection_id,
              is_validity: 1,
            });
          }
        }
        let schResp;
        if (response.schl_group_id){
          schResp = respBySchMap.get(response.schl_group_id);
        }
        if (schResp && schResp.num_resp_rem){
          currentBatch.push(response);
          schResp.num_resp_rem = (schResp.num_resp_rem || 0) - 1; // reduce allowed allocation by one
        }
        if (currentBatch.length >= batch_size){
          batches.push(_.shuffle(currentBatch));
          currentBatch = [];
          if (batches.length >= num_batch_claim){
            break;
          }
        }
      }
      if (currentBatch.length){
        batches.push(currentBatch);
      }

      // get associated window items (for multiple criteria)
      let syncWindowItems = await dbRawRead(this.app, [window_item_id], SQL_ITEM_SYNC_BATCH);
      const syncWindowItemIDs: number[] = syncWindowItems.map(item => item.id);
      const mwi_ids = syncWindowItems.map(item => item.id);
      const windowItemsSupressionMap: Map<number, ISuppressionCondition> = await this.getSuppressionMap(mwi_ids);
      // For invalidating
      const taqrIds: number[] = [];
      const mcbrIds: number[] = [];
      const mcbIds: number[] =[];
      const mcbpdIds: number[][] =[];
      const mcbgIds: number[] = [];
      try{
        // make all of the writes
        for (let i = 0; i < batches.length; i++){
          const selectedResponses = batches[i];
          const batchIds = [];
          // Go through each response
          for(let k = 0; k < selectedResponses.length; k++) {
            // Initialize response info
            let {
              taqr_id, 
              schl_group_id,
              response_selection_id,
              is_validity,
              student_accommodation_cache
            } = selectedResponses[k];
            taqrIds.push(taqr_id);
            const batchGroupMap = new Map<number, IExistingBatchGroup>();
            const { useInvalidatedRead, existingBatchGroup} : {useInvalidatedRead: boolean; existingBatchGroup: IExistingBatchGroup[];}  = await this.getExistingBatchGroup(taqr_id, syncWindowItemIDs);
            const ignoreReadRuleSuppression = useInvalidatedRead;
            let batchGroupId;
            if(existingBatchGroup && existingBatchGroup.length > 0) {
              for(let group of existingBatchGroup) {
                if(!syncWindowItemIDs.includes(group.window_item_id)) {
                  throw new Errors.GeneralError('MISSING_EXISTING_MWI_RECORD');
                }
                batchGroupMap.set(group.window_item_id, group);
              }
              const groupMwis: number[] = existingBatchGroup.map(group => group.window_item_id);
              for(let windowItemId of syncWindowItemIDs) {
                if(!groupMwis.includes(windowItemId)) {
                  throw new Errors.GeneralError(`MISSING_SCALES ${existingBatchGroup}`);
                }
              }
            } else {
              const batchGroup = await this.app.service('db/write/marked-claimed-batches-groups').create({created_by_uid: uid});
              batchGroupId = batchGroup.id;
              mcbgIds.push(batchGroupId);
            }
            const markerReadId = (await this.app.service('db/write/marking-scorer-reads').create({created_by_uid: uid})).id;
            //Go through each scale
            for (let j = 0; j < syncWindowItemIDs.length; j++) {
              const markingWindowItemId = syncWindowItemIDs[j];
              if(k == 0) {
                const newBatch = await this.app
                  .service('db/write/marking-claimed-batches')
                  .create({
                    marking_window_item_id: markingWindowItemId,
                    component_type: 'SCORING',
                    batch_size: selectedResponses.length, // instead of batch_size because there might be partial claims
                    uid,
                  })
                batchIds.push(newBatch.id);
                mcbIds.push(newBatch.id);
                if(isPairedMarking && readOnlyUids.length > 0 && activeUid) {
                  const newBatchPDIds: number[] = [];
                  for(const uid of pairingUids){
                    const newBatchPairedDraft = await this.app
                    .service('db/write/marking-claimed-batches-paired-draft')
                    .create({
                      marking_claimed_batch_id: newBatch.id,
                      uid,
                      is_marked: 0,
                    })
                    newBatchPDIds.push(newBatchPairedDraft.id)
                  }
                  mcbpdIds.push(newBatchPDIds);
                }
              }
              const claimed_batch_id = batchIds[j];
              const claimed_batch_paired_draft_id = mcbpdIds[j];

              const mwiTaqr = await dbRawReadSingle(this.app, {taqr_id, markingWindowItemId}, SQL_TAQR_TO_ITEM_GROUP_TO_TAQR_ID);
              if(!mwiTaqr || !mwiTaqr.taqr_id) {
                throw new Errors.GeneralError('MISSING_TAQR_GROUP')
              }
              const currentMwiTaqrId = mwiTaqr.taqr_id;
              
              let claimed_batch_group_id = null;
              let is_scale_rescore_supressed = 0;
              let ruleApplied;
              let read_id = 1;
              let is_prorated = 0;

              if(existingBatchGroup && existingBatchGroup.length > 0 && !ignoreReadRuleSuppression) {
                const existingRecRescore = batchGroupMap.get(markingWindowItemId);
                if(!existingRecRescore) {
                  // This is the main cause of broken claims with missing scales. Stops the process and keeps the broken scales.
                  throw new Errors.GeneralError('MISSING_EXISTING_MWI_RECORD');
                }
                claimed_batch_group_id = existingRecRescore.claimed_batch_group_id;
                is_scale_rescore_supressed = existingRecRescore.is_scale_rescore_supressed;
                ruleApplied = existingRecRescore.read_rule_type
                read_id = existingRecRescore.read_id + 1;
              } 
              else {
                if(existingBatchGroup.length > 0){
                  batchGroupId = existingBatchGroup[0].claimed_batch_group_id;
                }
                claimed_batch_group_id = batchGroupId;
              }

              let from_read_rule = ''
              // todo:DB_MODEL should not be hard coded like this
              if (ruleApplied){
                if (ruleApplied == READ_RULE_ADJ){
                  from_read_rule = 'FOURTH_READ_RULE_TYPE'
                }
                else {
                  from_read_rule = 'THIRD_READ_RULE_TYPE'
                }
              }


              // Check accommodation
              const supressionConditions = windowItemsSupressionMap.get(markingWindowItemId);
              if(supressionConditions && student_accommodation_cache){
                is_prorated = await this.suppressionCheck(supressionConditions, student_accommodation_cache);
                if(is_prorated){
                  is_scale_rescore_supressed = 1;
                }
              }

              const newMCBR = await this.app
              .service('db/write/marking-claimed-batch-responses')
              .create({
                claimed_batch_id, 
                window_item_id: markingWindowItemId,
                taqr_id: currentMwiTaqrId,
                schl_group_id,
                response_selection_id,
                is_validity,
                claimed_batch_group_id,
                is_scale_supressed: is_scale_rescore_supressed,
                is_scale_rescore_supressed: is_scale_rescore_supressed,
                read_id,
                marker_read_id: markerReadId,
                from_read_rule,
                is_prorated,
                is_rescore
              })

              mcbrIds.push(newMCBR.id);

              if(isPairedMarking && readOnlyUids.length > 0 && activeUid) {
                for(let m = 0; m < pairingUids.length; m++){
                  const newMCBRPD = await this.app
                  .service('db/write/marking-claimed-batch-responses-paired-draft')
                  .create({
                    claimed_batch_paired_draft_id: claimed_batch_paired_draft_id[m],
                    batch_response_id: newMCBR.id,
                    window_item_id: markingWindowItemId,
                  })
                }
              }
            }
          }
        }

        isAlreadyClaimed = await this.isAlreadyClaimed(window_item_id, taqrIds, mcbrIds);

        if(mcbIds.length != syncWindowItemIDs.length) {
          isAlreadyClaimed = true;
        }

        if(isAlreadyClaimed) {
          await this.invalidateResponses(mcbIds, mcbrIds, mcbgIds);
        }

      } catch (err: any) {
        // invalidate everything if any error is thrown
        isAlreadyClaimed = true;
        await this.invalidateResponses(mcbIds, mcbrIds, mcbgIds);
        this.logError(err);
      }
    } while (isAlreadyClaimed);
    
    return {};
  }
  async getExistingBatchGroup(taqr_id: number, mwi_ids: number[]){
    let useInvalidatedRead = false
    let existingBatchGroup: IExistingBatchGroup[] = await dbRawRead(this.app, {taqr_id, mwi_ids:mwi_ids}, SQL_EXISTING_BATCH_GROUP(useInvalidatedRead))
    if(!(existingBatchGroup.length > 0)){
      useInvalidatedRead = true;
      existingBatchGroup = await dbRawRead(this.app, {taqr_id, mwi_ids:mwi_ids}, SQL_EXISTING_BATCH_GROUP(useInvalidatedRead))
    }
    return {
      useInvalidatedRead,
      existingBatchGroup
    };
  }
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async getSuppressionMap(mwiIds: number[]){
    const scoreProfilesSupressionRaw = await dbRawRead(this.app, { mwiIds }, SQL_SCORE_PROFILE_INIT_SUPRESSION);
    let windowItemsSupressionMap = new Map<number, ISuppressionCondition>();
    for (const scoreProfilesSupression of scoreProfilesSupressionRaw){
       const condition: ISuppressionCondition = await JSON.parse(scoreProfilesSupression.supress_condition)
       windowItemsSupressionMap.set(scoreProfilesSupression.mwi_id, condition);
    }
    return windowItemsSupressionMap;
  }
  
  async invalidateResponses(mcbIds: number[], mcbrIds: number[], mcbgIds: number[]) {
    if(mcbIds.length > 0) {
      await dbRawWrite(this.app, {mcbIds}, `
        UPDATE marking_claimed_batches
        SET
            is_revoked = 1
          , revoked_on = CURRENT_TIMESTAMP()
        WHERE id in (:mcbIds);
      `);
    }

    if(mcbrIds.length > 0) {
      await dbRawWrite(this.app, {mcbrIds}, `
        UPDATE marking_claimed_batch_responses
        SET
            is_invalid = 1
          , invalidated_on = CURRENT_TIMESTAMP()
          , is_revoked = 1
          , revoked_on = CURRENT_TIMESTAMP()
        WHERE id in (:mcbrIds);
      `);
    }

    // if(mcbgIds.length > 0) {
    //   await dbRawWrite(this.app, {mcbgIds}, `
    //     UPDATE marked_claimed_batches_groups
    //     SET
    //         is_revoked = 1
    //       , revoked_on = CURRENT_TIMESTAMP()
    //     WHERE id in (:mcbgIds);
    //   `);
    // }
  }

  async isAlreadyClaimed(windowItemId: number, taqrIds: number[], mcbrIds: number[]) {
    const alreadyClaimed = await dbRawRead(this.app, {mcbrIds}, SQL_MCBR_ALREADY_CLAIMED) // no longer using: windowItemId: markingWindowId.marking_window_id, taqrIds
    return alreadyClaimed && alreadyClaimed.length > 0;
  }

  logError(err: any) {
    console.log('ERROR_FOUND', err.message);
  }
  async validateBatchCompletion(responseBatchIDs :any, batchGroupIds : any[]){
    
    const mwiMarkedMap : Map<number, IMwiMarked>= new Map();
    const submitRuleMap : Map<number,  ISubmitRule[]>= new Map();
    const claimed_batch_group_ids = batchGroupIds.map(batchGroupId => batchGroupId.id)
    // 1. Initialize Map
    claimed_batch_group_ids.map(claimed_batch_group_id => {
      mwiMarkedMap.set(claimed_batch_group_id, {mwi_marked:[], mwi_flagged:[]})
      submitRuleMap.set(claimed_batch_group_id, [])
    });
    // 2. Get submit rules
    const scoreRules = await dbRawRead(this.app, {claimed_batch_group_ids: claimed_batch_group_ids, claimed_batch_ids: responseBatchIDs}, `
      select 
        mcbr.claimed_batch_group_id claimed_batch_group_id
        , mwi.id score_rule
        , mwi.sync_batches_to_wmi_id flag_rule
        , mwi.group_to_mwi_id, mwi.slug 
      from marking_claimed_batch_responses mcbr 
      join marking_window_items mwi 
        on mwi.id = mcbr.window_item_id
	    join marking_score_profiles msp 
	      on msp.id = mwi.score_profile_id 
	    and msp.is_non_scored_profile = 0
      where claimed_batch_group_id in (:claimed_batch_group_ids)
        and claimed_batch_id in (:claimed_batch_ids)
      ;
    `);
    
    // 3. Find MCBRS
    const mcbrs = await dbRawRead(this.app, {claimed_batch_group_ids: claimed_batch_group_ids, claimed_batch_ids: responseBatchIDs}, `
      select 
        mcbr.id mcbr_id, 
        mcbr.claimed_batch_group_id claimed_batch_group_id, 
        mcbr.window_item_id mwi_id, 
        GREATEST(mcbr.is_marked , mcbr.is_scale_supressed) as is_marked 
      from marking_claimed_batch_responses mcbr 
        where claimed_batch_group_id in (:claimed_batch_group_ids)
        and claimed_batch_id in (:claimed_batch_ids)
      ;
    `);
    // 4. Find MRS
    const mrss = await dbRawRead(this.app, {claimed_batch_group_ids: claimed_batch_group_ids, claimed_batch_ids: responseBatchIDs}, `
    SELECT 
      mcbr.id mcbr_id, 
      mcbr.claimed_batch_group_id claimed_batch_group_id, 
      mcbr.claimed_batch_id,
      mcbr.window_item_id mwi_id, 
      mrs.score_flag_id score_flag_id 
      FROM marking_claimed_batch_responses mcbr
      join marking_response_scores mrs 
        on mrs.batch_response_id = mcbr.id
      where mcbr.claimed_batch_group_id in (:claimed_batch_group_ids)
      and mcbr.claimed_batch_id in (:claimed_batch_ids)
      ;`);
    // 5. Map Items
    scoreRules.map(rule => {
      const score_rule = rule.score_rule;
      const flag_rule = rule.flag_rule;
      submitRuleMap.get(rule.claimed_batch_group_id)?.push({score_rule, flag_rule})
    })
    mcbrs.map(mcbr => {
      if(mcbr.is_marked && mcbr.is_marked == 1){
        mwiMarkedMap.get(mcbr.claimed_batch_group_id)?.mwi_marked.push(mcbr.mwi_id)
      }
    })
    mrss.map(mrs => {
      if(mrs.score_flag_id && mrs.score_flag_id > 0){
        mwiMarkedMap.get(mrs.claimed_batch_group_id)?.mwi_flagged.push(mrs.mwi_id)
      }
    })
    
    // 6. Check each rules
    for(const claimed_batch_group_id of claimed_batch_group_ids){
      await this.verifySubmitRule(submitRuleMap.get(claimed_batch_group_id) || [], mwiMarkedMap.get(claimed_batch_group_id) || {mwi_marked:[], mwi_flagged:[]})
    }
  }
  
  async verifySubmitRule( rules: {score_rule: number, flag_rule:number}[], mcbr: {mwi_marked: number[],mwi_flagged: number[]}){
    for(const rule of rules){
      if(!mcbr.mwi_marked.includes(rule.score_rule) && !mcbr.mwi_flagged.includes(rule.flag_rule))
      {
        throw new GeneralError("SCALE_NOT_FINISHED");
      }
    }
  }
  
  async suppressionCheck(supressionCondition: {accommodation_slug:any[]}, student_accommodation_cache: string){
    if(student_accommodation_cache){  
      const student_accommodations = JSON.parse(student_accommodation_cache);
      let matchCondition = 0;
      if(student_accommodations.length > 0){
        student_accommodations.forEach((student_accommodation: any)=>{
          for (const [key, value] of Object.entries(supressionCondition)) {
            if(value.includes(student_accommodation[key])){
              matchCondition = 1;
            }
          }
        })
      }
      return matchCondition;
    }
    return 0;
  }
  
  async patch (id: NullableId, data: Data, params: Params): Promise<Data> {
    // mark batch as completed
    // to do: check that it is actually completed
    const {isFinalCloser, mcbrIds, responseBatchIDs, is_paired_draft} = <any> data; // only for final closer
    if (isFinalCloser){
      return <any> this.finalClosing(mcbrIds)
    }

    const uid = await currentUid(this.app, params);

    if(is_paired_draft) {
      await this.submitPairedDraft(responseBatchIDs, uid);
      return data;
    }

    const batchGroupIds = await dbRawRead(this.app, [responseBatchIDs[0]], `
      select mcbg.id from marking_claimed_batch_responses mcbr
      join marked_claimed_batches_groups mcbg 
        on mcbg.id = mcbr.claimed_batch_group_id 
      where mcbr.claimed_batch_id = ?
      ;
    `);

    await this.validateBatchCompletion(responseBatchIDs, batchGroupIds);
    
    const mcbgIds = batchGroupIds.map((group) => group.id);
    if(mcbgIds && mcbgIds.length > 0) {
      await dbRawWrite(this.app, {mcbgIds}, `
        UPDATE marked_claimed_batches_groups
        SET
          is_available_to_scorers = 0
        WHERE id in (:mcbgIds);
      `);
    }
    if(responseBatchIDs && responseBatchIDs.length > 0) {
      await dbRawWrite(this.app, {responseBatchIDs}, `
        UPDATE marking_claimed_batches
        SET
            is_marked = 1
          , completed_on = CURRENT_TIMESTAMP()
        WHERE id in (:responseBatchIDs);
      `);
    }

    await this.handleUNF(responseBatchIDs);
    return data;
  }

  async submitPairedDraft(responseBatchIDs: number[], uid: number) {
    // todo bring score validation logics to paired draft
    if(responseBatchIDs && responseBatchIDs.length > 0) {
      await dbRawWrite(this.app, {responseBatchIDs, uid}, `
        UPDATE marking_claimed_batches_paired_draft mcbpd 
        SET
            is_marked = 1
          , completed_on = CURRENT_TIMESTAMP()
        WHERE marking_claimed_batch_id in (:responseBatchIDs)
          AND uid = :uid
        ;
      `);
    }
  }

  async handleUNF(responseBatchIDs: number[]) {
    if(!responseBatchIDs || responseBatchIDs.length <= 0) {
      return [];
    }

    // Find submitted batch groups with UNF
    const UNFBatches = await this.getUNFBatches(responseBatchIDs);

    if(UNFBatches.length == 0) {
      return;
    }

    const UNFSubmissions = await this.getUNFSubmissions(responseBatchIDs, UNFBatches);

    if(UNFSubmissions.length == 0) {
      return;
    }
    let marking_claimed_batch_responses: any;
    // Get unf repools count
    marking_claimed_batch_responses = await dbRawReadSingle(this.app, {responseBatchIDs}, `
      select MIN(mbap.max_unf_repools) max_unf_repools
      from marking_claimed_batch_responses mcbr 
      join marking_window_items mwi 
        on mcbr.window_item_id = mwi.id
      join marking_batch_alloc_policies mbap 
        on mwi.batch_alloc_policy_id = mbap.id
      where mcbr.claimed_batch_group_id > 0
        and mcbr.claimed_batch_id in (:responseBatchIDs)
    `)
    if(!marking_claimed_batch_responses){
      throw new Errors.GeneralError("BATCH_ALLOCATION_POLICIES_NOT_FOUND.");
    }
    // Repool responses that have less than 4 UNF reads
    const responsesReqRepool = UNFSubmissions.filter((res) => res.unf_count < marking_claimed_batch_responses.max_unf_repools);
    const mcbgIdsReqRepool: number[] = responsesReqRepool.map((res) => res.claimed_batch_group_id);
    if(mcbgIdsReqRepool.length > 0) {
      const data = {
        sendToTarget: 'POOL',
        batchGroupIds: mcbgIdsReqRepool,
        supervisorNote: null,
      }
      await this.app.service('public/scor-lead/response-repooling').create(data);
    }

    await this.handleOBS(UNFSubmissions, marking_claimed_batch_responses.max_unf_repools);
  }

  async handleOBS(UNFSubmissions: {claimed_batch_group_id: number, obs_id: number, max_read_id: number, unf_count: number, }[], max_unf_repools:number) {
    // Flag responses as OBS
    const responseReqOBS = UNFSubmissions.filter((res) => res.unf_count >= max_unf_repools); // UNF count of 4 requires OBS

    if(responseReqOBS.length <= 0) {
      return;
    }

    const mcbgIds = responseReqOBS.map((res) => res.claimed_batch_group_id);
    const markerReadIds = responseReqOBS.map((res) => res.max_read_id);
    const obsId = UNFSubmissions[0].obs_id; // All should share same OBS_ID as the first one because same assessment

    // Invalidate all flagged reads except for the current read ID
    await this.app.service('public/scor-lead/response-repooling').invalidateFlaggedReads(mcbgIds, markerReadIds);

    const responsesToUpdate = await dbRawRead(this.app, {markerReadIds}, `
      -- Find marking response scores to update
      select mrs.id from marking_claimed_batch_responses mcbr
      join marking_window_items mwi
        on mwi.id = mwi.sync_batches_to_wmi_id 
        and mwi.id = mcbr.window_item_id
      join marking_response_scores mrs 
        on mrs.batch_response_id = mcbr.id
      where marker_read_id in (:markerReadIds);
    `)

    if(!responsesToUpdate || responsesToUpdate.length <= 0) {
      throw new Errors.GeneralError('NO_RESP_FOUND_TO_UPDATE')
    }
    if(!obsId) {
      throw new Errors.GeneralError('OBS_NOT_IN_SCORE_PROFILE')
    }
    
    const mrsIdsToUpdate = responsesToUpdate.map((res) => res.id);
    await dbRawWrite(this.app, {mrsIdsToUpdate, obsId}, `
      UPDATE marking_response_scores
      SET
          score_flag_id = :obsId
        , last_touched_on = CURRENT_TIMESTAMP
      WHERE id in (:mrsIdsToUpdate);
    `);
  }

  async getUNFBatches(responseBatchIDs: number[]): Promise<number[]> {
    const UNFBatches = await dbRawRead(this.app, {responseBatchIDs}, `
      select distinct mcbr.claimed_batch_group_id from marking_claimed_batch_responses mcbr 
        join marking_response_scores mrs 
          on mrs.batch_response_id = mcbr.id
        join marking_score_profile_flags mspf 
          on mspf.id = mrs.score_flag_id 
              join marking_score_options mso 
          on mso.id = mspf.score_option_id 
          and mso.slug_code = 'UNF'
      where mcbr.claimed_batch_id in (:responseBatchIDs);
    `)

    if(!UNFBatches || UNFBatches.length == 0) {
      return [];
    }

    return UNFBatches.map((batch) => batch.claimed_batch_group_id);
  }

  /**
   * Helper function for getting batch groups that have been marked as UNF
   */
  async getUNFSubmissions(responseBatchIDs: number[], UNFBatchGroupIds: number[]) {
    if(!responseBatchIDs || responseBatchIDs.length <= 0) {
      return [];
    }

    const UNFSubmissions: {claimed_batch_group_id: number, obs_id: number, max_read_id: number, unf_count: number}[] = await dbRawRead(this.app, {responseBatchIDs, UNFBatchGroupIds}, `
      select    mcbr.claimed_batch_group_id
              , mspf2.id obs_id
              , MAX(mcbr2.marker_read_id) max_read_id
              , count(distinct mcbr2.marker_read_id) unf_count 
      from marking_claimed_batch_responses mcbr 
      join marking_claimed_batch_responses mcbr2 
        on mcbr2.claimed_batch_group_id = mcbr.claimed_batch_group_id
        AND mcbr2.is_revoked = 0
      join marking_window_items mwi on mwi.id = mcbr2.window_item_id 
      join marking_response_scores mrs 
        on mrs.batch_response_id = mcbr2.id
      join marking_score_profile_flags mspf 
        on mspf.id = mrs.score_flag_id 
      left join marking_score_profile_flags mspf2 
        on mspf2.score_profile_id = mwi.score_profile_id
        and mspf2.score_option_id = 22 -- getting ID for OBS
      join marking_score_options mso 
        on mso.id = mspf.score_option_id 
        and mso.slug_code = 'UNF'
      where mcbr.claimed_batch_id in (:responseBatchIDs)
       and mcbr.claimed_batch_group_id in (:UNFBatchGroupIds)
      group by mcbr2.claimed_batch_group_id;
    `);

    if(!UNFSubmissions || UNFSubmissions.length == 0) {
      return [];
    }

    return UNFSubmissions;
  }

  async finalClosing(mcbIds:number[]){
    return {}
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    const mcb_id = id;
    await this.revokeBatch(mcb_id)
    return {}
    
  }

  async revokeBatch(mcb_id:NullableId){
    const mcbrRecords = await dbRawRead(this.app, {mcb_id}, `
      select id, is_marked, is_revoked, is_invalid, invalidated_on, invalidation_note  
      from marking_claimed_batch_responses mcbr 
      where mcbr.claimed_batch_id = :mcb_id
    `)

    for (let record of mcbrRecords){
      this.app.service('db/write/marking-claimed-batch-responses').patch(record.id, {
        is_marked: 0, 
        is_revoked: 1, 
        revoked_on: dbDateNow(this.app), 
        is_invalid: 1, 
        invalidated_on: dbDateNow(this.app), 
        invalidation_note: 'claim glitch'
      })
    }

    this.app.service('db/write/marking-claimed-batches').patch(mcb_id, {
      is_marked: 0, 
      is_revoked: 1, 
      revoked_on: dbDateNow(this.app), 
    })
  }
}
