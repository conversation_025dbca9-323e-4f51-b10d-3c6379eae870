import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../../util/db-raw';
import { SQL_BATCH_RESP_SCORES, SQL_ITEM_SCORE_OPTIONS, SQL_ITEM_SCORE_FLAGS, SQL_RESPONSE_STATE, SQL_SCORE_OPTION_VAL, SQL_SCORER_SCORING_TASK, SQL_ITEM_RULES, SQL_PREV_READS, SQL_BATCH_RESP_SCORE_ENTRY_BY_ID, SQL_BATCH_RESP_RESPONSES, SQL_LEADER_SUPER_BY_MCBR_AND_UID, SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID, SQL_IS_TAQR_PAPER_FORMAT, SQL_BATCH_RESP_SCORES_PAIRED_DRAFT, SQL_BATCH_RESP_SCORE_ENTRY_BY_ID_PAIRED_DRAFT, SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID_PAIRED_DRAFT, SQL_BATCH_RESP_SCORES_PAIRED_DRAFT_ALL_STAGE_1, SQL_ITEMS_BATCH_POLICIES} from '../../../../../sql/scoring';
import { dbDateNow } from '../../../../../util/db-dates';
import { arrToMap } from '../../../../../util/param-sanitization';
import { AssignedItemComponentType, AssignedItemStatus } from '../../summary/types/assigned-tasks';

interface Data {}

const SECRET_VALIDITY_KEY = '2547987894516556115465456456465156156487d4f654sd5fs1f6s87';

interface ServiceOptions {}

interface IMarkingResponseScores {
  id: number,
  score_option_id?: number,
  score_flag_id?: number,
  meta?: string
}

export class Mark implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const uid = await currentUid(this.app, params);
      let {window_item_id, window_item_ids, validity_key} = params.query;
      if(window_item_id && window_item_ids){
        throw new Errors.GeneralError('DUPLICATED_PARAMS')
      }
      // convert single id param into array
      if(window_item_id){
        window_item_ids = [window_item_id]
      }
      if(!window_item_ids || window_item_ids.length <= 0){
        throw new Errors.GeneralError('NO_PARAM')
      }
      let task_arr = []
      const isShowValidiity = (validity_key == SECRET_VALIDITY_KEY);
      // pull tasks
      const scoringTasks = await dbRawRead(this.app, [uid, window_item_ids, AssignedItemComponentType.SCORING], SQL_SCORER_SCORING_TASK);
      if(scoringTasks.length !== window_item_ids.length || scoringTasks.filter(batch => !(batch.status===AssignedItemStatus.NOT_AVAILABLE)).length !== scoringTasks.length){
        throw new Error('SCORING_NOT_AVAILABLE')
      }
      // validate window lock
      for (const window_item_id of window_item_ids){
        await this.app.service('public/scor-lead/windows').validateWindowLockByItem(window_item_id)
      }
      let scoringTask = scoringTasks[0];
      let batchOwnerUid = uid
      const pairedMarkingStatus = await this.app.service('public/scor-scor/summary').getPairedMarkingStatus(window_item_ids, scoringTask.marking_pair_id, uid)
      const { isPairedDraftStageCompleted, isPairedDraftCompleted }= await this.app.service('public/scor-scor/summary').getPairedDraftStatus(pairedMarkingStatus.isPairedMarking, window_item_ids, uid, pairedMarkingStatus.activeUid)
      if (scoringTask.marking_pair_id){
        batchOwnerUid = pairedMarkingStatus.activeUid
      }
      // list of responses available for marking (including the marking data), dynamically computing the "current batch"
      const claimedBatches = await this.app.service('public/scor-scor/batches/available').identifyBatchesClaimed(batchOwnerUid, window_item_ids)
      if (claimedBatches.length < window_item_ids.length){
        throw new Errors.BadRequest('NO_CLAIMED_BATCHES_AVAIL')
      }
      let maxBatchSize = 0;
      // pull responses, scores, and prevReads
      const batch_info = await Promise.all(claimedBatches.map(async (claimed_batch) => {
        const prevReads = await this.getPreviousScoresForBatch(claimed_batch.id)
        const responses = await dbRawRead(this.app, [claimed_batch.id], SQL_BATCH_RESP_RESPONSES)
        
        let scores;
        let paired_draft_scores = [];
        if (scoringTask.marking_pair_id && !isPairedDraftStageCompleted){
          scores = await dbRawRead(this.app, [claimed_batch.id, uid], SQL_BATCH_RESP_SCORES_PAIRED_DRAFT)
        }
        else {
          scores = await dbRawRead(this.app, [claimed_batch.id], SQL_BATCH_RESP_SCORES)
          if (scoringTask.marking_pair_id){
            paired_draft_scores = await dbRawRead(this.app, [claimed_batch.id], SQL_BATCH_RESP_SCORES_PAIRED_DRAFT_ALL_STAGE_1)
            await this.app.service('public/scor-lead/accounts').applyMarkerMetaData(paired_draft_scores, claimed_batch.marking_window_id, 'scorer_uid')
          }
        }
        const prevReads_at = await this.getPReviousScoresForAnotherTask(claimed_batch.id, window_item_ids)
        return {
          "mwi_id": claimed_batch.marking_window_item_id,
          prevReads,
          prevReads_at,
          responses,
          scores,
          paired_draft_scores
      }}))
      // load scoring profile
      const scoreOptionFullList = await dbRawRead(this.app, [window_item_ids], SQL_ITEM_SCORE_OPTIONS);
      const flagOptionFullList = await dbRawRead(this.app, [window_item_ids], SQL_ITEM_SCORE_FLAGS(false));
      const itemRuleFullList = await dbRawRead(this.app, [window_item_ids], SQL_ITEM_RULES);
      // check scoring access
      for (const key in scoringTasks){
        await this.app.service('public/scor-scor/batches/available').checkScoringAccess(scoringTasks[key].id, true);
      }
      // get relevant scoring policies
      const scoringPolicyRef = new Map();
      try {
        const mwi_ids = [... new Set(scoreOptionFullList.map(r => r.mwi_id))]
        const scoringPolicyRecords = await dbRawReadReporting(this.app, {mwi_ids}, SQL_ITEMS_BATCH_POLICIES)
        for (let sp of scoringPolicyRecords){
          scoringPolicyRef.set(+sp.mwi_id, JSON.parse(sp.read_rules));
        }
      }
      catch(e){
        console.log('Failed to pull scoring policies')
      }
      // mapping info into marking window items with ids
      for (const key in window_item_ids){
        const window_item_id = window_item_ids[key]
        const scoringTask = scoringTasks.filter((task)=> task.marking_window_item_id == window_item_id)[0]
        const currentBatch = claimedBatches.filter((batch)=> batch.marking_window_item_id == window_item_id)[0]
        const info = batch_info.filter((info)=> info.mwi_id == window_item_id)
        const prevReads = info[0].prevReads
        const prevReads_at = info[0].prevReads_at
        const responses = info[0].responses
        const scores = info[0].scores
        const paired_draft_scores = info[0].paired_draft_scores
        const flagOptions = flagOptionFullList.filter((option)=> option.mwi_id == window_item_id)
        const scoreOptions = scoreOptionFullList.filter((option)=> option.mwi_id == window_item_id)
        const itemRules = itemRuleFullList.filter((option)=> option.marking_window_item_id == window_item_id)
        let validityScoringKeys;
        try {
          const scoringPolicyRules = scoringPolicyRef.get(+window_item_id)
          const scales_map = scoringPolicyRules?.scales_map
          if (scales_map){
            for (let so of scoreOptions){
              so.slug = scales_map[so.slug] || so.slug
            }
          }
        }
        catch(e){
          console.log('Failed to parse slug remappings from scoring policy')
        }
        if (isShowValidiity){
          validityScoringKeys = await this.getValidityScoringKeys(window_item_id, responses);
        }
        maxBatchSize=Math.max(maxBatchSize, responses.length);
        task_arr.push({
          task_id: scoringTask.id,
          prevReads_at,
          currentBatch,
          prevReads,
          scoreOptions,
          flagOptions,
          responses,
          itemRules,
          validityScoringKeys, // normally undefined
          scores,
          paired_draft_scores
        })
      }

      let isAligned = true;
      type ResponseComparison = {claimed_batch_group_id: number, ta_id: number, taqr_id: number}
      for(let j=0; j<maxBatchSize; j++) { // Iterating through responses in the progress tracker
        const firstResponse: ResponseComparison  = task_arr[0].responses[j];
        if(firstResponse) {
          for(let i=1; i<task_arr.length; i++) {
              const response = task_arr[i].responses[j];
              if(response) {
                if(response.ta_id != firstResponse.ta_id) {
                  isAligned = false;
                }
                if(response.claimed_batch_group_id != firstResponse.claimed_batch_group_id) {
                  isAligned = false;
                }
              } else {
                isAligned = false;
              }
          }
        } else {
          isAligned = false;
        }
      }
      if(!isAligned) {
        throw new Errors.GeneralError('MISALIGNED_CLAIMED_BATCHES');
      }
      return task_arr;
    }
    throw new Errors.GeneralError('NO_PARAM')
  }

  async getPreviousScoresForBatch(claimed_batch_id:number){
    const batchResponseToPrevReads:any = {};
    try {

      type PrevClaimRecords = {
        mcbr_id: number,
        mwi_id: number,
        taqr_id : number,
        read_id: number,
        marker_number: string,
      }
      const records:PrevClaimRecords[] = await dbRawRead(this.app, {claimed_batch_id}, SQL_PREV_READS)
      for (let record of records){
        let arr = batchResponseToPrevReads[record.mcbr_id]
        if (!arr){
          arr = batchResponseToPrevReads[record.mcbr_id] = []
        }
        arr.push(record)
      }
    }
    catch (e){
      batchResponseToPrevReads.err = e
    }
    return batchResponseToPrevReads
  }
  async getPReviousScoresForAnotherTask(claimed_batch_id:number, window_item_ids: number[]){
    
    const mcbrTestAttemptRes = await dbRawRead(this.app, {claimed_batch_id, window_item_ids}, `
    select  mcbr.id mcbr_id
          , mtc.ta_id ta_id
          , mtc.marking_window_id
    from marking_claimed_batch_responses mcbr
    join marking_taqr_cache mtc 
        on mcbr.taqr_id = mtc.taqr_id
    where mcbr.claimed_batch_id = :claimed_batch_id
      and mtc.ta_id is not null
      and mtc.mwi_id in (:window_item_ids)
    group by mcbr.id, mtc.ta_id
    `)
    // separate queries and map at api to avoid taqr join query
    const testAttemptMcbrMap = new Map<number, number>()
    const taIds = mcbrTestAttemptRes.map((mcbrTestAttempt) => {
      testAttemptMcbrMap.set(mcbrTestAttempt.ta_id, mcbrTestAttempt.mcbr_id)
      return mcbrTestAttempt.ta_id
    });
    const marking_window_ids = mcbrTestAttemptRes.map(mcbrTestAttempt => mcbrTestAttempt.marking_window_id);
    taIds.push(-1)
    const testAttemptTaqrRes = await dbRawRead(this.app, {taIds, marking_window_ids}, `
      select  mtc.ta_id
            , mtc.taqr_id  
      from marking_taqr_cache mtc 
      where mtc.ta_id in (:taIds)
      and mtc.marking_window_id in (:marking_window_ids)
    `)
    const taqrMcbrMap = new Map<number, number>()
    const taqrIds = testAttemptTaqrRes.map((testAttemptTaqr) => {
      const mcbr_id = testAttemptMcbrMap.get(testAttemptTaqr.ta_id)
      taqrMcbrMap.set(testAttemptTaqr.taqr_id, mcbr_id || -1)
      return testAttemptTaqr.taqr_id
    });
    const mcbrPrevReadsMap: any = {};
    try{
      type prevRead = {
        mcbr_id: number,
        taqr_id: number,
        marker_number: number, 
        read_id: number,
        mwi_id: number
      }
      const prevReads_at: prevRead[] = await dbRawRead(this.app, {taqrIds, window_item_ids, marking_window_ids}, `
      select  mcbr.id mcbr_id
            , mcbr.taqr_id taqr_id
            , mwum.value marker_number
            , mcbr.read_id
            , mcbr.is_invalid is_invalid
            , mcb.marking_window_item_id mwi_id
      from marking_claimed_batch_responses mcbr 
      join marking_claimed_batches mcb
        on mcb.id = mcbr.claimed_batch_id
        and mcb.is_marked = 1
      join marking_window_items mwi
        on mwi.id = mcb.marking_window_item_id
        and mwi.id not in (:window_item_ids)
        and mwi.marking_window_id in (:marking_window_ids)
      join marking_window_user_meta mwum
        on mwum.marker_uid = mcb.uid
        and mwum.marking_window_id = mwi.marking_window_id
        and mwum.meta_key  = 'Marker_Number'
        and mwum.is_revoked  = 0
      where mcbr.taqr_id in (:taqrIds)
        and mcbr.is_revoked = 0
      group by marker_number, read_id, taqr_id
      order by read_id
      `)
      
      await prevReads_at.map((prevRead)=>{
        let taqr_id; //Add type to that
        if(typeof(prevRead.taqr_id) == 'string'){
          taqr_id= parseInt(prevRead.taqr_id)
        }
        else{
          taqr_id= prevRead.taqr_id;
        }
        const mcbrId = taqrMcbrMap.get(taqr_id);
        if(mcbrId){
          let arr = mcbrPrevReadsMap[mcbrId]
          if (!arr){
            arr = mcbrPrevReadsMap[mcbrId] = []
          }
          arr.push(prevRead)
        }
      })
      function groupByMultipleKeys(data : prevRead[], keys: (keyof prevRead)[]) {
        const groupedData =  data.reduce((result: Map<string, prevRead>, current) => {
          const groupKey = keys.map(key => current[key]).join('_');
          if (!result.has(groupKey)) {
            result.set(groupKey, current);
          }
          return result;
        }, new Map<string, prevRead>());
        return Array.from(groupedData.values());
      }
      for (const mcbr in mcbrPrevReadsMap) {
        mcbrPrevReadsMap[mcbr] = groupByMultipleKeys(mcbrPrevReadsMap[mcbr], ["marker_number", "read_id"])
      }
    }
    catch (e){
      mcbrPrevReadsMap.err = e
    }
    return mcbrPrevReadsMap;
  }

  async getValidityScoringKeys(window_item_id:number, responses:{taqr_id:number, is_validity:number}[]){
    const validityResponses = responses.filter(r => (r.is_validity == 1) );
    const validityResponseTaqrIds = validityResponses.map(r => r.taqr_id);
    if (validityResponseTaqrIds.length === 0){
      return []
    }
    const expectedScoreOptions = await dbRawRead(this.app, [window_item_id, validityResponseTaqrIds], `
      select mrss.taqr_id
           , mrs.score_option_id
      from marking_response_set_selections mrss
      join marking_response_selections mrs
        on mrs.id = mrss.selection_id
      where mrss .window_item_id = ?
      and mrss.taqr_id in (?)
    `)
    return expectedScoreOptions;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    const batch_response_id = id;
    if (!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const {is_leader_view} = params.query;
    const taqr = await dbRawReadSingle(this.app, [batch_response_id], SQL_RESPONSE_STATE); // required
    const scan_url = await this.app.service('public/scor-lead/validity/responses').getTaqrScanUrl(taqr.taqr_id);
    let is_paper_response;
    if (scan_url) {
      is_paper_response = 1
    }
    else if (is_leader_view && !scan_url){
      const paperInfo = await dbRawReadSingle(this.app, [taqr.taqr_id], SQL_IS_TAQR_PAPER_FORMAT);
      is_paper_response = paperInfo.is_paper_response
    }
    return {
      response_raw: taqr.response_raw,
      test_attempt_id: taqr.test_attempt_id,
      is_paper_response,
      scan_url: await this.app.service('public/scor-lead/validity/responses').getTaqrScanUrl(taqr.taqr_id),
      student_accommodation_cache: taqr.student_accommodation_cache,
      is_accomm_displayed: taqr.is_accomm_displayed
    }
  }
  
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async checkLeaderAccess(uid:number, mcbr_id:number | NullableId){
    const leaderRoles = await dbRawRead(this.app, {uid, mcbr_id}, SQL_LEADER_SUPER_BY_MCBR_AND_UID);
    if (leaderRoles.length > 0){
      return true;
    }
    else {
      return false;
    }
  }

  async patch (id: NullableId, data: any, params?: Params): Promise<Data> {
    if (params && params.query){
      let uid = await currentUid(this.app, params);
      const batch_response_id = id;
      const is_paired_draft = data.is_paired_draft;
      const {task_id, isLeader, scorer_uid} = params.query;
      let isByPassScorerRestrictions = false;
      if (isLeader == 1){
        isByPassScorerRestrictions = await this.checkLeaderAccess(uid, batch_response_id)
      }

      if (!isByPassScorerRestrictions){
        await this.app.service('public/scor-lead/windows').validateWindowLockByTask(task_id)
        await this.app.service('public/scor-scor/batches/available').checkScoringAccess(task_id, true)
      }

      if(scorer_uid) {
        uid = scorer_uid;
      }

      const payload:any = {
        score_option_id: data.score_option_id,
        score_flag_id: data.score_flag_id,
        meta: data.meta,
        last_touched_on: dbDateNow(this.app),
      }
      if(is_paired_draft){
        await this.patchMrsPd(payload, batch_response_id, uid);
      }
      else{
        await this.patchMrs(payload, batch_response_id, uid);
      }
      
      let isMarkedNow = (data.score_option_id !== undefined || data.score_flag_id !== undefined);
      let isMarkRemoved = (data.score_option_id === null && data.score_flag_id === null);

      if(is_paired_draft){
        const mcbrpd_id = await this.getMcbrPdId(batch_response_id, uid);
        const mcbrpd = await this.app.service('db/write/marking-claimed-batch-responses-paired-draft').get(mcbrpd_id || -1);
        if (!mcbrpd){
          throw new Errors.GeneralError('CANT_RETR_CLAIM_RESP')
        }
        if (isMarkedNow){
          let batchResponsePatch:any = {
            is_marked: isMarkRemoved ? 0 : 1,
            marked_on: isMarkRemoved ? undefined : dbDateNow(this.app),
          }
          await this.app.service('db/write/marking-claimed-batch-responses-paired-draft').patch(mcbrpd_id, batchResponsePatch);
        }
      }
      else {
        const claimedBatchResponseRecord = await this.app.service('db/write/marking-claimed-batch-responses').get(batch_response_id || -1);
        if (!claimedBatchResponseRecord){
          throw new Errors.GeneralError('CANT_RETR_CLAIM_RESP')
        }
        if (isMarkedNow){
          let batchResponsePatch:any = {
            is_marked: isMarkRemoved ? 0 : 1,
            marked_on: isMarkRemoved ? undefined : dbDateNow(this.app),
            is_validity_exact: undefined,
            is_validity_adj: undefined,
            is_validity_overunder: undefined
          }
          if (claimedBatchResponseRecord.response_selection_id && payload.score_option_id !== undefined){
            batchResponsePatch.is_validity_exact = 0;
            batchResponsePatch.is_validity_adj = 0;
            // look up the correct response
            const responseSelection = await this.app.service('db/write/marking-response-selections').get(claimedBatchResponseRecord.response_selection_id);
            const scoreOptionsCompared = [
              payload.score_option_id,
              responseSelection.score_option_id
            ];
            const scoreOptionValues = await dbRawRead(this.app, [scoreOptionsCompared], SQL_SCORE_OPTION_VAL);
            const scoreOptionValueRef = arrToMap(scoreOptionValues, 'id');
            const scoreOrderGiven = scoreOptionValueRef.get(payload.score_option_id);
            const scoreOrderExpected = scoreOptionValueRef.get(responseSelection.score_option_id);
            if (scoreOrderGiven && scoreOrderExpected){
              const difference = scoreOrderGiven.adj_index - scoreOrderExpected.adj_index;
              batchResponsePatch.is_validity_overunder = difference;
              let absDifference = Math.abs(difference);
              if (absDifference === 0){
                batchResponsePatch.is_validity_exact = 1;
              }
              else if (absDifference === 1){
                batchResponsePatch.is_validity_adj = 1;
              }
            }
          }
          await this.app.service('db/write/marking-claimed-batch-responses').patch(batch_response_id, batchResponsePatch);
        }
      } 
      return {};
    }
    throw new Errors.BadRequest();
  }

  async patchMrs(payload:any, batch_response_id: NullableId, uid: number){
    let records: any[];
    records = await dbRawRead(this.app, [batch_response_id], SQL_BATCH_RESP_SCORE_ENTRY_BY_ID);
    if (records && records.length > 0){
      await Promise.all(records.map(async (record) => {
        return await this.app.service('db/write/marking-response-scores').patch(record.id, payload);
      }))
    }
    else {
      payload.batch_response_id = batch_response_id;
      payload.uid = uid;
      await this.app.service('db/write/marking-response-scores').create(payload);
    }
  }

  async patchMrsPd(payload:any, batch_response_id: NullableId, uid: number){
    let records: any[];
    records = await dbRawRead(this.app, [batch_response_id, uid], SQL_BATCH_RESP_SCORE_ENTRY_BY_ID_PAIRED_DRAFT);
    if (records && records.length > 0){
      await Promise.all(records.map(async (record) => {
        return await this.app.service('db/write/marking-response-scores-paired-draft').patch(record.id, payload);
      }))
    }
    else {
      const mcbrpd_id = await this.getMcbrPdId(batch_response_id, uid);
      payload.batch_response_paired_draft_id = mcbrpd_id;
      payload.uid = uid;
      await this.app.service('db/write/marking-response-scores-paired-draft').create(payload);
    }
  }

  async getMcbrPdId(batch_response_id: NullableId, uid: number) {
    const mcbrpd = await dbRawRead(this.app, [batch_response_id, uid], `
      select mcbrpd.id 
      from marking_claimed_batch_responses_paired_draft mcbrpd 
      join marking_claimed_batches_paired_draft mcbpd 
        on mcbrpd.claimed_batch_paired_draft_id = mcbpd.id
      where mcbrpd.batch_response_id = ?
        and mcbpd.uid = ?
      and mcbrpd.is_revoked = 0;
    `);
    return mcbrpd[0].id;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (params && params.query){
      let uid = await currentUid(this.app, params);

      if(params.query?.scorer_uid) {
        uid = params.query.scorer_uid;
      }
      const {is_score_option, is_flag, is_bottom_flag, is_paired_draft} = params.query;

      const batch_response_id = id;
      // identify an existing score entry to update
      let records:IMarkingResponseScores[]  = [];
      
      if(is_paired_draft){
        records = await dbRawRead(this.app, [batch_response_id, uid], SQL_BATCH_RESP_SCORE_ENTRY_BY_ID_PAIRED_DRAFT)
      }
      else {
        if(is_bottom_flag){
          records = await dbRawRead(this.app, [batch_response_id], SQL_BATCH_RESP_SCORE_BOTTOM_FLAG_BY_ID)
        }
        else{
          records = await dbRawRead(this.app, [batch_response_id], SQL_BATCH_RESP_SCORE_ENTRY_BY_ID)
        }
      } 
      
      return await Promise.all(records.map(async (record: IMarkingResponseScores) => {
        let payload:any = {
          last_touched_on: dbDateNow(this.app),
        };
        if (is_score_option){ payload.score_option_id = null; }
        if (is_flag){ payload.score_flag_id = null; }
        if(is_paired_draft){
          const mcbrpd_id = await this.getMcbrPdId(batch_response_id, uid);
          await this.app.service('db/write/marking-response-scores-paired-draft').patch(record.id, payload);
          if (is_score_option){
            await this.app.service('db/write/marking-claimed-batch-responses-paired-draft').patch(mcbrpd_id, {is_marked:0});
          }
        }
        else {
          await this.app.service('db/write/marking-response-scores').patch(record.id, payload);
          if (is_score_option){
            await this.app.service('db/write/marking-claimed-batch-responses').patch(batch_response_id, {is_marked:0});
          }
        }

        return {success: true};
      }));
    }
    throw new Errors.BadRequest();
  }
}
