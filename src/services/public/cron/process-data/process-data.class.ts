import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

const GET_NEXT_EXPORT = 'RETRIEVE_NEXT_EXPORT';
const GET_UNPROCESSED_EXPORT = 'GET_UNPROCESSED_EXPORT';
export class ProcessData implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const mode = params.query.mode;
      const limit: number = Number(params.query.limit);
      if (!limit){
        throw new Errors.BadRequest();
      }
      if (mode == GET_NEXT_EXPORT) {
        return dbRawRead(this.app, [limit], `
          SELECT id, start_timestamp, end_timestamp
          FROM process_data_logs_exports_log
          ORDER BY id DESC
          LIMIT ?
        `);
      } else if (mode == GET_UNPROCESSED_EXPORT) {
        return dbRawRead(this.app, [limit], `
          SELECT id, start_timestamp, end_timestamp, s3_key_raw
          FROM process_data_logs_exports_log
          WHERE s3_key_processed is NULL
          LIMIT ?
        `);
      }
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {
      start_timestamp,
      end_timestamp,
      log_group_name,
      s3_bucket,
      s3_key_raw,
    } = <any> data;
  
    const start_date = new Date(Number(start_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;
    const end_date = new Date(Number(end_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;

    await this.app.service('db/write/process-data-logs-exports-log').create({
      start_timestamp,
      end_timestamp,
      start_date,
      end_date,
      log_group_name,
      s3_bucket,
      s3_key_raw,
      created_on: dbDateNow(this.app)
    }) 
    
    return {
      start_date,
      end_date
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const {
      start_timestamp,
      end_timestamp,
      s3_key_processed
    } = <any> data;
  
    await this.app.service('db/write/process-data-logs-exports-log')
      .db()
      .where({
        start_timestamp: start_timestamp,
        end_timestamp: end_timestamp,
      })
      .update({
        s3_key_processed: s3_key_processed
      });
    
    return {
      start_timestamp,
      end_timestamp,
      s3_key_processed
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
