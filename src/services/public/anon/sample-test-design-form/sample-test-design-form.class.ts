import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import axios from 'axios';
import { randArrEntry } from '../../../../util/random';
import { sanitizeFramework } from './util/sanitize-framework';
import { currentUid } from '../../../../util/uid';
import { dbRawReadSingle, dbRawReadSingleReporting } from '../../../../util/db-raw';
interface Data {}

interface ServiceOptions {}

export class SampleTestDesignForm implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {

    if(params && params.query) {
      
      const {ts_id, testVersionId, isPerusal} = params.query;

      let source_item_set_id = +id;
      let perusalSettings;
      let sessionInfo;

      const query:any = {}
      
      if(ts_id) { 
        if(!params.authentication){
          throw new Errors.NotAuthenticated("Not authenticated");
        }
        const uid = await currentUid(this.app, params);
        const record = await dbRawReadSingle(this.app, {ts_id, uid, testVersionId}, `
          select td.source_item_set_id , twtar.id, twtar.type_slug
          , twtar.perusal_type
          , twtar.perusal_end_type
          , twtar.perusal_offset_hours
          , twtar.perusal_duration_hours
          , twtar.perusal_date_start
          , twtar.perusal_date_end
          , twtt.perusal_configs
          , min(ta.started_on) as first_ta_started_on
          , ts.date_time_start as ts_date_time_start
          , tw.date_start tw_date_start
          , tw.date_end tw_date_end
          , twtar.test_date_end
          from school_class_test_sessions scts 
          join school_classes sc 
            on sc.id = scts.school_class_id
          join test_sessions ts 
            on ts.id = scts.test_session_id 
          join test_window_td_alloc_rules twtar 
            on twtar.type_slug = scts.slug  
            and ts.test_window_id = twtar.test_window_id 
          join test_windows tw
            on tw.id = twtar.test_window_id
          join test_window_td_types twtt 
            on twtt.type_slug = twtar.type_slug
            and twtt.test_window_id is null
            and twtt.is_revoked = 0
          join test_designs td 
            on td.id = twtar.test_design_id 
            ${ testVersionId ?  'and td.id = :testVersionId' : '' }
          join user_roles ur 
            on (
              (ur.group_id  = sc.group_id and ur.role_type in ('schl_teacher', 'schl_teacher_invig'))
              or 
              (ur.group_id = sc.schl_group_id and ur.role_type = 'schl_admin')
            )
            and ur.is_revoked = 0
          left join test_attempts ta
            on ta.test_session_id = ts.id
            and ta.is_invalid = 0
          where scts.test_session_id = :ts_id
          and ur.uid = :uid
          group by ts.id
        `)
        if (!record){
          throw new Errors.Forbidden('GROUP_ROLE_REQ')
        }
        source_item_set_id = record.source_item_set_id
        if (!!isPerusal){
          perusalSettings = {
            perusal_type: record.perusal_type,
            perusal_end_type: record.perusal_end_type,
            perusal_offset_hours: record.perusal_offset_hours,
            perusal_duration_hours: record.perusal_duration_hours,
            perusal_date_start: record.perusal_date_start,
            perusal_date_end: record.perusal_date_end,
            perusal_configs: record.perusal_configs
          }
          sessionInfo = {
            first_ta_started_on: record.first_ta_started_on,
            ts_date_time_start: record.ts_date_time_start,
            tw_date_start: record.tw_date_start,
            tw_date_end: record.tw_date_end,
            test_date_end: record.test_date_end
          }
        }
      }
      else {
        query.is_public = 1;
        if(testVersionId) { 
          const record = await dbRawReadSingle(this.app, {testVersionId}, `
            select td.source_item_set_id 
            from test_designs td  
            where id = :testVersionId
          `);
          if (record && record.source_item_set_id){
            source_item_set_id = record.source_item_set_id
          }
        }
        const isPwdProtected = await this.app.service('public/test-auth/public-pwd-protected').getPwdProtected(source_item_set_id);
        if(isPwdProtected) {
          const uid = await currentUid(this.app, params);
          const itemSet = await this.app.service('db/read/temp-question-set').get(id);
          //Ensure that the user has access to this assessment before allowing viewership
          await this.app.service('public/test-auth/item-set').checkItemSetAccess(itemSet, uid);
        }
      }

      query.source_item_set_id = source_item_set_id;
      let lang:string;
      if (params && params.query){
        lang = params.query.lang;
        if (testVersionId){
          query.id = testVersionId;
        }
      }
      const testDesignRecords = <Paginated<any>> await this.app
        .service('db/read/test-designs')
        .find({
          query
        })
      if (testDesignRecords.total === 0){
        throw new Errors.NotFound();
      }
      ////
      const test_design_ids = testDesignRecords.data.map( d => d.id);
      const testFormRefs = <Paginated<any>> await this.app
        .service('db/read/test-forms')
        .find({
          query: {
            $select: ['id', 'lang', 'file_path', 'test_design_id'],
            $limit: 5000,
            test_design_id: {$in: test_design_ids},
            is_revoked: 0,
          }
        });
      
      const testFormCandidates = [];
      testFormRefs.data.forEach(testForm => {
        if (testForm.lang == lang){
          testFormCandidates.push(testForm)
        }
      })
      if (!testFormCandidates.length){
        testFormCandidates.push(testFormRefs.data[0])
      }
      let testFormSelection:any = randArrEntry(testFormCandidates);
      
      const testDesignRecord = testDesignRecords.data.find(r => {return testFormSelection.test_design_id === r.id});
      const testFormData = await this.getTestDesign(testFormSelection.file_path);
      const styleProfileConfig = await this.app.service('public/student/session').loadStyleProfileByTestDesign(testFormSelection.id);
      
      return {
        testFormData,
        testDesignRecord,
        framework: sanitizeFramework(testDesignRecord.framework),
        styleProfileConfig,
        perusalSettings,
        sessionInfo
      };

    }

    throw new Errors.BadRequest('MISSING_PARAMS');
  }

  public async getTestDesign(file_path:string){
    const url = generateS3DownloadUrl(file_path, 60);
    const testDesign = await axios.get(url, {});
    return <any>testDesign.data;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
