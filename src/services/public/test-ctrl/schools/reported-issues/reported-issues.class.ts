import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbDateNow } from '../../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { arrToMap } from '../../../../../util/param-sanitization';
import { currentUid } from '../../../../../util/uid';
import { RoleTypes } from '../../../support/role-types/role-types.class';
import { FLAGS, isABED } from '../../../../../util/whiteLabelParser';
import { IReportedIssueLookupConfig } from './model/types';
import { SQL_REPORTED_ISSUES_COMMON, SQL_REPORTED_ISSUES_COMMON_BY_TS, SQL_REPORTED_ISSUES_COMMON_STUDENTS } from './model/sql';
import { ReadSysConstantsString } from '../../../../db/table-services';
import { getSysConstString } from '../../../../../util/sys-const-string';
import { DEFAULT_RECORD_LIMIT } from './model/constants';

interface Data {}

interface ServiceOptions {}

export class ReportedIssues implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const {test_window_id, ric_record_limit, max_ric_id, min_ric_id} = params.query
      return this.loadRecords({test_window_id, ric_record_limit, max_ric_id, min_ric_id})
    }
    throw new Error();
  }
  
  async loadRecords(config:IReportedIssueLookupConfig){

    const isRicFilter = (!!config.ric_id)
    const isTwFilter = (!! config.test_window_id)
    const isMaxRicIdFilter = (!! config.max_ric_id)
    const isMinRicIdFilter = (!! config.min_ric_id)
    const logRecords = await dbRawRead(this.app, config, SQL_REPORTED_ISSUES_COMMON(config.ric_record_limit || DEFAULT_RECORD_LIMIT, {isRicFilter, isTwFilter, isMaxRicIdFilter, isMinRicIdFilter} ))
    if (logRecords.length === 0){
      return [];
    }

    // attach student record (todo: should this really be done for the entire payload?)
    const ric_ids = logRecords.map( r => r.ric_id)
    const logRecordsRef = arrToMap(logRecords, 'ric_id');
    logRecords.forEach(ric => {
      ric.students = [];
    })
    const logRecordStudents = await this.loadRicStudents(ric_ids);
    logRecordStudents.forEach(ri => {
      const ric = logRecordsRef.get(ri.ric_id)
      ric.students.push(ri)
    });
    logRecords.forEach(record => {
      record.num_students = record.students.length
    })

    return logRecords
  }

  async loadRicStudents(ric_ids:number[]){
    const stuGovIdNs_str = await getSysConstString(this.app, 'DEFAULT_STU_META_NS_GOVNUM');
    const stuGovIdKey = await getSysConstString(this.app, 'DEFAULT_STU_META_KEY_GOVNUM');
    if (!stuGovIdNs_str){
      throw new Errors.GeneralError('APP_CLUSTER_CONFIG_MISSING_SYS_CONST', 'need to define a value for DEFAULT_STU_META_NS_GOVNUM')
    }
    const stuGovIdNs = stuGovIdNs_str.split(',');
    return await dbRawRead(this.app, {ric_ids, stuGovIdNs, stuGovIdKey}, SQL_REPORTED_ISSUES_COMMON_STUDENTS)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (params && params.query){
      const {uid, ts_id} = params.query;
      const records = await dbRawRead(this.app, {uid, ts_id}, SQL_REPORTED_ISSUES_COMMON_BY_TS);
      return {records}
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const config = <any> data;
      const created_by_uid = await currentUid(this.app, params);
      const {test_session_id} = config;
      const sctsRecords = await dbRawRead(this.app, {test_session_id}, `
        select scts.school_class_id 
        from school_class_test_sessions scts 
        where scts.test_session_id = :test_session_id
      `)
      const school_class_id = sctsRecords[0].school_class_id
      return this.app
        .service('public/educator/session-reported-issue')
        .createReportedIssue(created_by_uid, {
          test_session_id: config.test_session_id, 
          student: config.studentUids, 
          msg: config.msg, 
          categoryId: config.categoryId,
          categorySelection: config.categorySelection, 
          school_class_id
        })
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params && params.query && id){
      const uid = await currentUid(this.app, params);
      const {patchType, isResolved, resolution_slug} = <any> data;
      const payload:any = {}
      const response:any = {}
      let comment = ''
      if (patchType === 'KEYWORDS'){
        const {newKeywords, oldKeyWords} = <any> data;
        // check old keywords vs. what the user has as the old keywords (if different, the current keywords will be merged with the new keywords)
        const curRI = await this.app.service('db/read/reported-issues-common').get(id);
        let isMergeRequired = oldKeyWords !== curRI.keywords
        if (isMergeRequired){
          const oldKeyWordsSplit:string[] = curRI.keywords.split(',').map((s:string)=> s.trim())
          const newKeyWordsSplit:string[] = newKeywords.split(',').map((s:string)=> s.trim())
          for (let keyword of oldKeyWordsSplit){
            if (!newKeyWordsSplit.includes(keyword)){
              newKeyWordsSplit.push(keyword)
            }
          }
          payload.keywords = newKeyWordsSplit.join(', ');
        }
        else {
          payload.keywords = newKeywords
        }
        response.keywords = payload.keywords
        comment = `Set keywords: "${newKeywords}" ${isMergeRequired ? '\n\n(merged with existing keywords because of a parallel save)' : ''}`
      }
      if (patchType === 'ASSIGNMENT'){
        const {assigned_uid, assigneeName} = <any> data;
        payload.assigned_uid = assigned_uid
        if (assigned_uid){
          payload.assigned_on = dbDateNow(this.app);
          comment = `Assigned to ${assigneeName} (${assigned_uid})`
        }
        else {
          payload.assigned_on = null
          comment = `Unassigned`
        }
      }
      else if (patchType === 'RESOLUTION'){
        if (isResolved){
          payload.is_resolved = 1
          payload.resolved_by_uid = uid;
          payload.resolved_on = dbDateNow(this.app);
          payload.resolution_slug = resolution_slug;
          comment = `Resolution: ${resolution_slug}`
        }
        else {
          payload.resolution_slug = resolution_slug || '';
          payload.is_resolved = 0
          comment = `Unset Resolution`
        }
      }

      await this.app.service('db/write/reported-issue-comments').create({
        reported_issues_common_id: id,
        comment,
        uid,
        is_auto: 1
      })
      await this.app.service('db/write/reported-issues-common').patch(id, payload);

      return response
    }
    throw new Errors.BadGateway();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
