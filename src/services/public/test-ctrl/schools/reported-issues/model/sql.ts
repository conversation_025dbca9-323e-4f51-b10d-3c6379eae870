import { IReportedIssueLookupConfig } from "./types";

// todo: consider splitting this into 3: 1 to pull ric_ids, another to pull the sessions+schools, another to pull the users

export interface IReportedIssuesCommonFlags {
    isTwFilter?:boolean, 
    isRicFilter?:boolean, 
    isMaxRicIdFilter?:boolean, 
    isMinRicIdFilter?:boolean, 
    isCreatedByFilter?:boolean, 
    isDaysBackLimit?:boolean,
}


export const SQL_REPORTED_ISSUES_COMMON = (limit=1000, config:IReportedIssuesCommonFlags) => ` /*SQL_REPORTED_ISSUES_COMMON*/
    select ric.id ric_id
        , ric.created_on
        , ric.school_class_id sc_id
        , ric.test_session_id ts_id
        , ric.category categorySelection
        , ric.sub_category subCategorySelection
        , ric.description msg
        , scts.slug ts_slug
        , sd.foreign_id sd_code
        , sd.name sd_name
        , s.foreign_id s_code
        , s.name s_name
        , UPPER(s.lang) s_lang
        , sc.name sc_name
        , ric.phone_number
        , ifnull(ric.contact_email, a.email) invigilator_email
        , ric.test_window_id tw_id
        , ric.log_id old_log_id
        , ric.is_resolved
        , ric.resolved_on
        , ric.resolution_slug
        , ric.assigned_uid
        , ric.keywords
        , concat(u.first_name, ' ', u.last_name) assigned_name
        , u.contact_email assigned_email
        , ric.assigned_on
    from reported_issues_common ric
    left join school_class_test_sessions scts 
        on scts.test_session_id = ric.test_session_id
    left join school_classes sc 
        on sc.id = scts.school_class_id
    left join schools s
        on s.group_id = sc.schl_group_id
    left join school_districts sd
        on sd.group_id = s.schl_dist_group_id
    left join auths a 
        on a.uid = ric.created_by_uid
    left join users u
        on u.id = ric.assigned_uid
    where 1=1
    ${config.isRicFilter ? 'and ric.id = :ric_id' : '' }
    ${config.isTwFilter ? 'and ric.test_window_id = :test_window_id' : '' }
    ${config.isCreatedByFilter ? 'and ric.created_by_uid = :created_by_uid' : '' }
    ${config.isMaxRicIdFilter ? 'and ric.id < :max_ric_id' : '' }
    ${config.isMinRicIdFilter ? 'and ric.id > :min_ric_id' : '' }
    ${config.isDaysBackLimit ? `and ric.updated_on > DATE_SUB(NOW(), INTERVAL :daysBackLimit DAY)` : '' }
    order by ric.updated_on desc
    LIMIT ${+limit}
`

export const SQL_REPORTED_ISSUES_COMMENTS = (isRestrictToReplies?:boolean, isByCommentId?:boolean) => ` /*SQL_REPORTED_ISSUES_COMMENTS*/
    select ric.id
        , ric.reported_issues_common_id 
        , ric.uid 
        , u.first_name 
        , u.last_name 
        , u.contact_email 
        , ric.comment 
        , ric.created_on 
        , ric.is_auto
        , ric.is_email_sent
    from reported_issue_comments ric 
    left join users u 
    on u.id = ric.uid 
    where ric.reported_issues_common_id IN (:ric_ids)
    and ric.is_removed = 0 
    ${ isByCommentId ? `and ric.id = :commentId` : ''}
    ${ isRestrictToReplies ? `and ric.is_email_sent = 1` : ''}
`

export const SQL_REPORTED_ISSUES_COMMON_BY_THREAD = ` /*SQL_REPORTED_ISSUES_COMMON_BY_THREAD*/
    select ric.id ric_id
        , ric.is_resolved
    from reported_issues_common ric
    where ric.thread_hash = :thread_hash
`
export const SQL_REPORTED_ISSUES_COMMON_BY_ID = ` /*SQL_REPORTED_ISSUES_COMMON_BY_ID*/
    select ric.id ric_id
        , ric.is_resolved
    from reported_issues_common ric
    where ric.id = :ric_id
`

export const SQL_REPORTED_ISSUES_COMMON_BY_TS = ` /*SQL_REPORTED_ISSUES_COMMON_BY_TS*/
    select ric.id ric_id
        , ri.id ri_id
        , ric.test_session_id ts_id
        , ric.category categorySelection
        , ric.description msg
    from reported_issues_common ric
    join reported_issues ri
        on ri.reported_issues_common_id = ric.id
    where ri.testtaker_uid = :uid
        and ri.test_session_id = :ts_id
        and ri.is_revoked = 0 
`


export const SQL_REPORTED_ISSUES_COMMON_STUDENTS = ` /*SQL_REPORTED_ISSUES_COMMON_STUDENTS*/
    select ric.id ric_id
        , ri.id ri_id
        , um_stu_gov_id.uid
        , um_stu_gov_id.value StudentIdentificationNumber
        , u.first_name
        , u.last_name
        , ric.log_id old_log_id
    from reported_issues_common ric
    join reported_issues ri
        on ri.reported_issues_common_id = ric.id
        and ri.is_revoked = 0
    join users u
        on u.id = ri.testtaker_uid
    join user_metas um_stu_gov_id
        on u.id = um_stu_gov_id.uid
        and um_stu_gov_id.key = :stuGovIdKey
        and um_stu_gov_id.key_namespace in (:stuGovIdNs)
    where ric.id in (:ric_ids)
`