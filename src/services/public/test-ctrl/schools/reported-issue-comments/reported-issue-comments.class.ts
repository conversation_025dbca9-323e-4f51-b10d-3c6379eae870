import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { sendReportedIssueCommentEmail } from '../../../../mail/core/email-utils';
import { SQL_REPORTED_ISSUES_COMMENTS } from '../reported-issues/model/sql';

interface Data {}

interface ServiceOptions {}

export class ReportedIssueComments implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {ric_id} = params.query;
      return this.getFormattedRecords([ric_id])
    }
    throw new Error();
  }

  async getFormattedRecords(ric_ids:number[], commentId?:number){
    return dbRawRead(this.app, {ric_ids, commentId}, SQL_REPORTED_ISSUES_COMMENTS(false, !!commentId));
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const {ric_id, comment, shouldSendEmail, contact_email, email_subject} = <any> data;
      const created_by_uid = await currentUid(this.app, params);
      return await this.writeComment(created_by_uid, ric_id, comment, 0, shouldSendEmail, contact_email, email_subject);
    }
    throw new Error();
  }

  async writeComment(created_by_uid:number, ric_id:number, comment:string, is_auto=0, shouldSendEmail=false, contact_email:string = '', email_subject:string = ''){
    const commentRecord = await this.app.service('db/write/reported-issue-comments').create({
      reported_issues_common_id: ric_id,
      comment,
      uid: created_by_uid,
      is_auto,
      is_email_sent: shouldSendEmail ? 1 : 0,
    });

    // If we should send email and there's a contact email
    if (shouldSendEmail && contact_email) {
      await sendReportedIssueCommentEmail(this.app, {
        ric_id,
        contact_email,
        email_subject,
        comment,
      });
    }

    const formattedRecords = await this.getFormattedRecords([ric_id], commentRecord.id);
    return formattedRecords[0];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
