import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { DBD_U_GROUP_TYPES, DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';
import { checkDomain } from '../../../../util/domain-whitelist';
import { AccountType } from '../../../../types/account-types';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';

interface Data {}

interface ServiceOptions {}

interface RoleType{
  uid?: number,
  role_type: string,
  group_id: number,
  created_by_uid: number,
}
interface InviteConfig {
  role: RoleType,
  account: {
    firstName?: string,
    lastName?: string,
    email: string,
  },
  offsetDays: number,
  created_by_uid: number,
  emailLinkDomain?:string, 
  domain?: string, 
  isAutoEmail?:boolean
}

export class Roles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  roleTypes : string[];
  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.roleTypes = [
      DBD_U_ROLE_TYPES.test_ctrl_data_retr,
      DBD_U_ROLE_TYPES.test_ctrl_issue_tracker,
      DBD_U_ROLE_TYPES.test_ctrl_lias_cert_body,
      DBD_U_ROLE_TYPES.test_ctrl_lias_internal,
      DBD_U_ROLE_TYPES.test_ctrl_lias_test_admin,
      DBD_U_ROLE_TYPES.test_ctrl_meta_reg,
      DBD_U_ROLE_TYPES.test_ctrl_score_valid,
      DBD_U_ROLE_TYPES.test_ctrl_window_monitor,
      DBD_U_ROLE_TYPES.test_ctrl_issue_revw,
      DBD_U_ROLE_TYPES.test_ctrl_data_exporter,
      DBD_U_ROLE_TYPES.debug
    ];
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid
    return <any> this.app.service('auth/user-role-actions').getSystemRoles(uid, DBD_U_GROUP_TYPES.mpt_test_controller);
  }

  async get (id: Id, params?: Params): Promise<Data> {
    
    const records = await dbRawRead(this.app, {roleTypes: this.roleTypes}, `
    select u.id uid
      , u.first_name
      , u.last_name 
      , u.contact_email
      , ur.id ur_id
      , ur.role_type  
      , ur.created_on
      , ur.is_revoked
      , ur.revoked_on
      , ur.group_id
    from user_roles ur 
    join users u
      on ur.uid = u.id
    where ur.role_type in (:roleTypes)
      and ur.is_removed = 0
    order by uid
    ;`);
    return [{
      records
    }];
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = userInfo.uid;
    let {email, role_type, group_id, invite, first_name, last_name, emailLinkDomain } = <any> data;
    if(!created_by_uid || !email || !role_type || !group_id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if(!this.roleTypes.includes(role_type)){
      throw new Errors.BadRequest('INCORRECT_ROLE_TYPE');
    }
    const user = await dbRawReadSingle(this.app, {email}, `
    select u.id uid 
    from users u
    where contact_email = :email
    and is_claimed = 1
    ;`);
    
    if(invite){
      const role:RoleType = {
        role_type,
        group_id,
        created_by_uid
      };
      if(user){
        throw new Errors.BadRequest('USER_EXISTS');
      }
      const account = {
        firstName: first_name || '',
        lastName: last_name || '',
        email: email,
      }; 
      const whitelabel = this.app.get('whiteLabel')
      let domain = whitelabel;
      const offsetDays = 8;
      const isAutoEmail = true;
      await this.createTestCtrlInvite({role, account, offsetDays, created_by_uid, emailLinkDomain, domain, isAutoEmail});
      return {
        success: true,
        message: "Invitation created successfully."
      }
      
    }
    else{
      if(!user){
        throw new Errors.BadRequest('USER_NOT_FOUND');
      }
      const roleData:RoleType = {
        uid: user.uid,
        role_type,
        group_id,
        created_by_uid
      };
      await this.app.service('db/write/user-roles').create({
        ... roleData,
      });
      
      return {
        success: true,
        message: "Role created successfully."
      }
    }
  }

  async createTestCtrlInvite(config: InviteConfig){
    const subject = 'abed_testctrl_invite_subject';
    const content = 'abed_testctrl_invite_email';
    const domain: string = isABED(config.domain as FLAGS) ? config.domain! : 'api-eassessment.vretta.com' as string; // server
    const {role, account, offsetDays, created_by_uid, emailLinkDomain, isAutoEmail} = config;
    const DOMAIN = checkDomain(emailLinkDomain, this.app.get('isDevMode'));
    const account_type = AccountType.TEST_CTRL;
    const roles = [
      {role_type: role.role_type, group_id: role.group_id}
    ]
    const invite = await this.app
      .service('auth/invitation')
      .create({
        account_type,
        roles,
        created_by_uid,
        first_name: account.firstName,
        last_name: account.lastName,
        contact_email: account.email,
        isAutoEmail,
        langCode: 'en',
        offsetDays,
        domain,
        emailSubjectSlug: subject,
        emailTemplateSlug: content,
        emailTemplateParams: 
        {
          DOMAIN,
          FULL_NAME: account.firstName,
          ROLE_NAME: role.role_type == "debug"? "Support":"Test Controller",
          LANG_CODE: 'en',
          EMAIL: account.email,
          FIRST_NAME: encodeURIComponent(account.firstName || ''),
          LAST_NAME: encodeURIComponent(account.lastName|| ''),
          EMAIL_ENCODED: encodeURIComponent(account.email),
        }
      });
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!id){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    await this.app.service('db/write/user-roles').patch(id, {
      is_revoked: 1,
      revoked_on: dbDateNow(this.app),
    })
    return {
      success: true,
      message: "Role removed successfully."
    }}
}
