
export const SQL_SA_TEST_WINDOWS = (isAllowQaWindows:boolean) => `
  select tw.id
        , tw.title
        , tw.is_active
        , tw.date_start
        , tw.date_end
        , tw.test_design_id
        , tw.notes
        , tw.created_on
        , tw.create_by_uid
        , tw.duration_m
        , tw.last_activated_on
        , tw.last_activated_by_uid
        , tw.is_allow_new_ts
        , tw.is_allow_new_bookings
        , tw.is_allow_results_tt
        , tw.is_multi_attempt
        , tw.num_attempts
        , tw.attempts_intvl
        , tw.is_invig_unsubmit
        , tw.is_invig_taketest
        , tw.is_invig_mark
        , tw.is_archived
        , tw.is_allow_test_centre
        , tw.is_allow_classroom
        , tw.is_allow_remote
        , tw.is_allow_mobile_tether
        , tw.is_allow_video_conference
        , tw.test_ctrl_group_id
        , tw.is_allow_appeals
        , tw.test_centre_name_slug
        , tw.classroom_name_slug
        , tw.remote_name_slug
        , tw.window_code
        , tw.item_set_id
        , tw.type_slug
        , tw.is_qa
        , tw.irt_ready
        , tw.dow_timing
        , tw.show_report_to_Board
        , tw.is_closed
        , tw.default_foreign_scope_id
        , tw.scope_group_id
        , tw.academic_year
        , tw.is_bg
        , tw.is_allow_session_schedule
        , tw.is_duration_enforced
        , tw.show_isr_num_q
        , tw.reg_lock_on
        , tw.report_require_validate
        , tw.is_not_students
        , tw.window_date_human
        , tw.title_persistent
        , tw.is_for_pasi
        , tw.PASI_school_year
        , tw.is_public_practice
        , tw.pasi_exam_period
        , tw.is_school_allowed_strict
        , tw.next_tw_id
        , tw.hardstop_offset_h
        , tw.is_allow_tqer_live_override
        , tw.is_teacher_creation
        , tw.is_sa_signoff_required
        , tw.is_classroom_assessment
        , tw.public_practice_configs
        , tw.is_field_test
        , tw.is_qa_internal
  from test_windows tw
  where tw.type_slug in (?)
    and tw.is_active = 1
    and tw.is_test_centre = 0
    ${isAllowQaWindows ? '' : 'and tw.is_qa = 0' }
`

export const SQL_SA_TEST_WINDOWS_ASSOC = `
  select  tw.id
        , tw.title
        , tw.is_active
        , tw.date_start
        , tw.date_end
        , tw.test_design_id
        , tw.notes
        , tw.created_on
        , tw.create_by_uid
        , tw.duration_m
        , tw.last_activated_on
        , tw.last_activated_by_uid
        , tw.is_allow_new_ts
        , tw.is_allow_new_bookings
        , tw.is_allow_results_tt
        , tw.is_multi_attempt
        , tw.num_attempts
        , tw.attempts_intvl
        , tw.is_invig_unsubmit
        , tw.is_invig_taketest
        , tw.is_invig_mark
        , tw.is_archived
        , tw.is_allow_test_centre
        , tw.is_allow_classroom
        , tw.is_allow_remote
        , tw.is_allow_mobile_tether
        , tw.is_allow_video_conference
        , tw.test_ctrl_group_id
        , tw.is_allow_appeals
        , tw.test_centre_name_slug
        , tw.classroom_name_slug
        , tw.remote_name_slug
        , tw.window_code
        , tw.item_set_id
        , tw.type_slug
        , tw.is_qa
        , tw.irt_ready
        , tw.dow_timing
        , tw.show_report_to_Board
        , tw.is_closed
        , tw.default_foreign_scope_id
        , tw.scope_group_id
        , tw.academic_year
        , tw.is_bg
        , tw.is_allow_session_schedule
        , tw.is_duration_enforced
        , tw.show_isr_num_q
        , tw.reg_lock_on
        , tw.report_require_validate
        , tw.is_not_students
        , tw.window_date_human
        , tw.title_persistent
        , tw.is_for_pasi
        , tw.PASI_school_year
        , tw.is_public_practice
        , tw.pasi_exam_period
        , tw.is_school_allowed_strict
        , tw.next_tw_id
        , tw.hardstop_offset_h
        , tw.is_allow_tqer_live_override
        , tw.is_teacher_creation
        , tw.is_sa_signoff_required
        , tw.is_classroom_assessment
        , tw.public_practice_configs
        , tw.is_field_test
        , tw.is_qa_internal
  from school_test_access sta
  join test_windows tw 
    on tw.id = sta.test_window_id 
    and tw.is_active = 1
  where sta.school_group_id = :schl_group_id
    and sta.is_revoked != 1
  group by tw.id
`

export const SQL_ACADEMIC_YEARS_BY_CODE = `
  select ay.code
       , ay.is_active
       , ay.is_preview
  from academic_years ay
  where ay.code IN (:academicYearCodes)
  order by ay.code desc
`

export const SQL_SCHOOL_ASSESSMENT_DEFS = `
  select ad.id
       , ad.assessment_slug
       , ad.label
       , ad.is_disabled
       , ad.is_classroom_assessment
       , ad.order
  from school_assessments_map sam
  join assessment_def ad on ad.id = sam.assessment_def_id
  where sam.school_id = :school_id
  order by ad.order
`