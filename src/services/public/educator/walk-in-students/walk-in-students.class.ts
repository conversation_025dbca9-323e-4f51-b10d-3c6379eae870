import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { dbDateNow } from '../../../../util/db-dates';
import { SQL_PREV_STU_ROLES, SQL_SC_CLASSES, SQL_SC_FROM_UID, SQL_SC_GUEST_EXISTING, SQL_STU_ROLES, SQL_TW_USER_METAS, SQL_USER_METAS, SQL_WALKIN_STUDENTS, SQL_GUEST_STU_ROLE_EXIST } from '../session/model/sql';
import { IWalkInStudentState } from '../session/model/types';
import { STUDENT_ROLE_TYPES, CLASS_TYPES } from './model/constants'
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';

export { STUDENT_ROLE_TYPES, CLASS_TYPES }

interface Data { }

interface ServiceOptions { }

export class WalkInStudents implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: any): Promise<Data[] | Paginated<Data>> {
    //we pull the information for one student who is in the waiting room of a given class.
    const { classId, uid } = params?.query;
    if (classId && uid) {
      const walkin_student = await this.getWalkinStudents(classId, uid);
        if(walkin_student?.students.length) {
          return [{ ...walkin_student.students[0], isShowingSecretId: false, is_walkin: true }];
        }
        else{
          //check if the student is a regular student in the class, there are times where this
          //function of the endpoint is called where a regular student could be untracked
          const regular_student = await this.getWalkinStudents(classId, uid, STUDENT_ROLE_TYPES.regular_student_role);
          if(regular_student?.students.length) {
            return [{ ...regular_student.students[0], isShowingSecretId: false, is_walkin: false }];
          }
        } return [];
    }
    throw new Errors.BadRequest('MISSING_PARAMS');
  }

  async getWalkinStudents(classId: any, oneUID?: any, role_type: string = STUDENT_ROLE_TYPES.walk_in_student_role) {
    //we pull the information for all students who are in the waiting room of a given class.
    //I added an extra check to see if the student is enrolled in the current school, and based on that we assign `is_guest` flag
    let student_metas    :any[] = [];
    let tw_student_metas :any[] = [];
    const walkin_students = await dbRawRead(this.app, {classId, role_type, uid: oneUID}, SQL_WALKIN_STUDENTS(oneUID) );
    if(walkin_students.length>0){
      const walkinUIDs = walkin_students.map((ws) => ws.uid);
      student_metas = await dbRawRead(this.app, 
        {walkinUIDs}, 
        SQL_USER_METAS
      );
      
      tw_student_metas = await dbRawRead(this.app, 
        {walkinUIDs}, 
        SQL_TW_USER_METAS 
      );

      tw_student_metas.forEach(tw_um => {
        try{
          tw_um.meta = JSON.parse(tw_um.meta);
        }catch(e){
          tw_um.meta = null;
        }
      })
    }

    const schl_group_id = await this.getSchoolGroupByClassroomId(classId);
    await this.app.service('public/abed-pasi').setDiplomaExamInfo(walkin_students, schl_group_id); // todo: this is hard-coded to ABED
    //`isShowingSecretId` is a switch that allows the user to show and hide the students' ASN 
    
    const response:IWalkInStudentState = {
      students: walkin_students.map(w => {return {...w, isShowingSecretId: false}}),
      student_metas,
      tw_student_metas
    }

    return response
  }

  // There are two uses for the GET function.
  // 1: 'isStudentWalkIn' that is a request to check if the student id we are passing belongs to a walk-in student
  //     This is used when a student logs in and we should navigate them to the regular dashboard or to the waiting room.
  // 2: Only passing the student's id. That is a request to check the student's user_metas, seeing if they have been accepted or rejected by their teacher.
  //     This is used when a student is in the waiting room and every 5 seconds their view will ping this endpoint to see if their status has changed or not. 
  async get(id: Id, params: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  //need to export this
  static async confirmWalkinGuestingClass(app: Application, targetClass: any, originClass: any, uid: number){
    //Guesting across schools works as the following:
    //a target class in one school will have one guest class for any student in the origin school.
    //any student that wants to guest in the target class will be added to the same guesting class. 
    //the guesting class will not appear in any of the school admin views. 
    const existinGuestC = await dbRawRead(app, 
      {targetClassGroupID: targetClass.schl_class_group_id, originSchlGroupID: originClass.schl_group_id}, 
      SQL_SC_GUEST_EXISTING
    );
    let newGuestC;
    //if we can't find the origin school's guesting class in the target class, we make one.
    if(existinGuestC.length == 0){

      //first we create a u_group record to generate a new group id.
      const uGroup = await app.service('db/write/u-groups').create({
        group_type: DBD_U_GROUP_TYPES.school_class,
        description: `${targetClass.schl_class_group_id}'s Guest`,
        created_on: dbDateNow(app),
        created_by_uid: uid
      })

      //then we create the new guest class. 
      //The purpose of this class is to allow students from the origin school to take the test in the target class.
      newGuestC = await app
      .service('db/write/school-classes')
      .create({
        schl_group_id: originClass.schl_group_id,
        schl_dist_group_id: originClass.schl_dist_group_id,
        name: `${targetClass.schl_class_group_id}'s Guest`,
        semester_id: targetClass.semester_id,
        is_grouping: targetClass.is_grouping,
        group_type: CLASS_TYPES.WALKIN_GUEST,
        created_by_uid: uid,
        created_on: dbDateNow(app),
        access_code: null,
        group_id: uGroup.id,
        is_active: 1,   // We decided to keep guest classes active for now. We might want to change this in the future but it is risky.
        is_placeholder: 0,
      });

      //Once we create the new class, we make them a guest of the target class.
      await dbRawRead(app, [targetClass.schl_class_group_id, newGuestC.group_id, uid], `
        INSERT INTO school_classes_guest (invig_sc_group_id, guest_sc_group_id, created_by_uid)
        VALUES (?, ?, ?);
      `);
    }
    //Now that we have the special guesting class, we enroll the student to it.
    const guestClass = existinGuestC.length ? existinGuestC[0] : newGuestC;
    await app
      .service('db/write/user-roles')
      .create({
        role_type: STUDENT_ROLE_TYPES.regular_student_role,
        uid: uid,
        group_id: guestClass.group_id,
        created_by_uid: uid,
        });
  }

  static async getSchoolClassFromUID(app: Application, studentUid: any){
    const resultWithSchl = await dbRawReadSingle(app, 
      { studentUid }, 
      SQL_SC_FROM_UID(true)
    );
    
    if (resultWithSchl) {
        return resultWithSchl;
    }

    return await dbRawReadSingle(app, 
      { studentUid }, 
      SQL_SC_FROM_UID(false)
    );
  }

  static async getClassInfoFromClassGroupId(app: Application, sch_class_group_id: number): Promise<{
    schl_class_group_id: number,
    schl_group_id: number,
    semester_id: number,
    sc_id: number
  }> 
  {
    return dbRawReadSingle(app, 
      {sch_class_group_id}, 
      SQL_SC_CLASSES
    );
  }

  async previousStudentRoles(studentUid: any, schl_group_id: any, semesterId: number){
    const enforceOneclassPerStudent = await getSysConstNumeric(this.app, 'ENFORCE_STU_1_TW_CLASS');
    return dbRawRead(this.app, 
      {studentUid, schl_group_id, semesterId}, 
      SQL_PREV_STU_ROLES(!!enforceOneclassPerStudent)
    );
  }

  async isGuestStudent(studentUid: any, schl_group_id: any){
    //this will check if the current student is enrolled in the school of the class the test is taking place.
    const schoolEnrollment = await dbRawRead(this.app, 
      {studentUid, group_id: schl_group_id}, 
      SQL_STU_ROLES
    );
    if(schoolEnrollment.length) return false;
    return true;
  }

  // There are two uses for the PATCH function.
  // 1: Rejecting a walk-in. A teacher can send a request to reject a walk-in student from a class. Their meta will be updated to rejected
  //     When that happens, the student will still remain in the waiting room but a message notifying them of the rejection will be displayed.
  //     The teacher can still choose to accept the same rejected student.
  // 2: Accepting a walk-in. A teacher can send a request to accept a walk-in student into a class. Their meta will be updated to accepted
  //     When that happens, the student's walk-in user roles become revoked. 
  //     And a new a set matching the old ones (school enrollment and class enrollment roles) are made with the role_type 'schl_student'
  //     Making them fully convert from a walk-in student into a regular student, same as any other student that is made with class attachment.
  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    const { uid, key} = data;
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const sch_class_group_id = params.query?.sch_class_group_id;
    if(!uid || !key || !sch_class_group_id)  throw new Errors.BadRequest('MISSING DATA: !uid || !key || !sch_class_group_id');
    const cUid = await currentUid(this.app, params);
    //with the introduction of cross school walkins, we want to make sure that when we revoke student-class connections 
    // we localize it to the current school so that the guest students original enrollments remain untouched.
    const classInfo = await WalkInStudents.getClassInfoFromClassGroupId(this.app, sch_class_group_id);
    let school_group_id: number = classInfo.schl_group_id;
    //a) if the teacher accepts the student, then we revoke their user_role that is requesting en1tering the class.
    //b) if the teacher rejects the student, then we revoke their user_role that is requesting entering the class.
    const relevantScRoles = await this.previousStudentRoles(uid, school_group_id, classInfo.semester_id);
    let isGuest = await this.isGuestStudent(uid, school_group_id);
    for(let user_role of relevantScRoles){
      await this.app
      .service('auth/user-role-actions')
      .revokeUserFromGroup(uid, user_role.group_id, cUid)
    }
    //only if the teacher accepts the student, we give the student regular access to the class, making them a regular student. 
    if (key == 'AcceptedByTeacher') {
      // Check if the student already has the regular_student_role in the user-roles table
      let existingRoles;
      if(!isGuest){
        existingRoles = await dbRawRead(this.app, 
          {studentUid: uid, group_id: sch_class_group_id}, 
          SQL_STU_ROLES
        );
      } else {
        existingRoles = await dbRawRead(this.app, 
          {studentUid: uid, group_id: sch_class_group_id}, 
          SQL_GUEST_STU_ROLE_EXIST
        );
      }
      if(existingRoles.length) return data;
      
      //only when the teacher accepts the student we will want to turn all is_removed flags for the student's previous connections to other classes to true
      for(let user_role of relevantScRoles){
        await this.app
        .service('auth/user-role-actions')
        .removeUserFromGroup(uid, user_role.group_id, cUid)
      }
      //if current student is not a guest then we assign them a regular student user role linking them to the target class.
      if(!isGuest){
        await this.app
        .service('db/write/user-roles')
          .create({
            role_type: STUDENT_ROLE_TYPES.regular_student_role,
                uid: uid,
                group_id: sch_class_group_id,
                created_by_uid: cUid,
            });
      }else{
        try{
          //if the current student is not a guest then we make sure that they are guesting into the target class.
          //first get an original class enrollment from the student's origin school.
          const originalClass = await WalkInStudents.getSchoolClassFromUID(this.app, uid); 
          //after, get the target class
          const targetClass = await WalkInStudents.getClassInfoFromClassGroupId(this.app, sch_class_group_id);
          //then we pass the target class  and the origin class 
          await WalkInStudents.confirmWalkinGuestingClass(this.app, targetClass, originalClass, uid);
        }catch(e){
          throw new Errors.GeneralError();
        }
      }
    }
    return data;
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  private async getSchoolGroupByClassroomId(school_class_id:number){
    const schoolClassRecord = <any>await this.app
      .service('db/read/school-classes')
      .get(school_class_id);
    return schoolClassRecord.schl_group_id;
  }
}
