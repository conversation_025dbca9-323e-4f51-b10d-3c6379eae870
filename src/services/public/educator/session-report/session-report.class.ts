import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import axios from 'axios';

interface Data {}

interface ServiceOptions {}

export interface IItemMarkingData {
  [itemId: number]: {
    attempts: {
      [attemptId: number]: {
        flagScoreOption?: string,
        cachedScore?: number,
        scales: {
          [scaleSlug: string]: IScoreOptionMinimal
        }
      }
    }
  }
}

interface IScoreOptionMinimal {
  scoreCode:string,
  scoreValue:number,
}


export class SessionReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const test_session_id = +id

    let students:any[] = [];
    let questions:any[] = [];
    let responses:any[] = [];
    let itemMarks:IItemMarkingData = {};
    
    type ITestDesignInfo = {test_design_id:number, tf_id:number, lang:string, file_path:string, testForm:string, group_type: string}
    let test_designs:ITestDesignInfo[] = await dbRawRead(this.app, {test_session_id}, `
      select  twtar.test_design_id 
          , twtar.slug
          , twtar.id twtar_id 
          , tf.id tf_id 
          , tf.lang 
          , tf.file_path 
          , twtt.is_perusal_allow
          , twtt.perusal_configs
          , twtar.perusal_type
          , twtar.perusal_end_type
          , twtar.perusal_offset_hours
          , twtar.perusal_duration_hours
          , twtar.perusal_date_start
          , twtar.perusal_date_end
          , twtt.is_local_score
          , twtt.is_download_results
          , twtt.is_bulk_print
          , tw.type_slug group_type
          , ts.closed_on
          , ts.date_time_start as ts_date_time_start
          , min(ta.started_on) as first_ta_started_on
          , tw.date_start tw_date_start
          , tw.date_end tw_date_end
          , twtar.test_date_end
          , CASE WHEN (tw.is_active = 1 and tw.date_start < now() and tw.date_end > now()) THEN 1 ELSE 0 END is_tw_current
      from school_class_test_sessions scts
      join test_sessions ts 
        on ts.id = scts.test_session_id
      join test_window_td_alloc_rules twtar
        on twtar.type_slug = scts.slug
        and ts.test_window_id = twtar.test_window_id
      join test_forms tf
        on tf.test_design_id = twtar.test_design_id
      left join test_windows tw
        on tw.id = twtar.test_window_id
      left join test_window_td_types twtt
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      left join test_attempts ta
        on ta.test_session_id = ts.id
        and ta.is_invalid = 0
      where  scts.test_session_id = :test_session_id
      group by tf.id
    `);
    for (let tf of test_designs){
      const formUrl = generateS3DownloadUrl(tf.file_path, 60);
      const formData = await axios.get(formUrl, {});
      tf.testForm = formData?.data;
    }

    const test_design_ids = test_designs.map(td => td.test_design_id)

    // pull question configs
    if(test_design_ids.length == 0) {
      test_design_ids.push(-1); // Fix 5XX
    }
    questions = await dbRawRead(this.app, {test_design_ids}, `
      select tq.id
            , tq.config
            , tqr.tqsi_id
            , tqsi.is_human_scored
            , 1 weight -- todo weight should not be fixed to 1 for all items
      from test_question_register tqr
      join test_questions tq 
        on tq.id = tqr.question_id
      left join test_question_scoring_info tqsi 
        on tqsi.id = tqr.tqsi_id
      where tqr.test_design_id in (:test_design_ids)
    `)

    responses = await dbRawRead(this.app, {test_session_id}, `
      select  tqr.question_id test_question_id 
          , tqr.question_label
          , twtar.test_window_id 
          , twtar.test_design_id 
          , tf.test_design_id response_test_design_id
          , twtar.slug
          , twtar.lang
          , twtar.id twtar_id 
          , ta.uid
          , ta.id attempt_id
          , ta.is_submitted
          , taqr.score 
          , IFNULL(tqr.score_points, taqr.weight) weight
          , taqr.response_raw 
          , taqr.updated_on 
      from school_class_test_sessions scts
      join test_sessions ts 
        on ts.id = scts.test_session_id
      join test_window_td_alloc_rules twtar
        on twtar.type_slug = scts.slug
        and ts.test_window_id = twtar.test_window_id
      join test_question_register tqr
        on tqr.test_design_id = twtar.test_design_id
      join test_attempts ta 
        on ta.twtdar_id = twtar.id
        and ta.test_session_id = scts.test_session_id
        and ta.is_invalid = 0
      join test_forms tf
        on tf.id = ta.test_form_id
      join test_attempt_question_responses taqr 
        on taqr.test_attempt_id = ta.id 
        and taqr.is_invalid = 0
        and taqr.test_question_id = tqr.question_id
      where scts.test_session_id = :test_session_id
      group by taqr.id
      order by tqr.question_label -- todo: order should be from the form, might be cached in TQR (but doesnt have to be for this purpose)
    `);

    if (responses.length){
      
      for (let response of responses){
        if (response.response_raw){
          const {formatted_response} = await this.app
            .service('public/student/extract-item-response')
            .processResponse(response.response_raw, response.test_question_id);
          response.responseText = formatted_response;
        }
      }
      const uids = [... new Set(responses.map(r => r.uid))];

      const overrides = await this.applyTqerScoreOverrides(responses);

      // pull students (todo:generalize)
      students = await dbRawRead(this.app, {uids}, `
        select u.id uid
            , u.first_name 
            , u.last_name 
            , um_dob.value dob
            , um_sin.value student_gov_id
        from users u 
        left join user_metas um_dob 
          on um_dob.uid = u.id 
          and um_dob.key = 'DateofBirth' -- todo:WHITELABEL
        left join user_metas um_sin 
          on um_sin.uid = u.id 
          and um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber') -- todo:WHITELABEL
        where u.id in (:uids)
        order by u.last_name, u.first_name
      `)

      // Add student accommodation
      const group_type = test_designs.map(td => td.group_type)
      const presetOfAccommodations: any[] = await dbRawRead(this.app, {group_type}, this.getAccommsNoUID());
      const allStudentsAccommodations: any[] = await dbRawRead(this.app, {group_type, student_uid: uids}, this.getAccommsWithUID());
      if(presetOfAccommodations && presetOfAccommodations.length > 0){
        for (let student of students){
          let setOfAccommodations: any[] = presetOfAccommodations.filter( accommodations => !accommodations.uid);
          if(student.uid) {
            const studentsAccommodations = allStudentsAccommodations.filter( accommodations => accommodations.uid == student.uid);
            //check if the student already has an accommodation that is outside of the current class's list and add them
            for(let accomm of studentsAccommodations){
              const index = setOfAccommodations.findIndex(all_accomm => all_accomm.accommodation_slug == accomm.accommodation_slug);
              //if we don't find the existing accommodations in the class's list then we add it.
              if(index == -1){
                setOfAccommodations.push(accomm);
              }else{
                //if we do find the existing accommodations in the class's list then we make sure to bring over the existing value
                setOfAccommodations[index] = accomm;
              }
            }
            for(let accomm of setOfAccommodations){
              if(accomm.type == 'checkbox'){
                accomm.value = accomm.value == 1;//turn the value from string to boolean.
              }
            }
          }
          student.accommodation = setOfAccommodations;
        }
      }
      
      /*
        Pull local scoring order by the latest tls created
        when local score is updated in new test session, it will create a new tls and overwirte the older
      */ 
      const localScoringRecords:{attempt_id:number, item_id:number, data:string}[] = await dbRawRead(this.app, {test_session_id}, `
      select tls.attempt_id
      , tls.item_id
      , tls.data
      from temp_local_scoring tls
      join (select MAX(tls.id) tls_id
      , tls.attempt_id
      , tls.item_id
      , tls.data
      from test_attempts ta  
      join temp_local_scoring tls
        on ta.id = tls.attempt_id
      where ta.test_session_id = :test_session_id
      and ta.is_invalid != 1
      group by tls.attempt_id, tls.item_id
      ) t
      on tls.id = t.tls_id
      `)
      itemMarks = {};
      for (let record of localScoringRecords){
        const {attempt_id, item_id, data} = record;
        try {
          const scoringData = JSON.parse(data);
          if (!itemMarks[item_id]){
            itemMarks[item_id] = {attempts:{}}
          }
          itemMarks[item_id].attempts[attempt_id] = scoringData
        }
        catch(e){
          
        }
      }
    }

    // Filter test_designs based on responses if there is more than one test design
    const responseTestDesignIds = [...new Set(responses.map(r => r.test_design_id))];
    if (test_designs.length > 1 && responses.length > 0) {
      test_designs = test_designs.filter(td => responseTestDesignIds.includes(td.test_design_id));
    }


    let entryIdRemappings:any[] = [];
    const responseActualTestDesignIds = [...new Set(responses.map(r => r.response_test_design_id))];
    const currentTestDesignIds = [...new Set(responses.map(r => r.test_design_id))];
    const dbQueryParams = {td_ids_response: responseActualTestDesignIds, td_ids_layout: currentTestDesignIds}
    if (responseActualTestDesignIds.length > 0 && currentTestDesignIds.length > 0) {
      entryIdRemappings = await dbRawRead(this.app, dbQueryParams, `
        select tdieir.td_id_response
             , tdieir.td_id_layout
             , tdieir.config
        from test_design_item_entry_id_remapping tdieir 
        where tdieir.td_id_response in (:td_ids_response)
          and tdieir.td_id_layout in (:td_ids_layout)
          and tdieir.is_revoked = 0 
      `)
    }


    // Filter questions based on responses if there is more than one test design
    // if (questions.length > 1 && responses.length > 0) {
    //   const responseQuestionIds = [...new Set(responses.map(r => r.test_question_id))];
    //   questions = questions.filter(q => responseQuestionIds.includes(q.id));
    // }

    if (responses.length == 0){
      test_designs = [test_designs[0]] // todo: do not show any test designs until students start writing?
      questions = [];
    }

    return {
      test_designs,
      students,
      questions,
      responses,
      itemMarks,
      entryIdRemappings,
    }

  }

  // todo: assign a type to the responses
  async applyTqerScoreOverrides(responses:{test_question_id?:number, item_id?:number, lang: string, responseText: string, test_window_id:number, score:number, hasScoreOverride?:boolean}[]){
    type IOverride = {
      test_question_id:number, 
      lang:string, 
      formatted_response:string, 
      score_override:number
    }
    try {
      const item_ids = responses.map(r => r.test_question_id || r.item_id);
      const test_window_id = responses[0].test_window_id // assuming just one
      const twSetting:{is_allow_tqer_live_override:number} = await dbRawReadSingle(this.app, {test_window_id}, `
        select tw.id
             , tw.is_allow_tqer_live_override  
        from test_windows tw 
        where tw.id = :test_window_id
      `)
      if (twSetting.is_allow_tqer_live_override == 1 && item_ids.length > 0) {
        const overrides:IOverride[] = await dbRawRead(this.app, {item_ids}, `
          select tqer.id
              , tqer.item_id test_question_id
              , tqer.lang
              , tqer.formatted_response
              , tqer.score_override 
          from test_question_expected_responses tqer
          where tqer.item_id in (:item_ids)
            and tqer.is_revoked = 0
            and tqer.is_item_score_exceptions = 1
          order by tqer.item_id,tqer.lang,tqer.created_on 
        `);
        for (let response of responses){
          for (let override of overrides){
            // todo: map could be more efficient, but would be multi-layered
            const isMatch = (response.test_question_id == override.test_question_id) &&
                            (response.lang == override.lang) &&
                            (response.responseText == override.formatted_response)
            if (isMatch) {
              if (response.score != override.score_override){
                response.score = override.score_override;
                response.hasScoreOverride = true
              }
            }
          }
        }
        return overrides
      }
    }
    catch(e){
    }
    return []
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
  // SQL from student accommodation
  getAccommsNoUID() {
    return `
      select 
        acc.id
        ,acc.name
        ,acc.system_slug
        ,acc.accommodation_slug
        ,acc.accommodation_type as type
        ,acc.has_extra_notes 
      from  
        accommodations acc
      where 
        acc.is_revoked = 0
        and acc.system_slug in (:group_type)
      order by acc.sortOrder;
      `;
  }
  // SQL from student accommodation and modify for multiple uids
  getAccommsWithUID(){
    return `
      select 
        u_acc.uid uid
        ,acc.id
        ,acc.name
        ,acc.system_slug
        ,acc.accommodation_slug
        ,acc.accommodation_type as type
        ,acc.has_extra_notes 
        ,u_acc.accommodation_value AS value
        ,u_acc.extra_notes_value
      from  
        users_accommodations u_acc
      inner join accommodations acc
        on  acc.id = u_acc.accommodation_id 
        and acc.is_revoked = 0
      where 
        u_acc.is_revoked = 0
        and u_acc.uid in (:student_uid)
        and u_acc.accommodation_value = 1
      order by acc.sortOrder;
    `;
  }
}
