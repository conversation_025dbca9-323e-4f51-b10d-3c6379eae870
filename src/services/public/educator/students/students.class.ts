import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { data } from '../../testlets/loft/data/data-sample';
import { Knex } from 'knex';
import { ISchoolClass } from '../../dist-admin/student/student.class';
import { IUserRole } from '../../../db/schemas/user-roles.schema';
import { IUserMeta } from '../../auth/test-taker/test-taker.class';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { STUDENT_ROLE_TYPES } from '../walk-in-students/walk-in-students.class';
import { SysFlags } from '../../support/sys-flags/sys-flags.class';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { dbDateNow } from '../../../../util/db-dates';
import { FLAGS, isABED } from '../../../../util/whiteLabelParser';
import { clone, cloneDeep } from 'lodash';
import { WalkInStudents } from '../walk-in-students/walk-in-students.class';
import { SQL_GUEST_STU_ROLE_EXIST } from '../session/model/sql';


interface IRequest {
  type:string;
  group_ids?:number[];
  session:string;
}

interface ISCRevokeReq {
  group_id: number
  ur_id: number
}


export const map_key_to_key_namespace: {[key:string]: string} = {
  'StudentOEN'  : 'eqao_sdc',
  'NBED_UserId' : 'nbed_sdc',
  'MBED_UserId' : 'mbed_sdc',
  'SASN'        : 'eqao_sdc',
  'StudentIdentificationNumber' : 'abed_course'
}

interface Data {}

interface ServiceOptions {}

export class Students implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params){
      const {value, schoolClassId, school_class_group_id, sasn_login, isASNPasiSyncAttempted} = <any> params.query;
      return await this.getStudentOenInSchoolByClassroom(params, value, schoolClassId, +sasn_login, isASNPasiSyncAttempted);
    }
    throw new Errors.BadRequest();
  }

  // async getStudentSecretIdInSchoolByClassroom(value, schoolClassId){

  // }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: any, params?: Params): Promise<any> {
    if (params) {
      const {school_class_group_id} = <any> params.query;
      const {student, schoolClassId, isSasnLogin, ASN, semesterId, schoolGroupId} = <any> data;
      const created_by_uid = await currentUid(this.app, params);
      const nowTime = dbDateNow(this.app);

      // entry here only once, when an ASN look up is done and no records are found
      if (params?.query?.tryPASIStudentSync === 1) {
        const schoolYearInfo = await this.app.service("public/abed-pasi").getPASISchoolYearFromSemesterId(semesterId);

        if (schoolYearInfo != null) {
          try {

            // find school
            const schoolData = <Paginated<any>> await this.app
            .service('db/read/schools')
            .find({ query: {
              $select: ['foreign_id', 'id', 'group_id'],
              group_id: schoolGroupId,
              $limit: 1,
            }});

            const teacherSchoolCode = schoolData.data[0].foreign_id;

            // find SSE records, and search for any belonging to the school of the teacher
            // Remove this restriction
            // const sseRecords = await this.app.service("public/abed-pasi").retrieveSSERecords(ASN, schoolYearInfo.PASISchoolYear);
            
            // if (sseRecords.filter(record => {
            //   return this.app.service("public/abed-pasi").formPASIStyleOrgCode(true, record.SchoolCode) === teacherSchoolCode;
            // }).length === 0) {
            //   // Do not sync the student from pasi_data to mpt_dev unless they have an enrolment record
            //   // belonging to the school the teacher is currently triggering this from
            //   // Simply return no students found in this case, even if an ASN actually exists in pasi_data.students.
            //   return [];
            // }

            // try sync ASN from PASI schema
            const studentUid = await this.app.service("public/abed-pasi").trySyncASNFromPASISchema(ASN, nowTime, schoolYearInfo.PASISchoolYear,
            schoolYearInfo.testWindowId, semesterId, schoolYearInfo.typeSlug, schoolYearInfo.pasiGradeCode);

            // create school_class user_role for the placeholder class of the same school, test window and semester
            // as the school class the teacher is currently in
            // this is to allow the student to be displayed on the UI, and the quick add feature to work
            await this.app.service("public/abed-pasi").
            createSchoolClassUserRole(schoolGroupId, semesterId, studentUid, nowTime, created_by_uid);

            let paramsMod = cloneDeep(params);
            paramsMod.query = {
                value: ASN,
                schoolClassId,
                school_class_group_id,
                isASNPasiSyncAttempted: 1
            };

            // at this point, looking up the ASN again will ALWAYS find a match, so return the data as expected
            return await this.find(paramsMod);
          }

          catch(e: any) {
            // console.log(e);
            // if 403 error , display it (coming from this ASN is a secondary ASN error - only forbidden error in above block)
            if (e?.code === 403) {
              throw(e);
            }
            // retry when pasi done if not a 403 error. User role and user metas could be created even placeholder class doesn't exist
            let paramsMod = cloneDeep(params);
            paramsMod.query = {
                value: ASN,
                schoolClassId,
                school_class_group_id,
                isASNPasiSyncAttempted: 1
            };
            return await this.find(paramsMod);
          }
        }

        else {
          return [];
        }
      }

      const newStudent = await this.app
        .service('public/dist-admin/student')
        .createStudentRecordForClassroom(
          student,
          schoolClassId,
          created_by_uid,
          // +isSasnLogin
        )
      await this.app
        .service('public/school-admin/student-asmt-info-signoff')
        .revokeSchStuAsmtSignoffByClassId(schoolClassId, created_by_uid);
      await this.app.service('public/school-admin/classes').updateStudentFrenchImm(schoolClassId, newStudent , created_by_uid);
      return newStudent;
    }
    return [];
  }

  async update (id: NullableId, data: any, params?: Params): Promise<Data> {
    //added this section to update student info
    if(id && data && params){
      const student = data.student;
      const uid = +id;
      //dob isn't editable anymore and we want the teacher's note to be editable, so I simply swapped the two fields
      let userMetaPatchLookup:any = {
        uid,
        key: 'TeacherNotes',
      };
      await this.updateStudentUserMeta(userMetaPatchLookup, student.teacher_notes);
      await this.app.service('db/write/users').patch(id, {
        first_name: student.first_name,
        last_name: student.last_name,
      });
      return data;
    }
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    // this method is currently used for moving students from one class to another
    if (id  && params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      const {school_class_group_id, keepOldTWRecord, ASN, crossSchool} = params.query;

      const whiteLabel = this.app.get('whiteLabel');
      if (isABED(whiteLabel as FLAGS)) {
        await this.ensureASNIsNotSecondaryASN(ASN);
      }
      
      const studentUid = +id;

      if (await getSysConstNumeric(this.app, 'ENFORCE_STU_1_TW_CLASS')){
        await this.revokeStudentSCinSameWindow(studentUid, school_class_group_id, keepOldTWRecord, created_by_uid)
      }
      // await this.reflectClassAssignmentInUserMeta(studentUid, school_class_group_id)// todo: generalize if we want to continue using

      if (+crossSchool){
        //if this student is being added from another school then we run the already tested and confirmed guesting process
        const originalClass = await WalkInStudents.getSchoolClassFromUID(this.app, studentUid);
        const targetClass = await WalkInStudents.getClassInfoFromClassGroupId(this.app, school_class_group_id);
        await WalkInStudents.confirmWalkinGuestingClass(this.app, targetClass, originalClass, studentUid);
      }else{
        // assign new role
        await this.app.service('auth/user-role-actions').assignUserRoleToGroup({
          uid: studentUid,
          group_id: school_class_group_id,
          role_type: DBD_U_ROLE_TYPES.schl_student,
          created_by_uid
        });
      }

      //preserve um after the assignment is done. //todo:clarify what this meanss
      await this.preserveStudentUserMeta(studentUid, created_by_uid);
      return <any> {}
    }
    throw new Errors.BadRequest();
  }

  async reflectClassAssignmentInUserMeta(studentUid:number, school_class_group_id:number){
      // ensure that when a student is added to a G9/G10 class, they are given the appropraite user meta as well
      // todo: generalize if we want to continue using
      const targetClassRecords = <Paginated<any>> await this.app.service('db/read/school-classes').find({
        query: {
          group_id: school_class_group_id,
        }
      });
      const targetClass = targetClassRecords.data[0];
      // todo: generalize... key_namespace: 'eqao_sdc', EQAO_G10:IS_G10, EQAO_G9:IS_G9
      // if (userMetaPatchLookup.key){ await this.updateStudentUserMeta(userMetaPatchLookup, 1); }
  }

  async moveTestAttemptsBetweenTestSessions(studentUid:number, fromClassGroupId:number, toClassGroupId:number){
    const prev   = await this.getTestAttemptByUIDAndClassGID(studentUid, toClassGroupId);
    const target = await this.getTestAttemptByUIDAndClassGID(studentUid, +fromClassGroupId);
    for(let i = 0; i < prev.length; i++){
      if(prev && target && prev[i].taid && prev[i]?.slug == target[i]?.slug){
        const ts_id = target[i].test_session_id
        const ta_id = prev[i].taid
        await dbRawWrite(this.app, {ts_id, ta_id}, `
          UPDATE test_attempts 
          SET test_session_id = :ts_id
            , active_sub_session_id = NULL 
          WHERE id = :ta_id;
        `);

        await dbRawWrite(this.app, {
          ts_id: target[i].test_session_id, 
          sub_session_id: target[i].sub_session_id, 
          id: prev[i].test_attempt_sub_session_id}, `
          UPDATE test_attempt_sub_sessions 
          SET test_session_id = :ts_id, 
            sub_session_id = :sub_session_id 
          WHERE id = :id;
        `);
      }
    }
  }

  async revokeStudentSCinSameWindow(studentUid:number, school_class_group_id:number, keepOldTWRecord:number | boolean, created_by_uid:number){
      // validate that UID is in the same school, etc
      const schoolClassesToRevoke:ISCRevokeReq[] = await dbRawRead(this.app, [school_class_group_id, studentUid, DBD_U_ROLE_TYPES.schl_student], `
        select ur.id ur_id, sc2.group_id
        from school_classes sc1
        join school_semesters ss1
          on ss1.id = sc1.semester_id
          and sc1.group_id = ?
        join test_windows tw1
          on tw1.id = ss1.test_window_id
        join test_windows tw2
          on tw2.is_active = 1
         and tw2.type_slug = tw1.type_slug
        join school_semesters ss2
        ${keepOldTWRecord ? 
          `on ss1.test_window_id = ss2.test_window_id` : 
          `on ss2.test_window_id = tw2.id` }
        join school_classes sc2
          on ss2.id = sc2.semester_id
          and sc2.schl_group_id = sc1.schl_group_id
        join user_roles ur
          on ur.group_id = sc2.group_id
          and ur.uid = ?
          and ur.role_type = ?
          and ur.is_revoked = 0
        group by sc2.group_id
      `);

      // revoke old role if the other class is in the same test window
      if (schoolClassesToRevoke.length){
        //preserve um before revoking
        await this.preserveStudentUserMeta(studentUid, created_by_uid);// todo: what exactly does this accomplish?
        for (let schoolToRevoke of schoolClassesToRevoke){
          await this.app.service('auth/user-role-actions').revokeUserRoleFromGroup(studentUid, schoolToRevoke.group_id, DBD_U_ROLE_TYPES.schl_student, created_by_uid);
          await this.moveTestAttemptsBetweenTestSessions(studentUid, school_class_group_id, schoolToRevoke.group_id)
        }
      }
  }

  async updateStudentUserMeta(userMetaPatchLookup:{uid:number, key_namespace:string, key:string}, value:number|string='#'){
    const existingUserMetaRecords = <Paginated<any>> await this.app .service('db/read/user-metas') .find({ query: userMetaPatchLookup });
    if (existingUserMetaRecords.total > 0){
      const id = existingUserMetaRecords.data[0].id;
      await this.app.service('db/write/user-metas').patch(id, { value });
    }
    else{
      await this.app.service('db/write/user-metas').create({ ...userMetaPatchLookup, value });
    }
  }

  async updateTWStudentUserMeta(userMetaPatchLookup:{uid:number, key_namespace:string, key:string, test_window_id: number}, value:number|string='#', created_by_uid: number){
    const existingUserMetaRecords = <Paginated<any>> await this.app .service('db/read/tw-user-metas') .find({ query: userMetaPatchLookup });
    if (existingUserMetaRecords.total > 0){
      const id       = existingUserMetaRecords.data[0].id;
      const foundVal = existingUserMetaRecords.data[0].value;
      if(foundVal != value) {
        await this.app.service('db/write/tw-user-metas').patch(id, { value, updated_by_uid: created_by_uid, });
      }
    }
    else{
      await this.app.service('db/write/tw-user-metas').create({...userMetaPatchLookup, created_by_uid, value });
    }
  }

  async preserveStudentUserMeta(studentUID: number, created_by_uid: number){
    const userMetasToConfirm = await dbRawRead(this.app, {studentUID}, `
        SELECT 
          ss.test_window_id, um.*
        FROM 
          school_classes sc
        JOIN school_semesters ss 
          ON ss.id = sc.semester_id
        JOIN user_roles ur 
          ON ur.group_id = sc.group_id 
          AND ur.is_removed = 0 
          AND ur.is_revoked = 0
        JOIN user_metas um 
          ON um.uid = ur.uid
        WHERE 
          ur.uid = :studentUID
          AND sc.is_active = 1
        ;`);
    const existingTWUserMeta = await dbRawRead(this.app, {studentUID}, `
        SELECT 
          tum.test_window_id
        FROM 
          tw_user_metas tum
        WHERE 
          tum.uid = :studentUID
        ;`);
    //the result of the query is the student's user metas and the test window of the classes they are in. 
    //if the student exists in multiple test windows then the same set of the user metas will appear for each of the relevant test windows
    //then i use that info by passing it to the `updateTWStudentUserMeta` function which:
    //If it finds the tw_user_metas version of the user's user_metas then it would update the records only if the value is different, 
    //If there is no record it will make a new one.
    for(let um of userMetasToConfirm){
      //if we have a tw_user_metas record already then don't change it.
      if(existingTWUserMeta.findIndex(etum => etum.test_window_id == um.test_window_id) != -1) continue;
      await this.updateTWStudentUserMeta(
        {
          uid: um.uid,
          key_namespace: um.key_namespace,
          key: um.key,
          test_window_id: um.test_window_id
        },
        um.value,
        created_by_uid
      );
    }
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {

    if (id && params?.query){
      const sc_group_id:number = +id;
      const {studentUid} = params.query;
      const created_by_uid = await currentUid(this.app, params);

      // Check if the student is a guest student
      const guestStudentRow = await dbRawRead(
        this.app,
        { group_id: sc_group_id, studentUid },
        SQL_GUEST_STU_ROLE_EXIST
      );

      if (guestStudentRow.length > 0) {
        // If the student is a guest, remove them from the guest class
        const guestClassGroupId = guestStudentRow[0].guest_class_group_id;
        await this.app.service('auth/user-role-actions').revokeUserRoleFromGroup(
          studentUid,
          guestClassGroupId,
          DBD_U_ROLE_TYPES.schl_student,
          created_by_uid
        );
        // Return here for guest students, as the rest of the logic is for regular students
        return { sc_group_id: guestClassGroupId, studentUid };
      }

      // If the student is a regular student, proceed with the existing logic    
      await this.app.service('auth/user-role-actions').revokeUserRoleFromGroup(studentUid, sc_group_id, DBD_U_ROLE_TYPES.schl_student, created_by_uid);
      // check if the student is still part of any other similar class
      type SCAlt = {group_id: number, is_placeholder: number}
      const scAlts:SCAlt[] = await dbRawRead(this.app, {sc_group_id}, `
        select sc_other.group_id
             , sc_other.is_placeholder 
            -- , sc_other.name 
        from school_classes sc 
        join school_classes sc_other
          on sc.semester_id = sc_other.semester_id
          and sc.schl_group_id = sc_other.schl_group_id
          and sc.id != sc_other.id
        where sc.group_id = :sc_group_id
      `)
      const scAltsGroupIds = [];
      let scPlaceholderGroupId:number | undefined;
      for (let scAlt of scAlts){
        scAltsGroupIds.push(scAlt.group_id);
        if (scAlt.is_placeholder==1){
          scPlaceholderGroupId = scAlt.group_id;
        }
      }
      if (scAltsGroupIds.length){
        // check for student role in any of the other classes
        const activeUserRoles = await dbRawRead(this.app, {studentUid, scAltsGroupIds}, `
          select id
          from user_roles ur 
          where ur.uid = :studentUid
            and ur.group_id IN (:scAltsGroupIds)
            and ur.is_revoked = 0
        `)
        if (activeUserRoles.length == 0 && scPlaceholderGroupId){
          // add tp placeholder group
          await this.app.service('auth/user-role-actions').assignUserRoleToGroup({
            uid: studentUid, 
            group_id: scPlaceholderGroupId, 
            role_type: DBD_U_ROLE_TYPES.schl_student, 
            created_by_uid
          });
        }
      }

      return {sc_group_id, studentUid}
    }
    throw new Errors.MethodNotAllowed();
  }

  getKeyAndNamespace(schoolClassEntry:ISchoolClass): {key: string, key_namespace: string}{
    let {key} = schoolClassEntry;
    if(!key) key = 'StudentOEN'  // default   
    let key_namespace = map_key_to_key_namespace[key];
    
    return {
      key,
      key_namespace
    }
  }
  
  async getStudentOenInSchool(params: any, value:string, schoolClassEntry:ISchoolClass, test_window_id:number, isASNPasiSyncAttempted: 0 | 1 = 0) {
    let {schl_group_id, group_id, id, semester_id} = schoolClassEntry;
    const {key, key_namespace} = this.getKeyAndNamespace(schoolClassEntry);
    const abedWhiteLabel = this.app.get('whiteLabel');
    
    // const additionalMetas = ['Gender', 'Program'];
    const studentMetas = <Paginated<any>> await this.app
      .service('db/read/user-metas')
      .find({ query: {
        $select: ['uid', 'value'],
        //relying on the class's keynamespace is not reliable as it is not consistent with the entire student body of a given school.
        key_namespace: {$in: ['abed_sdc', 'abed_course']},
        key,
        value,
        $limit: 100,
      }});
    const uids = studentMetas.data.map(meta => meta.uid);

    // get test windows
    const activeTestWindows = <Paginated<any>> await this.app
    .service('db/read/test-windows')
    .find({ query: {
      $select: ['id'],
      is_active: 1,
      $limit: 1000,
    }});
    const activeTestWindowIds = activeTestWindows.data.map(tw => tw.id);

    // get eligible semesters
    const schoolSemesters = <Paginated<any>> await this.app
      .service('db/read/school-semesters')
      .find({ query: {
        $select: ['id', 'test_window_id'],
        test_window_id: {$in: activeTestWindowIds},
        $limit: 1000,
      }});
    const semesterIds = schoolSemesters.data.map(semester => semester.id);
    const semesterToTestWindow = new Map();
    schoolSemesters.data.forEach(s => semesterToTestWindow.set(s.id, s.test_window_id) )

    // get eligible classrooms
    const schoolClasses = <Paginated<any>> await this.app
      .service('db/read/school-classes')
      .find({ query: {
        $select: ['id', 'name', 'group_id', 'semester_id'],
        schl_group_id,
        semester_id: {$in: semesterIds},
        $limit: 1000,
      }});
    const schoolClassGroupIds = schoolClasses.data.map(classes => classes.group_id);
    const schoolClassGroupToTestWindow = new Map();
    schoolClasses.data.forEach(sc => schoolClassGroupToTestWindow.set(sc.group_id, semesterToTestWindow.get(sc.semester_id)) );

    // get eligible Guest Classroom(s)
    const schoolGuestClasses =  await dbRawRead(this.app, {schl_group_id, semesterIds}, `
      SELECT sc.id as id,
          sc.name as name,
          sc.group_id as group_id,
          scg.guest_sc_group_id as guest_sc_group_id,
          sc.semester_id as semester_id
      FROM school_classes sc
      JOIN school_classes_guest scg 
        on sc.group_id = scg.invig_sc_group_id 
      WHERE sc.schl_group_id = :schl_group_id 
        and sc.semester_id in (:semesterIds)
      ;`);
    const schoolGuestClassGroupIds = schoolGuestClasses.map(classes => classes.guest_sc_group_id);
    const schoolGuestClassGroupToTestWindow = new Map();
    schoolGuestClasses.forEach(sc => schoolGuestClassGroupToTestWindow.set(sc.group_id, semesterToTestWindow.get(sc.semester_id)) );

    // Merge both Maps into one
    const combinedSchoolClassGroupToTestWindow = new Map([
      ...schoolClassGroupToTestWindow,
      ...schoolGuestClassGroupToTestWindow
    ]);

    //since this check doesn't specifiy student's role_type, that means it will pull regualr and walk-in students, any student that is linked to any class in the current school
    const userRoles = <Paginated<IUserRole>> await this.app
      .service('db/read/user-roles')
      .find({ query: {
          $select: ['uid', 'group_id'],
          group_id: {$in: [...schoolClassGroupIds, schl_group_id]},
          is_revoked: 0,
          uid: {$in: uids},
          $limit: 100,
      }});

    const guestUserRoles = <Paginated<IUserRole>> await this.app
    .service('db/read/user-roles')
    .find({ query: {
        $select: ['uid', 'group_id'],
        group_id: {$in: schoolGuestClassGroupIds},
        is_revoked: 0,
        uid: {$in: uids},
        $limit: 100,
    }});

    // Combine guestUserRoles and userRoles into a single array
    const combinedUserRoles = {
      data: [...userRoles.data, ...guestUserRoles.data]
    };

    const crossSchoolUserRoles = <Paginated<IUserRole>> await this.app
    .service('db/read/user-roles')
    .find({ query: {
        $select: ['uid'],
        is_revoked: 0,
        uid: {$in: uids},
        $limit: 100,
    }});
    
    // Make sure we get Unique user Ids
    const validUids = [...new Set( [...crossSchoolUserRoles.data.map(r => r.uid)] )];

    const studentInSameTW = new Map();
    combinedUserRoles.data.map(ur => {
      const tw = combinedSchoolClassGroupToTestWindow.get(ur.group_id)
      if (+test_window_id === +tw){
        studentInSameTW.set(ur.uid, true)
      }
    });

    // we don't need to through an error here, all cases are covered through the ui, this passes as an alert, which is inconsistent and redundant
    // if((userRoles.data.length > 0 && studentInSameTW.size == 0) || (walkinUserRoles.data.length > 0 && userRoles.data.length == 0)){
    //   throw new Errors.BadRequest('STUDENT_NOT_EXIST_IN_COURSE');
    // }

    // Get user full metas
    let student_meta:any[] = []
    if(validUids.length > 0){
      student_meta = await dbRawRead(this.app, [validUids], `
        select um.uid, um.key_namespace, um.key, um.value
        from user_metas um
        where um.uid IN (?)
      ;`);
    }
    // Get all Student Classes
    const studentClasses: any[] = [];
    combinedUserRoles.data.forEach(ur => {
      const classroom = schoolClasses.data.find(cl => cl.group_id === ur.group_id);
    
      if (classroom) {
        const semesterTW = semesterToTestWindow.get(classroom.semester_id);
        studentClasses.push({ ...classroom, isInSameTW: +test_window_id === +semesterTW });
      } else {
        // If not found in schoolClasses, check in schoolGuestClasses
        const guestClassroom = schoolGuestClasses.find(cl => cl.guest_sc_group_id === ur.group_id);
    
        if (guestClassroom) {
          const semesterTW = semesterToTestWindow.get(guestClassroom.semester_id);
          studentClasses.push({ ...guestClassroom, isInSameTW: +test_window_id === +semesterTW });
        }
      }
    });

    // load validated user infos
    const users = <Paginated<IUserRole>> await this.app
      .service('db/read/users')
      .find({ query: {
          $select: ['id', 'first_name', 'last_name'],
          id: {$in: validUids},
          $limit: 100,
      }});
    // const validStudentMetas = <Paginated<IUserMeta>> await this.app
    //   .service('db/read/user-metas')
    //   .find({ query: {
    //       $select: ['uid', 'key', 'value'],
    //       key_namespace,
    //       uid: {$in: validUids},
    //       key: {$in: additionalMetas},
    //       $limit: 100,
    //   }});

    const usersExpanded = users.data.map(user => {
      const userExp:any = {
        ... user,
        isInSameTW: !!studentInSameTW.get(user.id),
        crossSchool: !!(!userRoles.data.length && crossSchoolUserRoles.data.length),
        meta: student_meta,
        classrooms: studentClasses
      }
      // student_meta.forEach(userMeta => {
      //   if (user.id === userMeta.uid){
      //     userExp.meta[userMeta.key] = userMeta.value;
      //   }
      // });
      return userExp;
    });

    // console.log(usersExpanded, isASNPasiSyncAttempted, studentMetas.data, value);
    // if 0 users found, we have not already attempted a PASI student search and sync, and we are ABED
    // attempt to sync the student from `pasi_data`
    // Remove student meta not exist as a condition since user roles can update
    if (usersExpanded.length === 0 && isABED(abedWhiteLabel as FLAGS) && !isASNPasiSyncAttempted) {
      let paramsMod = cloneDeep(params);
      paramsMod.query = {
        school_class_group_id: group_id,
        tryPASIStudentSync: 1
      }

      // this basically trigger entry into this same function again
      // but this time guranteeing a match, returning expected data
      return await this.create({
        schoolClassId: id, 
        semesterId: semester_id, 
        ASN: value, 
        schoolGroupId: schl_group_id}, paramsMod);
    }

    return usersExpanded;
  }

  async getStudentOenInSchoolByClassroom(params: any, value:string, schoolClassId:number, sasn_login = 0, isASNPasiSyncAttempted: 0 | 1 = 0) {
    if (value){
      const schoolClassEntry = <ISchoolClass> await this.app
        .service('db/read/school-classes')
        .get(schoolClassId);
        if (schoolClassEntry.semester_id){
          const semesterRecord = <any> await this.app
            .service('db/read/school-semesters')
            .get(schoolClassEntry.semester_id);
          return await this.getStudentOenInSchool(params, value, schoolClassEntry, semesterRecord.test_window_id, isASNPasiSyncAttempted);
        }
    }
    throw new Errors.BadRequest();
    // return LOCAL_STATE.activeAssessment;
  }


  async getActiveSessionAccessCodes() {
    // return LOCAL_STATE.activeAssessment;
  }

  private getLocalStateForClassrooms(group_ids?:number[]){
    // return LOCAL_STATE;
  }

  private refreshStudentOnlineStatus(){
    // const timestamp = this.timestamp();
    // const maxdist = 5*1000;
    // Object.values(LOCAL_STATE.studentSummaries).forEach((studentSummary:any) => {
    //   if (studentSummary.isOnline){
    //     if ((timestamp - studentSummary.lastTimeStamp) > maxdist){
    //       studentSummary.numSkipped ++
    //       if (studentSummary.numSkipped >= 2){
    //         // studentSummary.isOnline = false;
    //       }
    //     }
    //   }
    // })
  }

  private timestamp() {
    return (new Date()).valueOf()
  }

  async getTestAttemptByUIDAndClassGID(studentUID: number, school_class_group_id: number){
    const res = await dbRawRead(this.app, {studentUID, school_class_group_id}, `
      SELECT ta.id as taid, tsss.id as sub_session_id, tass.id as test_attempt_sub_session_id, ts.id as test_session_id, scts.slug 
        FROM test_sessions ts
      JOIN school_classes sc 
        ON ts.schl_group_id = sc.schl_group_id 
        AND sc.is_active = 1
      JOIN school_class_test_sessions scts 
        ON scts.test_session_id = ts.id 
        AND scts.school_class_id = sc.id
      JOIN test_session_sub_sessions tsss
        ON tsss.test_session_id = ts.id
      LEFT JOIN test_attempts ta 
        ON ta.test_session_id = ts.id 
        AND ta.is_submitted = 0
        AND ta.uid = :studentUID
      LEFT JOIN test_attempt_sub_sessions tass
        ON tass.test_session_id = ts.id
        AND tass.test_attempt_id = ta.id
      WHERE sc.group_id = :school_class_group_id
        AND ts.is_closed = 0
        AND ts.is_cancelled = 0
      ;`);

    return res;
  }

  async ensureASNIsNotSecondaryASN(ASN: string): Promise<void> {
    const isASNSecondaryASN = await this.app.service("public/abed-pasi").isASNASecondaryASN(ASN);
    if (isASNSecondaryASN) {
      // TODO: confirm slug/error message
      throw new Errors.Forbidden("This ASN is a secondary ASN, so this student cannot be moved to another class.");
    }
  }

}
