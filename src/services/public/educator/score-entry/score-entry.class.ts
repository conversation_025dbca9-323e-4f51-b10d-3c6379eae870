import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import axios from 'axios';
import { DBD_U_ROLE_TYPES } from '../../../../constants/db-extracts';
import { SCORE_ENTRY_ACTION_TYPES } from './model';
import { dbDateNow } from '../../../../util/db-dates';

export const ENTRY_PARAM_DOMAIN_SLUG = 'entry_domain';
export const ENTRY_PARAM_CAPTION_SLUG = 'entry_caption';
export const ENTRY_PARAM_SECTION_SLUG = 'section_slug';

interface Data {
  [key: string]: any,
}

interface ServiceOptions {}

export class ScoreEntry implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  getTQRMapping(tqrMappingRaw: {id: number, item_bank_code: string, tqr_col: string }[]) {
    const tqrMapping: {[key: string]: string} = {};
    tqrMappingRaw.forEach((row) => {
      tqrMapping[row.item_bank_code] = row.tqr_col
    })

    return tqrMapping;
  }

  getQuestionScoreSlugMapping(tqrRows: any[], tqrMapping: {[key: string]: string}) {
    if(!Object.keys(tqrMapping).length) {
      return {};
    }
    const questionScoreSlugMap: any = {};

    tqrRows.forEach((row) => {
      questionScoreSlugMap[row.question_id] = {};

      Object.keys(tqrMapping).forEach((item_bank_code) => {
        const tqrCol = tqrMapping[item_bank_code];
        try {
          questionScoreSlugMap[row.question_id][item_bank_code] = JSON.parse(row[tqrCol]);
        } catch(err) {
          questionScoreSlugMap[row.question_id][item_bank_code] = row[tqrCol];
        }
      })
    });

    return questionScoreSlugMap;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if (!id || !params || !params.query) {
      throw new Errors.BadRequest("MISSING ID OR PARAMS")
    }
    const test_session_id = +id
    const { schl_class_group_id } = params.query
    type ITestDesignInfo = {test_design_id:number, twtar_id: number,  tf_id:number, lang:string, file_path:string, testForm:string, studentAttempts: {}, questionScoreSlugMap: {}, discontinuationRules: any, assessment_description: any}

    const tqrCodes = [ENTRY_PARAM_CAPTION_SLUG, ENTRY_PARAM_DOMAIN_SLUG, ENTRY_PARAM_SECTION_SLUG]
    const tqrMappingRaw = await dbRawRead(this.app, {tqrCodes}, `
      SELECT id, item_bank_code, tqr_col 
      FROM test_question_register_generic_param_map tqrgpm 
      WHERE item_bank_code in (:tqrCodes)
        AND is_revoked = 0;
    `);

    const tqrMapping = this.getTQRMapping(tqrMappingRaw);

    const testDesigns:ITestDesignInfo[] = await dbRawRead(this.app, {test_session_id}, `
      select  twtar.test_design_id 
          , twtar.slug
          , twtar.id twtar_id 
          , tf.id tf_id 
          , tf.lang 
          , tf.file_path 
          , twtt.is_perusal_allow
          , twtt.is_local_score
          , twtt.is_download_results
          , twtt.is_bulk_print
          , rdr.config discontinuationRules
          , sas.caption assessment_description
          , CASE 
              WHEN (twtar.test_date_end IS NOT NULL AND twtar.test_date_end < NOW()) OR tw.date_end < NOW() OR tw.is_closed = 1 THEN 1 
              ELSE 0 
            END AS is_closed
      from school_class_test_sessions scts
      join test_sessions ts 
        on ts.id = scts.test_session_id
      join test_window_td_alloc_rules twtar
        on twtar.type_slug = scts.slug
        and ts.test_window_id = twtar.test_window_id
      join test_forms tf
        on tf.test_design_id = twtar.test_design_id
      join test_designs td
        on td.id = twtar.test_design_id
      left join rp_reporting_profiles rrp 
      	on twtar.reporting_profile_id  = rrp.id
      left join rp_discontinuation_profiles rdp 
      	on rrp.discontinuation_rules_id = rdp.id
      left join rp_discontinuation_rules rdr 
      	on rdr.id = rdp.rule_id 
      left join test_window_td_types twtt
        on twtt.type_slug = twtar.type_slug
        and twtt.test_window_id is null
          and twtt.is_revoked = 0
      left join se_assessment_structures sas
        on sas.id = td.assessment_structure_id
      left join test_windows tw
        on tw.id = twtar.test_window_id
      where  scts.test_session_id = :test_session_id
      group by tf.id
    `);
    for (let tf of testDesigns){
      const formUrl = generateS3DownloadUrl(tf.file_path, 60);
      const formData = await axios.get(formUrl, {});
      tf.testForm = formData?.data;
      // TODO: Add TAQR 
      const taqrRows = await dbRawRead(this.app, [schl_class_group_id, test_session_id, tf.twtar_id], `
      SELECT ta.uid
        , u.first_name firstName 
        , u.last_name lastName
        , um_dob.value dob
        , um_sin.value studentGovId
        , ta.id testAttemptId
        , ta.is_closed isClosed
        , taqr.id taqrId
        , taqr.score
        , taqr.test_question_id questionId
        , tq.config
      FROM mpt_dev.test_attempts ta 
        JOIN mpt_dev.users u 
          ON u.id = ta.uid
        JOIN mpt_dev.user_roles ur
          ON ur.uid = u.id
          and ur.role_type = '${DBD_U_ROLE_TYPES.schl_student}'
          and ur.is_revoked = 0
          and ur.group_id = ?
        LEFT JOIN mpt_dev.user_metas um_dob 
          ON um_dob.uid = u.id 
          AND um_dob.key = 'DateofBirth' -- todo:WHITELABEL
        LEFT JOIN mpt_dev.user_metas um_sin 
          ON um_sin.uid = u.id 
          AND um_sin.key in ('StudentIdentificationNumber', 'TestTakerIdNumber') -- todo:WHITELABEL
        JOIN test_window_td_alloc_rules twtar
          ON twtar.id = ta.twtdar_id
        LEFT JOIN mpt_dev.test_attempt_question_responses taqr
          ON taqr.test_attempt_id = ta.id
        LEFT JOIN mpt_dev.test_questions tq
          ON tq.id = taqr.test_question_id
        WHERE ta.test_session_id = ?
        AND twtar.id = ?
      `)

      const tqrRows = await dbRawRead(this.app, {test_design_id: tf.test_design_id}, `
        SELECT * 
        FROM test_question_register tqr
        WHERE tqr.test_design_id = :test_design_id;
      `);

      const questionScoreSlugMap = this.getQuestionScoreSlugMapping(tqrRows, tqrMapping);

      const studentAttempts = {} as any;

      if(tf.discontinuationRules && typeof tf.discontinuationRules === 'string') {
        try {
          tf.discontinuationRules = JSON.parse(tf.discontinuationRules);
        } catch(err) {
          console.error(err);
        }
      }

      // Loop through the retrieved taqr rows
      for (const row of taqrRows) {
          const {
              uid,
              firstName,
              lastName,
              dob,
              studentGovId,
              testAttemptId,
              isClosed,
              taqrId,
              questionId,
              score,
              config,
          } = row;

          // Initialize the nested structure if not already present
          if (!studentAttempts[uid]) {
              studentAttempts[uid] = {
                uid,
                firstName,
                lastName, 
                dob,
                studentGovId,
                testAttemptId,
                isClosed,
                questions: {},
                total: 0
              };
          }

          // Assign taqr data to the nested structure
          studentAttempts[uid].questions[questionId] = {
              taqrId,
              score,
              testAttemptId,
              weight: JSON.parse(config)?.content[0]?.scoreWeight,
          };
          if (score) {
            studentAttempts[uid].total += score;
          }
      }

      tf.studentAttempts = studentAttempts
      tf.questionScoreSlugMap = questionScoreSlugMap

      
      if(tf.assessment_description) {
        try {
          tf.assessment_description = JSON.parse(tf.assessment_description)
        } catch (err) {
          console.log(err);
        }
      } else {
        tf.assessment_description = {};
      }
    }
    

    return {
      testDesigns, 
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: Id, data: Data, params?: Params): Promise<Data> {
    const uid = +id;
    if (!params || !params.query) {
      throw new Errors.BadRequest("REQ_PARAMS_MISSING")
    }
    const { action } = params.query
    if (action == SCORE_ENTRY_ACTION_TYPES.SCORE_CHANGE) {
      const score = data.score;
      const { testSessionId, questionId, testAttemptId } = params.query
      try {
        const taqr = (await dbRawRead(this.app, [uid, testSessionId, questionId, testAttemptId], `
          select taqr.* from mpt_dev.test_attempts ta 
          join mpt_dev.test_attempt_question_responses taqr 
          where ta.uid = ? 
          and ta.test_session_id = ? 
          and taqr.test_question_id = ?
          and taqr.test_attempt_id = ?
        `))[0];
        if (!taqr) {
          const weight = data.weight;
          const createResult = await dbRawWrite(this.app, [testAttemptId, questionId, score, weight], `
            INSERT INTO mpt_dev.test_attempt_question_responses
            (test_attempt_id, test_question_id, score, weight)
            VALUES (?, ?, ?, ?)
          `);
          return createResult;
        }
        else {
          const patchResult = await dbRawWrite(this.app, [score, taqr.id], `
            UPDATE mpt_dev.test_attempt_question_responses
            SET score = ?
            WHERE id = ?
          `)
          return patchResult;
        }
      }
      catch (e) {
        throw new Errors.BadRequest("PATCH_ERR", e)
      }
    } else if(action == SCORE_ENTRY_ACTION_TYPES.SCORE_CLEAR) {
      const score = data.score;
      const { testSessionId, questionIds, testAttemptId } = params.query
      try {
        const taqrs = await dbRawRead(this.app, {uid, testSessionId, questionIds, testAttemptId}, `
          select taqr.* from mpt_dev.test_attempts ta 
          join mpt_dev.test_attempt_question_responses taqr 
          where ta.uid = :uid
          and ta.test_session_id = :testSessionId
          and taqr.test_question_id in (:questionIds)
          and taqr.test_attempt_id = :testAttemptId
          and taqr.score IS NOT NULL
        `);
        const taqrIds: number[] = taqrs.map((taqr) => taqr.id);
        if(!taqrIds || !taqrIds.length) {
          return {};
        }
        const patchResult = await dbRawWrite(this.app, {score, taqrIds}, `
          UPDATE mpt_dev.test_attempt_question_responses
          SET score = :score
          WHERE id in (:taqrIds)
        `)
        return patchResult;
      }
      catch (e) {
        throw new Errors.BadRequest("PATCH_ERR", e)
      }
    }
    else if (action == SCORE_ENTRY_ACTION_TYPES.LOCK_CHANGE) {
      const is_closed = data.is_closed;
      const { testAttemptId } = params.query
      return await this.patchTestAttemptLock(is_closed, testAttemptId)

    }
    else if (action == SCORE_ENTRY_ACTION_TYPES.SUBMIT) {
      if (id === null || id === undefined) {
        throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
      }

      await dbRawRead(this.app, [id],
        `UPDATE mpt_dev.test_attempt_sub_sessions 
               SET is_submitted = 1
               where test_session_id= ?
                  and is_invalid != 1;
        `);

      await dbRawRead(this.app, [dbDateNow(this.app), id, id],
        `UPDATE mpt_dev.test_attempts
           SET is_submitted = 1, is_closed = 1, closed_on = ?, submitted_test_session_id = ?
           where test_session_id= ?
            and is_invalid != 1
      ;`)

      await this.app
        .service('db/write/test-sessions')
        .patch(id, {
          is_closed: 1,
          closed_on: dbDateNow(this.app),
        });

      return <any>{ id }
    }
    else if (action == SCORE_ENTRY_ACTION_TYPES.UNSUBMIT) {
      if (id === null || id === undefined) {
        throw new Errors.BadRequest('MISSING_TEST_SESSION_ID');
      }

      await dbRawRead(this.app, [id],
        `UPDATE mpt_dev.test_attempt_sub_sessions 
               SET is_submitted = 0
               where test_session_id= ?
                  and is_invalid != 1;
        `);

      await dbRawRead(this.app, [id],
        `UPDATE mpt_dev.test_attempts
           SET is_submitted = 0
           where test_session_id= ?
            and is_invalid != 1
      ;`)

      await this.app
        .service('db/write/test-sessions')
        .patch(id, {
          is_closed: 0
        });

      return <any>{ id }
    }
    else {
      throw new Errors.BadRequest("MISSING_PATCH_ACTION")
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async patchTestAttemptLock (isClosed: number | null, testAttemptId: number):Promise<any[]> {
    if (!testAttemptId) {
      throw new Errors.BadRequest("MISSING_TA_ID")
    }
    return await dbRawWrite(this.app, [ isClosed, testAttemptId ], `
      UPDATE mpt_dev.test_attempts
      SET is_closed = ?
      WHERE id = ?
    `)
  }
}
