import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../../util/db-raw';
import { IRepProfileDB, SQL_GET_LAYOUT_PROFILE } from '../layout-profiles/model/types';
import { dbDateNow } from '../../../../../util/db-dates';
import { SQL_GET_ITEM_SET_STRUCTURES } from './model/sql';
import { IItemSetStructuresDB } from './model/types';
import { IAsmtStructuresDB, ISEItemSets } from '../assessment-structures/model/types';

interface Data {}

interface ServiceOptions {}

export class ItemSetStructures implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {item_set_structure_ids} = params.query;

    return await dbRawRead(this.app, {item_set_structure_ids}, SQL_GET_ITEM_SET_STRUCTURES)
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Creates a new item set structure and revokes all other structures with the same slug.
   * @param data 
   * @param params 
   * @returns 
   */
  async create(data: Partial<IItemSetStructuresDB>, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    delete data.id;
    delete data.created_on
    delete data.is_revoked;
    delete data.revoked_by_uid;
    delete data.revoked_on;
    delete data.latest_id;

    const uid = await currentUid(this.app, params)
    data.created_by_uid = uid;
    if(data.config) {
     (data.config as any) = JSON.stringify(data.config);
    }

    const newItemStructure = await this.app.service('db/write/se-item-set-structures').create(data);

    // Revoke all previous versions
    await dbRawWrite(this.app, {newId: newItemStructure.id, slug: data.slug, uid}, `
      UPDATE se_item_set_structures
      SET 
        is_revoked = 1,
        revoked_on = NOW(),
        revoked_by_uid = :uid
      WHERE slug = :slug
      AND id != :newId;
    `);
    
    return newItemStructure;
  }

    /**
   * Updates a given assessment structure to use the latest item set structure ID
   * @param id the ID of the assessment structure to update
   * @param data object containing the current and latest ID
   * @param params not used
   */
  async update(id: number, data: {current_id: number, latest_id: number}, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    const asmtStructure: IAsmtStructuresDB = await this.app.service('db/read/se-assessment-structures').get(id);
    const {current_id, latest_id} = data;
    (asmtStructure.item_sets as ISEItemSets).item_set_structure_ids = this.replaceIds(
      (asmtStructure.item_sets as ISEItemSets).item_set_structure_ids,
      current_id,
      latest_id
    )

    const patchData = {
      item_sets: JSON.stringify(asmtStructure.item_sets),
      last_updated_on: dbDateNow(this.app),
      updated_by_uid: uid
    }

    return await this.app.service('db/write/se-assessment-structures').patch(id, patchData)
  }

  replaceIds(arr: number[], currentNum: number, latestNum: number): number[] {
    return arr.map(num => num === currentNum ? latestNum : num);
  }

  async patch(id: NullableId, data: any, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


}
