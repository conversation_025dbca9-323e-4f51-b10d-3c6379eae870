export interface ISESection {
    set: string;
    item_set_structure_slug: string;
}

export interface ISEItemSets {
    sets: ISESection[],
    item_set_structure_ids: number[]
}

export interface IAsmtStructuresDB {
    id: number,
    slug: string,
    caption: string,
    item_sets: ISEItemSets | string,
    assessment_description: {en: string, fr: string} | string,
    reporting_profile_id: number,
    is_revoked: number,
    revoked_by_uid: number,
    revoked_on: string,
    created_by_uid: number,
    created_on: string,
    last_updated_on: string,
    updated_by_uid: number,
    latest_id?: number
}