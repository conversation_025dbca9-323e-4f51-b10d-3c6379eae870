export const SQL_GET_ASMT_STRUCTURE_VIA_ITEM_SET = `
    SELECT sas.* FROM temp_question_set tqs 
    join se_assessment_structures sas 
        on sas.slug = tqs.assessment_structure_slug 
        and sas.is_revoked = 0
    WHERE tqs.id = :item_set_id;
`

export const SQL_GET_ASMT_STRUCTURE_VIA_REP_PROF = `
    SELECT sas.*, latest_structure.id AS latest_id
    FROM rp_reporting_profiles rrp
    JOIN se_assessment_structures sas
        ON sas.id = rrp.assessment_structure_id
    LEFT JOIN se_assessment_structures latest_structure
        on latest_structure.slug = sas.slug
        AND latest_structure.is_revoked = 0
    WHERE rrp.id = :reporting_profile_id
    ORDER BY id DESC;
`

export const SQL_GET_TWTT_TYPES_VIA_GRP_ID = `
    SELECT type_slug
    FROM test_window_td_types twtt
    WHERE authoring_group_id = :authoring_group_id 
    ORDER BY id DESC;
`

export const SQL_GET_ITEM_SETS = `
    SELECT * FROM se_item_set_structures
    WHERE id in (:item_set_ids);
`