import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { IRepProfileDB, SQL_GET_LAYOUT_PROFILE } from '../layout-profiles/model/types';
import { dbDateNow } from '../../../../../util/db-dates';
import { SQL_GET_ASMT_STRUCTURE_VIA_REP_PROF, SQL_GET_ASMT_STRUCTURE_VIA_ITEM_SET, SQL_GET_ITEM_SETS, SQL_GET_TWTT_TYPES_VIA_GRP_ID } from './model/sql';
import { IAsmtStructuresDB, ISEItemSets } from './model/types';
import { IItemSetStructuresDB, ISeItem, ISEItemSet, TItemSetSlugMapping } from '../item-set-structures/model/types';

interface Data {}

interface ServiceOptions {}

export class AssessmentStructures implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<any> {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {reporting_profile_id, authoring_group_id} = params.query;

    const asmtStructure = await dbRawReadSingle(this.app, {reporting_profile_id}, SQL_GET_ASMT_STRUCTURE_VIA_REP_PROF);
    const twttTypes = await dbRawRead(this.app, {authoring_group_id}, SQL_GET_TWTT_TYPES_VIA_GRP_ID)

    return {asmtStructure, twttTypes};
  }

  async get(id: Id, params?: Params): Promise<Data> {
    const asmtStructure = await dbRawReadSingle(this.app, {item_set_id: id}, SQL_GET_ASMT_STRUCTURE_VIA_ITEM_SET);
    if(!asmtStructure) {
      throw new Errors.NotFound('NO_ASMT_STRUC_FOUND');
    }

    const asmtStrucInfo: ISEItemSets = asmtStructure.item_sets;
    const itemSets: IItemSetStructuresDB[] = await dbRawRead(this.app, {item_set_ids: asmtStrucInfo.item_set_structure_ids}, SQL_GET_ITEM_SETS)
    const itemSetSlugMapping: TItemSetSlugMapping = {};
    itemSets.forEach((set) => {
      const itemSlugMapping: {[key: string]: ISEItemSet} = {}
      set.config.sets.forEach((item) => {
        itemSlugMapping[item.slug] = item;

      })
      itemSetSlugMapping[set.slug] = {
        sets: itemSlugMapping,
        item_slug_defaults: set.config.item_slug_defaults,
        item_params: set.config.item_params
      };
    })
    return {asmtStructure, itemSetSlugMapping};
  }

  async create(data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Updates the reporting profile to the latest assessment structure
   * @param id the ID of the reporting profile to update
   * @param data not used
   * @param params not used
   */
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    const asmtStructure: IAsmtStructuresDB = await dbRawReadSingle(this.app, {reporting_profile_id: id}, SQL_GET_ASMT_STRUCTURE_VIA_REP_PROF);

    if(asmtStructure.latest_id) {
      return this.app.service('db/write/rp-reporting-profiles').patch(id, {
        assessment_structure_id: asmtStructure.latest_id
      })
    }

    return {};

  }

  /**
   * 
   * @param id the ID of the assessment structure to update
   * @param data the new layout config data in JSON form
   * @returns 
   */
  async patch(id: NullableId, data: Partial<IAsmtStructuresDB>, params?: Params): Promise<Data> {
    if(!id) {
      throw new Errors.BadRequest('MISSING_ID');
    }
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    const date = dbDateNow(this.app)

    data.item_sets = JSON.stringify(data.item_sets);
    if(data.assessment_description) {
      data.assessment_description = JSON.stringify(data.assessment_description);
    }
    delete data.id;
    delete data.created_on;
    delete data.is_revoked;
    delete data.latest_id
    // Create new row
    const newStructure = await this.app.service('db/write/se-assessment-structures').create({
      ...data,
      created_by_uid: uid,
      created_on: date
    })

    // Revoke the old structure
    const oldStructure = await this.app.service('db/write/se-assessment-structures').patch(id, {
      is_revoked: 1, 
      revoked_by_uid: uid, 
      revoked_on: date
    });

    return newStructure;
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


}
