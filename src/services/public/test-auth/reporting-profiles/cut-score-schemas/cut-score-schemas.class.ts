import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application, ServiceTypes } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { dbRawRead, dbRawReadSingle, dbRawWrite } from '../../../../../util/db-raw';
import { currentUid } from '../../../../../util/uid';
import { ReportingSubprofileColumns, updateReportingProfileIds, IReportingSubprofile, patchReportingSubprofiles, ReportingSubprofileTables, getReportingSubprofileList, getReportingSubprofile, ICutScoreSchemaConfig, createSubprofile, revokeSubprofile} from '../reporting-profiles/model/common';

interface ServiceOptions {}

const subprofileTable = ReportingSubprofileTables.rp_cut_score_schema;
const subprofileCol = ReportingSubprofileColumns.rp_cut_score_schema;
const subprofileService: keyof ServiceTypes = 'db/write/rp-cut-score-schema';

export class CutScoreSchemas implements ServiceMethods<any> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<IReportingSubprofile[] | Paginated<IReportingSubprofile>> {
    return await getReportingSubprofileList(this.app, subprofileTable, subprofileCol, params);
  }
  async get(id: Id, params?: Params): Promise<IReportingSubprofile[]> {
    return await getReportingSubprofile(this.app, id, subprofileTable, subprofileCol);
  }

  async create(data: {slug: string, authoring_group_id: number}, params?: Params): Promise<IReportingSubprofile> {
    const defaultConfig: ICutScoreSchemaConfig = {
      lang: [],
      scopeTypes: []
    };
    return await createSubprofile(this.app, data, subprofileService, subprofileTable, defaultConfig, params);
  }

  async update(id: Id, data: any, params?: Params): Promise<any> {
    const reportingProfile: IReportingSubprofile = await getReportingSubprofile(this.app, id, subprofileTable, subprofileCol);

    return await updateReportingProfileIds(this.app, id, reportingProfile.latest_id, subprofileCol);
  }

  async patch(id: Id, data: Partial<IReportingSubprofile>, params?: Params): Promise<IReportingSubprofile> {
    return await patchReportingSubprofiles(
      this.app,
      id,
      data,
      subprofileService,
      subprofileTable,
      subprofileCol,
      params
    )
  }

  async remove(id: Id, params?: Params): Promise<IReportingSubprofile> {
    return await revokeSubprofile(this.app, id, subprofileService, params);
  }

}
