import { Id, Params } from "@feathersjs/feathers";
import { Application, ServiceTypes } from "../../../../../../declarations";
import { Errors } from "../../../../../../errors/general";
import { currentUid } from "../../../../../../util/uid";
import { dbRawRead, dbRawReadSingle, dbRawWrite } from "../../../../../../util/db-raw";
import { WriteAccommApplicantTransferReq } from "../../../../../db/table-services";
import { dbDateNow } from "../../../../../../util/db-dates";

/**
 * A common interface used across the reporting subprofile tables:
 * rp_category_schema, rp_cut_score_profile, rp_cut_score_schema, rp_domain_score_scaling_factor_profile
 */
export interface IReportingSubprofile {
    id?: number, 
    config: any, 
    slug: string, 
    created_on?: string,
    authoring_group_id: number,
    is_selected?: number,
    latest_id?: number,
    created_by_uid?: number
    is_revoked?: number,
    revoked_on?: string,
    revoked_by_uid?: number
}

export interface ICutScoreProfile extends IReportingSubprofile{
  config: ICutScoreProfileConfig
}
export interface ICategorySchema extends IReportingSubprofile{
  config: ICategorySchemaConfig[]
}

export interface ICutScoreSchema extends IReportingSubprofile{
  config: ICutScoreSchemaConfig
}

export interface IScalingProfile extends IReportingSubprofile {
  config: IScalingFactorConfig
}

export interface ICutScoreProfileConfig {
  en: TLanguageProfile,
  fi: TLanguageProfile,
  fr: TLanguageProfile
}

export type TLanguageProfile = {
  entry_domain: {[domain: string]: IDomainCutScore[]}
}

export interface IDomainCutScore {
  slug: string,
  cut_score: number,
  comparison_type: COMPARISON_TYPES
}

export interface ICutScoreSchemaConfig {
  lang: string[],
  scopeTypes: string[]
}

export interface ICategorySchemaConfig {
  caption: {en: string, fr: string},
  color: string,
  order: number,
  slug: string
}

export interface IScalingFactorConfig {
  grade: string,
  lang: {
    [key: string]: IScalingFactor
  }
}

export interface IScalingFactor {
  total_weight: number,
  total_max_score: number,
  scalings: IScalingFactorScalings[]
}
export interface IScalingFactorScalings {
  entry_domain: string,
  weight: number,
  max_score: number
}

export enum COMPARISON_TYPES {
  LTE = 'lte',
  GTE = 'gte',
  EQ = 'eq',
  LT = 'lt',
  GT = 'gt'
}

/**
 * The DB table names of the subprofile tables associated to rp_reporting_profiles
 */
export enum ReportingSubprofileTables {
    rp_reporting_profiles = 'rp_reporting_profiles',
    rp_category_schema = 'rp_category_schema',
    rp_cut_score_profile = 'rp_cut_score_profile',
    rp_cut_score_schema = 'rp_cut_score_schema',
    rp_domain_score_scaling_factor_profile = 'rp_domain_score_scaling_factor_profile'
}

/**
 * The column names in rp_reporting_profiles associated with the subprofile tables
 */
export enum ReportingSubprofileColumns {
    rp_cut_score_profile = 'cut_score_profile_id',
    rp_domain_score_scaling_factor_profile = "domain_score_scaling_factor_profile_id",
    rp_cut_score_schema = "cut_score_schema_id",
    rp_category_schema = "category_schema_id"
}

export const getReportingSubprofile = async (app: Application, id: Id, subprofileTable: ReportingSubprofileTables, subprofileCol: ReportingSubprofileColumns) => {
    return await dbRawReadSingle(app, {id}, `
      SELECT subprofile.*, latest_profile.id latest_id from rp_reporting_profiles rrp 
      JOIN ${subprofileTable} subprofile 
        on subprofile.id = rrp.${subprofileCol} 
      LEFT JOIN ${subprofileTable} latest_profile
        on latest_profile.slug = subprofile.slug
        AND latest_profile.is_revoked = 0
      WHERE rrp.id = :id;
    `);
  }

  /**
   * Generalized "find" function for reporting profile related endpoints. Gets the list of all profiles linked to a given authoring 
   * group, and signifies if any are selected by the reporting profile.
   * @param app application object
   * @param subprofileTable the 
   * @param subprofileCol 
   * @param params 
   * @returns 
   */
export const getReportingSubprofileList = async (app: Application, subprofileTable: ReportingSubprofileTables, subprofileCol: ReportingSubprofileColumns, params?: Params) => {
    if(!params || !params.query || !params.query.authoring_group_id || !params.query.reporting_profile_id) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const authoring_group_id = params.query.authoring_group_id;
    const reporting_profile_id = params.query.reporting_profile_id;

    const generalList: IReportingSubprofile[] = await dbRawRead(app, {authoring_group_id}, `
      SELECT    id
              , config
              , slug
              , created_on
              , authoring_group_id 
              , 0 as is_selected
      FROM ${subprofileTable} subprofile 
      WHERE authoring_group_id in (:authoring_group_id)
      AND is_revoked = 0;
    `);

    const reportingProfile: {id: number, slug?: string} = await dbRawReadSingle(app, {reporting_profile_id}, `
      SELECT rrp.id, subprofile.slug
      FROM rp_reporting_profiles rrp
      LEFT JOIN ${subprofileTable} subprofile 
        ON subprofile.id = rrp.${subprofileCol}
      WHERE rrp.id = :reporting_profile_id;
    `);

    if(reportingProfile.slug) {
      generalList.forEach((profile) => {
        if(profile.slug == reportingProfile.slug) {
          profile.is_selected = 1;
        } 
      })
    }

    return generalList;
  }

/**
 * Generalized "update" function for reporting profile related endpoints. Upgrades the reporting profile to use the newest version of a given subprofile
 * @param app Application object
 * @param reportingProfileId the reporting profile ID to update
 * @param latest_id the ID to apply to the reporting profile
 * @param column the associated column name in rp_reporting_profiles
 * @returns the patched reporting profile
 */
export const updateReportingProfileIds = async (app: Application, reportingProfileId: Id, latest_id: number | undefined, column: ReportingSubprofileColumns) => {
    if(!latest_id) {
        throw new Errors.BadRequest('MISSING_LATEST_ID');
    }

    const patchObj: any = {}
    patchObj[column] = latest_id;

    return await app.service('db/write/rp-reporting-profiles').patch(reportingProfileId, patchObj);
}

/**
 * Generalized "patch" function for reporting profile related endpoints. "Patches" a reporting subprofile table by creating a new record, revoking the older records of the same slug, and updating the reporting profiles column ID
 * @param app Application object
 * @param id the reporting profile ID to update
 * @param data the new subprofile data
 * @param writeService the service for the reporting subprofile table
 * @param tableName the name of the reporting subprofile table in the DB
 * @param columnName the associated column name in rp_reporting_profiles
 * @param params 
 * @returns the newly created profile
 */
export const patchReportingSubprofiles = async (app: Application, id: Id, data: Partial<IReportingSubprofile>, writeService: keyof ServiceTypes, tableName: string, columnName: ReportingSubprofileColumns, params?: Params) => {
    if(!params || !params.query) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }

    const uid = await currentUid(app, params);
    const reportingProfileId = id;

    // Delete properties that shouldn't carry over
    delete data.id;
    delete data.latest_id;
    delete data.is_selected;
    delete data.created_on;
    delete data.is_revoked;
    delete data.revoked_on;
    delete data.revoked_by_uid;

    data.created_by_uid = uid
    if(data.config) {
      data.config = JSON.stringify(data.config);
    }

    // Create the profile
    const newProfile: IReportingSubprofile = await (app.service(writeService) as WriteAccommApplicantTransferReq).create(data);
    const newId = newProfile.id;

    // Revoke previous profiles of the same slug
    await dbRawWrite(app, {newId, slug: newProfile.slug, uid}, `
        UPDATE ${tableName} 
        SET   is_revoked = 1
            , revoked_on = NOW()
            , revoked_by_uid = :uid
        WHERE slug = :slug
        AND is_revoked = 0
        AND id != :newId;
    `);

    // Update reporting profile
    await updateReportingProfileIds(app, reportingProfileId, newId, columnName)

    return newProfile;
}

export const createSubprofile = async (app: Application, data: {slug: string, authoring_group_id: number}, writeService: keyof ServiceTypes, tableName: string, defaultConfig: any, params?: Params) => {
  if(!params) {
    throw new Errors.BadRequest('MISSING_PARAMS');
  }
  if(!data.slug || !data.authoring_group_id) {
    throw new Errors.BadRequest('MISSING_DATA');
  }
  const {slug, authoring_group_id} = data;

  const existingSlug = await dbRawReadSingle(app, {slug}, `
    SELECT slug FROM ${tableName}
    WHERE slug = :slug
    AND is_revoked = 0;
  `);

  if(existingSlug) {
    throw new Errors.BadRequest('EXISTING_SLUG')
  }
  
  const uid = await currentUid(app, params);
  const parsedConfig = JSON.stringify(defaultConfig);

  const newSubprofile: IReportingSubprofile = {
    config: parsedConfig,
    slug,
    authoring_group_id,
    created_by_uid: uid
  }

  return await (app.service(writeService) as WriteAccommApplicantTransferReq).create(newSubprofile);
}

export const revokeSubprofile = async (app: Application, id: Id, writeService: keyof ServiceTypes, params?: Params) => {
  if(!params) {
    throw new Errors.BadRequest('MISSING_PARAMS');
  }
  const uid = await currentUid(app, params);
  return await (app.service(writeService) as WriteAccommApplicantTransferReq).patch(id, {is_revoked: 1, revoked_by_uid: uid, revoked_on: dbDateNow(app)})
}