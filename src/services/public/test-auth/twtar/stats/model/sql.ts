import { ICanonicalTagCacheFilter } from "../../../../data-exporter/data-export/model/filters";

export const SQL_GET_TWTAR_INFO = ` /*SQL_GET_TWTAR_INFO*/
    SELECT twtar.type_slug
         , twtar.form_code
    FROM test_window_td_alloc_rules twtar
    WHERE twtar.id = :twtar_id 
`

export const SQL_GET_EXPORT_TAG_JOB_URLS = (filter: ICanonicalTagCacheFilter) => ` /*SQL_GET_EXPORT_TAG_JOB_URLS*/
    select dejt.id
        , dejt.export_id
        , dej.created_on
        , dej.status
        , dejt.created_on tagged_on
        , dej.urls 
    from data_export_job_tags dejt 
    join data_export_jobs dej 
        on dej.id = dejt.export_id
    where dejt.twtar_id = :twtar_id
        and dejt.slug = :export_tag_slug -- 'item-analysis'
        and dejt.is_revoked = 0
        ${filter.test_window_id ?     'and dejt.test_window_id = :test_window_id'         : 'and dejt.test_window_id is null' }
        ${filter.twtar_id ?           'and dejt.twtar_id = :twtar_id'                     : 'and dejt.twtar_id is null' }
        ${filter.schl_group_id ?      'and dejt.schl_group_id = :schl_group_id'           : 'and dejt.schl_group_id is null' }
        ${filter.schl_dist_group_id ? 'and dejt.schl_dist_group_id = :schl_dist_group_id' : 'and dejt.schl_dist_group_id is null' }

    order by dejt.id desc 
`


export const SQL_GET_RELATED_TWTAR_IDS = ` /*SQL_GET_RELATED_TWTAR_IDS*/
    SELECT twtar.test_window_id
         , twtar.type_slug
         , twtar_assoc.id as twtar_id
    FROM test_window_td_alloc_rules twtar
    LEFT JOIN test_window_td_alloc_rules twtar_assoc
        ON twtar_assoc.test_window_id = twtar.test_window_id
        AND twtar_assoc.type_slug = twtar.type_slug
        AND twtar_assoc.is_revoked = 0
    WHERE twtar.id = :twtar_id 
`

export const SQL_GET_LAST_EXPORT_DURATION = ` /*SQL_GET_LAST_EXPORT_DURATION*/
    SELECT dej.id
         , dej.status
         , dej.dag_job_name
         , dej.test_window_id
         , dej.description
         , dej.started_on
         , dej.completed_on
         , dej.created_by_uid
         , dej.created_on
         , dej.updated_on
         , dej.last_stage_completed
         , dej.failure_reason
         , dej.is_bg
    FROM data_export_jobs dej
    JOIN data_export_job_tags dejt ON dej.id = dejt.export_id
    WHERE dejt.slug = :export_tag_slug
      AND dejt.twtar_id = :twtar_id
      AND dej.status = 'COMPLETE'
      AND dejt.is_revoked = 0
    ORDER BY dej.id DESC
    LIMIT 1
`