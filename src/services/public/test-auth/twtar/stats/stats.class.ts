import { Id, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawReadReporting, dbRawReadSingle, dbRawReadSingleReporting } from '../../../../../util/db-raw';
import { SQL_GET_EXPORT_TAG_JOB_URLS, SQL_GET_LAST_EXPORT_DURATION, SQL_GET_RELATED_TWTAR_IDS, SQL_GET_TWTAR_INFO } from './model/sql';
import { EXPORT_TAG_ITEM_ANALYSIS, ITEM_ANALYSIS_ASSET_SLUG_REF } from './model/const';
import { generateS3DownloadUrl } from '../../../../upload/upload.listener';
import axios from 'axios';
import { Errors } from '../../../../../errors/general';

interface Data {
  twtar_id?: number;
  test_window_id?: number;
  type_slug?: string;
  export_id?: number;
  is_empty?: boolean;
  created_on?: any;
  tagged_on?: any;
  urlsSigned?: any[];
  data?: any;
  estimated_completion_time?: number;
  id?: Id;
  jobName?: string;
  testWindowId?: number;
  triggeredFrom?: string;
  form_code?: string,
  status?: string,
  config?: {
    test_window_ids: number[];
    twtar_type_slugs: string[];
  };
}

const ESTIMATED_COMPLETION_TIME_MULTIPLIER = 1.2;

interface ServiceOptions {}

export class Stats implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const twtar_id = +id;
    const export_tag_slug = EXPORT_TAG_ITEM_ANALYSIS;
    const is_full = !!params?.query?.is_full;

    const twtarInfo = await dbRawReadSingle(this.app, {twtar_id}, SQL_GET_TWTAR_INFO);
    const {form_code} = twtarInfo; // todo: more db roundtrips than needed...

    const exportJobRecords = await dbRawReadReporting(this.app, {twtar_id, export_tag_slug}, SQL_GET_EXPORT_TAG_JOB_URLS({twtar_id}));
    const exportJobRecord = exportJobRecords[0];
    if (!exportJobRecord){
      return {
        is_empty: true
      }
    }
    else {
      const {export_id, created_on, tagged_on, urls, status} = exportJobRecord;
      const urlMap = JSON.parse(urls || '{}');
      const urlsSigned = this.getDataExportSignedUrls(urlMap);
      // populate in stages
      const data:any = {};
      data.forms = await this.getDataExportAsset(urlMap, ITEM_ANALYSIS_ASSET_SLUG_REF.FORMS)
      if (is_full){
        data.items = await this.getDataExportAsset(urlMap, ITEM_ANALYSIS_ASSET_SLUG_REF.ITEMS)
        data.responses = await this.getDataExportAsset(urlMap, ITEM_ANALYSIS_ASSET_SLUG_REF.RESPONSES)
        data.total_scores = await this.getDataExportAsset(urlMap, ITEM_ANALYSIS_ASSET_SLUG_REF.FREQ_TEMP)
      }
      return {
        export_id, created_on, tagged_on, status,
        form_code,
        urlsSigned,
        data,
      }
    }
  }

  getDataExportSignedUrls(urlMap:any){
    const assets:any[] = [
      {caption:'Form Statistics', slug:ITEM_ANALYSIS_ASSET_SLUG_REF.FORMS},
      {caption:'Total Score Distribution', slug:ITEM_ANALYSIS_ASSET_SLUG_REF.FREQ_TEMP},
      {caption:'Item Statistics', slug:ITEM_ANALYSIS_ASSET_SLUG_REF.ITEMS},
      {caption:'Response Statistics', slug:ITEM_ANALYSIS_ASSET_SLUG_REF.RESPONSES},
    ]
    for (let asset of assets){
      const url = urlMap[asset.slug]
      if (url){
        asset.signedUrl = generateS3DownloadUrl(url, 60*60*24) // 1 day expiry
      }
    }
    return assets
  }
  
  async getDataExportAsset(urlMap:any, assetSlug:string){
    try {
      const url = urlMap[assetSlug]
      if (!url) {
        console.log(`No URL found for asset slug: ${assetSlug}`)
        return []
      }
      const fileUrl = generateS3DownloadUrl(url)
      const payload:any = await axios.get(fileUrl, {})
      return payload.data || []
    }
    catch(e) {
      console.log(`Error fetching data export asset for ${assetSlug}:`, e)
      return []
    }
  }

  async create (data: Data | Data[], params?: Params): Promise<Data | Data[]> {
    if (Array.isArray(data)) {
      const results = await Promise.all(data.map(current => this.create(current, params)));
      return results as Data[];
    }

    const { twtar_id } = data;

    if (!twtar_id) {
      throw new Errors.BadRequest('Missing required parameters: twtar_id, test_window_id, type_slug');
    }

    const {twtar_ids, test_window_id, type_slug} = await this.findRelatedTwtarIds(twtar_id);

    // find the most recent export job for the given tag
    let estimated_completion_time : number | undefined = undefined;
    const lastExportJob = await this.findLastExportDuration(twtar_id);
    if (lastExportJob){
      const {duration_m} = lastExportJob;
      if (duration_m){
        estimated_completion_time = +((duration_m * ESTIMATED_COMPLETION_TIME_MULTIPLIER).toFixed(1));
      }
    }

    // Find all related TWTAR IDs that share the same test window and type slug

    // Create a data export job with the item-analysis job type
    const exportJob = await this.app.service('public/data-exporter/data-export').create({
      jobName: EXPORT_TAG_ITEM_ANALYSIS,
      testWindowId: test_window_id,
      triggeredFrom: '*',
      config: {
        test_window_ids: [test_window_id],
        twtar_type_slugs: [type_slug]
      }
    }, params || {}) as Data;

    // Tag the export job with all related TWTAR IDs using a for loop to avoid connection contention
    for (const relatedTwtarId of twtar_ids) {
      await this.app.service('db/write/data-export-job-tags').create({
        export_id: exportJob.export_id,
        twtar_id: relatedTwtarId,
        slug: EXPORT_TAG_ITEM_ANALYSIS,
        is_revoked: 0
      });
    }

    return {
      twtar_id,
      test_window_id,
      type_slug,
      estimated_completion_time,
      export_id: exportJob.export_id
    };
  }

  private async findLastExportDuration(twtar_id:number): Promise<{ duration_m: number, job: any } | null> {
    const export_tag_slug = EXPORT_TAG_ITEM_ANALYSIS;
    const result = await dbRawReadReporting(this.app, {export_tag_slug, twtar_id}, SQL_GET_LAST_EXPORT_DURATION);
    if (result && result.length > 0) {
      const job = result[0];
      if (job.started_on && job.completed_on) {
        const startTime = new Date(job.started_on).getTime();
        const endTime = new Date(job.completed_on).getTime();
        const duration_m = Math.round((endTime - startTime) / 1000 / 60); // Duration in minutes
        return { duration_m, job };
      }
    }
    return null;
  }

  private async findRelatedTwtarIds(twtar_id: number) {
    const records = await dbRawReadReporting(this.app, { twtar_id }, SQL_GET_RELATED_TWTAR_IDS);
    const twtar_ids:number[] = records.map(r => r.twtar_id).filter(id => !!id);
    const {test_window_id, type_slug} = records[0]
    return {
      twtar_ids, 
      test_window_id,
      type_slug,
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: Id, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: Id, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: Id, params?: Params): Promise<Data> {
    return { id };
  }
}
