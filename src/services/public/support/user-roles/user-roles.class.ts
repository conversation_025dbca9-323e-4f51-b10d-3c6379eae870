import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { AND_MATCH_ANY, dbEscapeNum, dbEscapeString, dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from './../../../../util/db-dates';

interface Data {
  [key: string]: any
}

interface ServiceOptions {}

export class UserRoles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const queryParams = params.query;
      const allowedParams:{prop:string, isNum?:boolean}[] = [
        {prop: 'uid', isNum:true},
        {prop: 'group_id', isNum:true},
        {prop: 'first_name'},
        {prop: 'last_name'},
        {prop: 'contact_email'},
        {prop: 'auth_email'},
        {prop: 'role_type'},
        {prop: 'account_type'},
        {prop: 'group_type'},
        {prop: 'district_id'},
        {prop: 'district_code'},
        {prop: 'school_id'},
        {prop: 'school_code'},
      ]
      const AND_PARAM_CLAUSES:string[] = [];
      for (let i=0; i<allowedParams.length; i++){
        const param = allowedParams[i];
        AND_PARAM_CLAUSES.push(
          await AND_MATCH_ANY(queryParams, param.prop, !param.isNum)
        )
      }      
      const sqlQuery = `
        select * from (
          select
              ur.id as ur_id
              , u.id uid
              , u.first_name
              , u.last_name
              , u.contact_email
              , a.email auth_email
              , u.created_on
              , ur.group_id
              , g.group_type
              , sd.id district_id
              , sd.foreign_id district_code
              , s.id school_id
              , s.foreign_id school_code
              , ur.role_type
              , ur.created_on role_created_on
              , ur.is_revoked
              , ur.revoked_on
              , ur.revoked_by_uid
              , rb.contact_email revoked_by_email
              , CASE
                  WHEN ur.revoked_by_uid IS NOT NULL AND rb.contact_email IS NOT NULL
                  THEN CONCAT(rb.contact_email, ' (', ur.revoked_by_uid, ')')
                  ELSE ''
                END as revoked_by
          from users u
          left join auths a on a.uid = u.id
          left join u_invites i on i.uid = u.id
          left join user_roles ur on ur.uid = u.id
          left join users rb on rb.id = ur.revoked_by_uid
          left join u_groups g on g.id = ur.group_id
          left join schools s on s.group_id = ur.group_id
          left join school_districts sd on sd.group_id = ur.group_id
        ) u
        where u.uid > 0
          ${AND_PARAM_CLAUSES.join('\n')}
        ORDER BY u.uid
        LIMIT 2000
      ;`;
      const rows = await dbRawRead(this. app, [], sqlQuery);
      return rows;
    }
    throw new Errors.BadRequest();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /** Revoke or unrevoke an existing user role record */
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params){
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const uid = await currentUid(this.app, params);
    const {ur_id, is_revoked} = data;
    const payload = {
      is_revoked,
      revoked_by_uid: is_revoked ? uid : null,
      revoked_on: is_revoked ? dbDateNow(this.app) : null
    }
    return this.app.service('db/write/user-roles').patch(ur_id, payload)
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (id && params){
      const created_by_uid = await currentUid(this.app, params);
      const uid = <any> id;
      const {account_type} = <any> data;
      if(!account_type){
        throw new Errors.BadRequest('MISSING_PARAMS');
      }
      const userRecord = await this.app.service('db/read/users').get(uid);
      logger.info({slug:'ACCOUNT_TYPE_CHANGE', data: JSON.stringify({uid, account_type: userRecord.account_type}) })
      await this.app.service('db/write/users').patch(uid, {account_type})
      return {account_type};
    }
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
