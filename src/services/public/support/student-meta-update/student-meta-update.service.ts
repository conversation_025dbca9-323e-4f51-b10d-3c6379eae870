// Initializes the `public/support/student-meta-update` service on path `/public/support/student-meta-update`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { StudentMetaUpdate } from './student-meta-update.class';
import hooks from './student-meta-update.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/student-meta-update': StudentMetaUpdate & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/student-meta-update', new StudentMetaUpdate(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/student-meta-update');

  service.hooks(hooks);
} 