import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { SQL_REPORTED_ISSUES_COMMENTS, SQL_REPORTED_ISSUES_COMMON, SQL_REPORTED_ISSUES_COMMON_BY_ID, SQL_REPORTED_ISSUES_COMMON_BY_THREAD } from '../../test-ctrl/schools/reported-issues/model/sql';
import { dbDateNow } from '../../../../util/db-dates';

const PERSONAL_RIC_DAYS_BACK_LIMIT = 8*7; // 8 weeks
const PERSONAL_RIC_RECORD_LIMIT = 200;

interface Data {}

interface ServiceOptions {}

export class ReportIssue implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      return this.loadReportedIssuesWithComments(created_by_uid);
    }
    throw new Errors.BadRequest();
  }

  async loadReportedIssuesWithComments(created_by_uid:number){
    const isCreatedByFilter = true;
    const isDaysBackLimit = true;
    const daysBackLimit = PERSONAL_RIC_DAYS_BACK_LIMIT;
    const logRecords = await dbRawRead(this.app, {created_by_uid, daysBackLimit}, SQL_REPORTED_ISSUES_COMMON(PERSONAL_RIC_RECORD_LIMIT, {isCreatedByFilter, isDaysBackLimit} ))
    const ric_ids:number[] = [];
    const reportedIssueRef = new Map<number, any>();
    for (let record of logRecords){
      ric_ids.push(record.ric_id);
      reportedIssueRef.set(record.ric_id, record);
      record.comments = [];
    }
    let commentRecords = []
    if (ric_ids.length > 0){
      commentRecords = await dbRawRead(this.app, {ric_ids}, SQL_REPORTED_ISSUES_COMMENTS(true));
      for (let record of commentRecords){
        const reportedIssue = reportedIssueRef.get(record.reported_issues_common_id);
        if (reportedIssue){
          reportedIssue.comments.push(record);
        }
      }
    }
    return logRecords;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (params && params.query){
      const created_by_uid = await currentUid(this.app, params);
      const config = <any> data;
      const {
        msg, 
        phone_number, 
        contact_email, 
        thread_hash, 
        reportedIssueId, 
        email_subject, 
        email_body, 
        email_timestamp,
        lang, 
        categorySelection
      } = config;

      let feedbackMessage = msg;

      let ricRecords:{ric_id:number, is_resolved:boolean}[] = [];
      if (thread_hash){
        ricRecords = await this.lookupExistingThread(thread_hash);
      }
      if (ricRecords.length == 0 && reportedIssueId){
        ricRecords = await this.lookupExistingRic(+reportedIssueId);
      }

      const isThreadToContinue = (ricRecords.length > 0);
      const isPlainMessageOverride = (email_subject && email_body);

      if (isPlainMessageOverride){  
        feedbackMessage = `**${contact_email}**\n\n${email_timestamp}\n\n-----\n\n${email_body}`;
      }

      if (!isThreadToContinue){
        if (isPlainMessageOverride){
          feedbackMessage = `# Subject: ${email_subject}\n\n${feedbackMessage}`;
        }
        return this.app
          .service('public/educator/session-reported-issue')
          .createReportedIssue(created_by_uid, {
            msg: feedbackMessage, 
            phone_number,
            contact_email,
            lang, 
            thread_hash,
            categorySelection, 
            // useragent,
            // test_session_id: config.test_session_id,  // todo: bring this back as optional later when there is a UI to connect to it
            // student: config.studentUids, // todo: bring this back as optional later when there is a UI to connect to it
            // categoryId: config.categoryId, // todo: bring this back as optional later when there is a UI to connect to it
            // categorySelection: config.categorySelection,  // todo: bring this back as optional later when there is a UI to connect to it
            // school_class_id: config.school_class_id // todo: bring this back as optional later when there is a UI to connect to it
          })
      }
      else {
        const ricRecord = ricRecords[0];
        const riCommonId = ricRecord.ric_id;
        const is_auto_comment = 1;

        await this.app
          .service('public/test-ctrl/schools/reported-issue-comments')
          .writeComment(created_by_uid, riCommonId, feedbackMessage, is_auto_comment)

        await this.app.service('db/write/reported-issues-common').patch(riCommonId, {
          is_resolved: 0,
          updated_on: dbDateNow(this.app),
        });

        return {riCommonId, thread_hash}
      }

    }
    throw new Errors.BadRequest();
  }

  async lookupExistingThread(thread_hash:string){
    const ricRecords = await dbRawRead(this.app, {thread_hash}, SQL_REPORTED_ISSUES_COMMON_BY_THREAD);
    return ricRecords;
  }
  async lookupExistingRic(ric_id:number){
    const ricRecords = await dbRawRead(this.app, {ric_id}, SQL_REPORTED_ISSUES_COMMON_BY_ID);
    return ricRecords;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
