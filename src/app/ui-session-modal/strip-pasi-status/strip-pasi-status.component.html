<div *ngIf="pasiCertificate && allowPASIUpdates" class="student-records-indicator">
  <span>
    <span 
      *ngIf="pasiStatus"
      style="margin-right:0.25em"
      class="tag"
      [class.is-warning]="getPasiStatus() == PASI_STATUS.NO_ACCESS "
      [class.is-success]="getPasiStatus() == PASI_STATUS.AVAILABLE "
      [class.is-light]  ="getPasiStatus() == PASI_STATUS.UNKNOWN "
      [class.is-warning]="getPasiStatus() == PASI_STATUS.NOT_AVAILABLE "
    >
      <tra slug="abed_pasi_status"></tra><tra slug="txt_colon"></tra>&nbsp;
      <ng-container [ngSwitch]="getPasiStatus()">
        <tra *ngSwitchCase="PASI_STATUS.NO_ACCESS"     slug="abed_pasi_not_available"></tra>
        <tra *ngSwitchCase="PASI_STATUS.AVAILABLE"     slug="abed_pasi_available"></tra>
        <tra *ngSwitchCase="PASI_STATUS.UNKNOWN"       slug="abed_pasi_unknown"></tra>
        <tra *ngSwitchCase="PASI_STATUS.NOT_AVAILABLE" slug="abed_pasi_no_access"></tra>
      </ng-container>
    </span>
    <span 
      class="tag"
      [class.is-danger]="hasPasiExpired()"
      [class.is-warning]="pasiAboutToExpire()"
      [class.is-success]="!hasPasiExpired() && !pasiAboutToExpire()"
    >
      <tra slug="abed_pasi_certificate"></tra><tra slug="txt_colon"></tra>&nbsp;
      <tra [slug]="renderPasiStatus()"></tra>
    </span>
  </span>
  <span style="font-size: .8em;" *ngIf="pasiAboutToExpire()">
    <tra slug="abed_pasi_certificate_will_expire"></tra>
  </span>
  <em>
    <b><tra slug="abed_pasi_environment"></tra></b> {{pasiCertificate.data.environment}} — 
    <b><tra slug="abed_pasi_expiry_date"></tra></b> {{renderDate(pasiCertificate.data.expiry_date)}} <br>
  </em>
  <button 
    class="button is-small is-dark" 
    (click)="showFullCert()"
  >
    <tra slug="abed_pasi_toggle_cert"></tra>
  </button>
  <ng-container *ngIf="fullCert">
    <input 
      type="text"
      [(ngModel)]="fullCert"
    >
  </ng-container>
</div>