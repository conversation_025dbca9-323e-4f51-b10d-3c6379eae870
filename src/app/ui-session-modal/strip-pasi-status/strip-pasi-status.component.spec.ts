import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StripPasiStatusComponent } from './strip-pasi-status.component';

describe('StripPasiStatusComponent', () => {
  let component: StripPasiStatusComponent;
  let fixture: ComponentFixture<StripPasiStatusComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ StripPasiStatusComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StripPasiStatusComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
