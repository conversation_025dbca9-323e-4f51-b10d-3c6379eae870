.times {
    display: flex;
    margin-top: 18px;
    align-items: flex-start;
    .label {
        color: teal;
        font-size: inherit;
        width: 168px;
        text-align: right;
        padding-right: 23px;
    }
    .values {
        .separator {
        margin: 0 5px;
        position: relative;
        top: 4px;
        }
    }

    .TZAbbr {
        margin-left: 1em;
        margin-top: 0.5em;
    }
}

.field-rows {
    display: flex;
    flex-direction: row;
    gap: 1em;
    align-items: flex-start;
}