@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/pseudo-objects/dashboard-view-summary.scss';
@import '../../../styles/pseudo-objects/panel-scoring';

.page-body {
    @extend %page-body;
    background-color:$off-white;
}


.page-content{
    display:flex;
    .page-container{
        flex-grow:1;
        .content-container{
            height:100%;
            div.flex-container{
                height: 80vh;
                // height: calc(100% - 51px);
                display:flex;
                .panel-content, .panel-score{
                    flex-grow:1;
                    height:100%;
                    overflow: auto;
                    min-width:36em;
                }
                .question-container{
                    max-width: 65%;
                }

                .table-container{
                    height:100%;
                }
            }
        }
    }
}
.panel-score-middle {
    overflow-y: scroll;
}

.controls{
    display:flex;
    flex-direction: row;;
    .message-button{
        // width:40%;
        cursor:pointer;
        font-weight:bold;
        display:flex;
        justify-content: center;
        border:0;
        border-radius:4px;
        padding: 0.5em 1em;
        margin:5px 0;
        background:white;
        i{
            font-size: 1.5em;
            margin-right:1em;
        }
        .small{
            font-size:0.8em;
        }
        .big{
            font-size:1.1em;
        }
       
    }
    .inspect-button {
        width:calc(60% - 0.5em);
        
        cursor:pointer;
        font-weight:bold;
        display:flex;
        justify-content: center;
        border:0;
        border-radius:4px;
        padding: 0.5em 1em;
        margin:5px 0;
        margin-right:0.5em;
        i{
            font-size: 1.5em;
            margin-right:1em;
        }
        .small{
            font-size:0.8em;
        }
        .big{
            font-size:1.1em;
        }
        &.uninspected{
            background:#9874ff;
            color:white;
        }
        &.inspected {
            color:#9874ff;
            background:white;
            border:2px solid #9874ff;
    
        }
    }
}


.s2s-section {
    margin-top:1em;
    background:#fddcdc;
    padding:0.5em;

}

.repool-container {
    display:flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1em;
    margin-bottom: 1em;
}

.super-note {
    width: auto;
    &:disabled {
        cursor:not-allowed;
        opacity: 0.5;
    }
}

.empty-score-panel {
    width: 100%;
    height: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.panel-score-action {
    // padding-bottom: 12em;
    min-width: 45em;
    max-width: 45em;
    overflow:auto;
    &.is-compressed-to-min {
        min-width: 20em;
        max-width: 20em;
    }
    .score-nav-container {
        padding:0.5em;
        display: flex;
        flex-direction: row;;
        justify-content: space-between;
        align-items: center;
        border-bottom:1px solid #ccc;
        min-width: 21em;
        .nav-progress {
            margin-right:1em;
        }
    }
    .score-assign-container {
        margin-top:1em;
        padding-bottom:4em;
        .score-header {
            font-weight:600;
            padding:0.5em;
            border-bottom:1px solid #ccc;
        }
        .score-option-container {
            padding: 0em 0.5em;
            .score-option-scale-container {
                .contents {
                    border: 1px solid #ccc;
                }
            }
            .score-options {
                display: flex; 
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                width: 100%;
            }
            .score-option {
                &.is-offset { 
                    margin-top:1.5em;
                }
            }
        }
        .score-flag-container {
            justify-content: center;
            display: flex;
            flex-direction: column;
            gap: 1em;
            padding: 0em 0.5em;
            margin-top: 2em;
        }
    }
}
.message-insertion {
    background-color: #fddcdc;
    color: #f14668;
    padding: 0.5em;
}

.is-green {
    background-color: #46f154;;
}
.scale-buttons {
    @extend %clean-button;

    .icon-color {
        color: rgb(64, 139, 209);

        &.is-active {
            color: rgb(105, 202, 105) !important;
        }

        &.is-not-scored {
            color: #d3d3dd;
        }
    }
}

.general-score-options{
    min-width: 5em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    button{
    border-radius: 0.4rem;
    min-width: 3rem;
    font-weight: 700;
    }

}

.panel-response {
    display: flex;
}

.assigned-flag-container {
    margin-top: 1em;
    padding: 0em 0.5em;
}

.score-row-container {
    display: flex; 
    flex-direction: row; 
    gap: 0.4em;
}

.history-signifier {
    min-height: 100%; 
    min-width: 0.3em; 
    background-color: #ffe169;
}

.name {
    font-weight: bold;
    margin: 0.5em;
}

.dashboard-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5em;
}

.is-warning {
    margin-top: 1em;
    color: white;
    background-color: #f14668;
}

.is-yellow {
    background-color: #ffd777;
}

.status {
    padding: 0.5em;
    border-radius: 0.3em;
    display: flex;
    justify-content: center;
}

.is-response-focus {
    min-width:80vw;
}

.is-response-expanded {
  width: 70%;
}

.is-compressed-to-nil {
    display:none;
}

.enable-nyc-limit {
    margin-left:2em;
}

.is-full-width{
  width: 100%;
}

.batch-backread-toggle {
    display: flex;
    flex-direction: row;
    gap: 0.5em;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: 0.5em;
    background-color: #fff;
    padding: 0.5em;
    align-items: center;
    &:hover {
        background-color: #f1f1f1;
    }
    &.is-success {
        background-color: #75ca92;
        &:hover {
            background-color: #53ac71;
        }
    
    }
}