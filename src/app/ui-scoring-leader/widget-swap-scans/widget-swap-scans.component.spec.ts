import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WidgetSwapScansComponent } from './widget-swap-scans.component';

describe('WidgetSwapScansComponent', () => {
  let component: WidgetSwapScansComponent;
  let fixture: ComponentFixture<WidgetSwapScansComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ WidgetSwapScansComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(WidgetSwapScansComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
