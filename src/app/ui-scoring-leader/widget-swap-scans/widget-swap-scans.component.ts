import { Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';

@Component({
  selector: 'widget-swap-scans',
  templateUrl: './widget-swap-scans.component.html',
  styleUrls: ['./widget-swap-scans.component.scss']
})
export class WidgetSwapScansComponent implements OnInit {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService
  ) { }

  @Input() test_attempt_id;
  @Input() markingWindowId;
  @Input() markingWindowGroupId;
  @Input() scoreProfileGroups;
  @Output() reassignActioned = new EventEmitter();

  ngOnInit(): void {
    this.loadAllScanData();
  }

  testAttemptScans;
  isViewScans: boolean = false;

  /** Get information about all the current scans in the selected test attempt */
  async loadAllScanData(){
    this.testAttemptScans = await this.auth.apiFind(this.routes.SCOR_LEAD_STUDENT_ATTEMPT_RESPONSE_SCANS, {
      query: {
        ta_id: this.test_attempt_id,
        mw_id: this.markingWindowId,
        mw_group_id: this.markingWindowGroupId
      }
    });

    // Add info on question and session for each scan
    this.testAttemptScans.forEach(scan => {
      // Default to initial item ID
      scan.override_group_id = undefined
    })
    this.testAttemptScans.sort((a, b) => a.section_id - b.section_id);
  }

    /** Save any rearrangement/discarding of scans selected */
    actionScanOverrides(){
      this.loginGuard.confirmationReqActivate({
        caption: `Are you sure you want to action all scan overrides?
        If the responses being overriden have already been scored, manually adjust the score.
        `,
        confirm: () => {
          const overridePromises = []
          this.testAttemptScans.forEach((record) => {
            if (!+record.override_group_id) {
              return
            }
            // Handle only discarding a certain scan
            else if (+record.override_group_id == -1){
              // If another scan is being switched into this item, that process will discard this scan already so do nothing
              if (this.testAttemptScans.some(scan => scan.override_group_id == record.group_id)) {
                return
              }
              const discardOnlyPromise = this.auth
              .apiRemove(this.routes.SCOR_LEAD_STUDENT_ATTEMPT_RESPONSE_SCANS, record.tasr_id, { 
                query: { 
                  taqr_id: record.taqr_id,
                  mw_group_id: this.markingWindowGroupId
                }
              })
              .then(() => {
              })
              overridePromises.push(discardOnlyPromise);
            } else {
              const targetTaqrId = this.testAttemptScans.find(s => s.group_id == record.override_group_id)?.taqr_id
              if (!targetTaqrId){
                throw new Error('Target response does not exist')
              }
              const data = {
                croppedFilePath: record.scan,
                uncroppedFilePath: record.full_scan
              }
              const query = {
                mw_group_id: this.markingWindowGroupId
              }
              const overrideTargetItemScanPromise = this.auth.apiPatch(this.routes.SCOR_LEAD_STUDENT_ATTEMPT_RESPONSE_SCANS, targetTaqrId, data, {query});
              overridePromises.push(overrideTargetItemScanPromise)
            }
          })
          Promise.all(overridePromises)
          .then(res => {
            // Refresh the view to display uplodates
            this.loadAllScanData();
            this.reassignActioned.emit()
          })
          .catch(() => {
            this.loginGuard.quickPopup('Error saving overrides.')
          })
        }
      })
    }
  
    /** The button to action group scan overrides is disabled if there're no changes or they're incomptible */
    isActionScanOverridesDisabled(){
      const noOverrideChanges = !this.testAttemptScans.some(scan => +scan.override_group_id)
      return (noOverrideChanges || this.isActionScanOverridesComboError());
    }
  
    /** 
     * Is the user's combination of scan rearrangement choices invalid.
     * No two scans can have the same item as the final target.
     * E.g. can't have A -> B and A -> C.
     * Also cannot have only A -> B, need to explicitly select that current A will be discarded.
     * */
    isActionScanOverridesComboError(){
      let isComboError:boolean = false;
      const finalGroupIdSet = new Set();
      this.testAttemptScans.forEach(scan => {
        const finalGroupId =  +scan.override_group_id ? +scan.override_group_id : +scan.group_id
        if (![0, -1].includes(finalGroupId) && finalGroupIdSet.has(+finalGroupId)){
          isComboError = true
        }
        finalGroupIdSet.add(finalGroupId)
      })
      return isComboError;
    }

    isAnyDiscardedChosen(){
      return this.testAttemptScans.some(s => s.override_group_id == -1)
    }

  /** Display section and question label as the potential destination of the scan */
  renderScanOverrideOption(scanQuestionInfo){
    const sectionCaption = scanQuestionInfo.section.caption
    const questionLabel = scanQuestionInfo.question.label
    return `${sectionCaption} - ${questionLabel}`
  }

  getOverrideScanOptions(scan){
    return this.scoreProfileGroups
    .filter(g => g.id != scan.group_id)
    .sort((a, b) => a.id - b.id);
  }

}


