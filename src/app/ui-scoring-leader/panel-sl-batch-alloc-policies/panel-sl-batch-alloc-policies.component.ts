import { Component, Input, OnInit } from '@angular/core';
import { PreloadAllModules } from '@angular/router';
import moment from 'moment';
import { generateSanitizedRecords } from 'src/app/scoring-leader-utils/sanitizeTableRows';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { rangeCategorySettings } from './../view-sl-validity/view-sl-validity.component';
import { renderYesNo } from '../../core/util/render';
import { LangService } from 'src/app/core/lang.service';
import { NEW_MAX_BATCH_CLAIM_LOWER_LIMIT, NEW_MAX_BATCH_CLAIM_UPPER_LIMIT } from 'src/app/ui-item-maker/batch-allocation-policies/batch-allocation-policies.component';
import { RoutesService } from 'src/app/api/routes.service';

@Component({
  selector: 'panel-sl-batch-alloc-policies',
  templateUrl: './panel-sl-batch-alloc-policies.component.html',
  styleUrls: ['./panel-sl-batch-alloc-policies.component.scss']
})
export class PanelSlBatchAllocPoliciesComponent implements OnInit {

  @Input() isLocked : boolean;
  @Input() markingWindowId: number;
  @Input() isSysSettings: boolean;

  originRate;
  originMaxBatchClaim;
  originDurationHours;

  policies:any [];
  sanitizedPolicies = [];
  selectedBatchAllocTable:any;
  maxBatchSizeAllowed = 10;
  rangeTags = [];
  rangeCategories = [];
  rangeTagSelections = {};
  pairedMarkingSelectedOptionId = null;

  pairedMarkingGroupOptions : any[] = [
    {id: 1, target_group_size: 2, backup_group_size: 3},
    {id: 2, target_group_size: 3, backup_group_size: 2}
  ]

  renderPerc = (params, prop) => {
    const val = params.data[prop];
    if (val){
      return (+((+val)).toFixed(6))+'%'
    }
    return val
  }

  batchAllocGridOption:any = {
    rowSelection: 'single',
    rowMultiSelectWithClick: false,
    columnDefs: [
      {
        headerName:'ID',
        field:'id',
        width:80,
        checkboxSelection:true 
      },
      {
        headerName:'Description',
        field:'description',
      },
      {
        headerName:'Notes',
        field:'notes',
        tooltipValueGetter: (params) => {
          return params.data.notes;
        },
        width:250
      },
      {
        headerName:'Max Batch Number Claim',
        field:'max_batch_num_claim',
        editable:true,
        valueSetter: (params) => {
          const newMaxBatchClaim = this.selectedBatchAllocTable.max_batch_num_claim;
          if ( newMaxBatchClaim < NEW_MAX_BATCH_CLAIM_LOWER_LIMIT || newMaxBatchClaim > NEW_MAX_BATCH_CLAIM_UPPER_LIMIT ){
            alert("Input range should be from 1 to 5");
            return false;
          }
          else if (newMaxBatchClaim != this.originMaxBatchClaim) {
            if ( confirm("Are you sure to Change"+ params.data.description +"'s \"Max Batch Number Claim\" Value from "+this.originMaxBatchClaim+" to "+ newMaxBatchClaim)) {
            params.data.max_batch_num_claim = newMaxBatchClaim;
            this.auth.apiPatch('public/scor-lead/batch-alloc-policies', params.data.id, {newMaxBatchClaim}).catch((e)=>{
              if(e.message == "EXCEED_MAX_BATCH_SIZE"){
                this.loginGuard.quickPopup("Batch size configuration exceed allowed number.")
              }
            })
            return true; // test : remove it 
          }
        }
        }
      },
      {
        headerName:'Batch Size',
        field:'batch_size',
        editable:true,
        width: 150,
      },
      {
        headerName:'Batch Validity Number',
        field:'batch_validity_num',
      },
      {
        headerName:'Claim Duration Hours',
        field:'claim_dur_hours',
        //editable:true,
      },
      {
        headerName:'Same School Limit Rate',
        field:'same_sch_lim_rate',
        sortable:true,
        filter:true,
        valueGetter: (p) => this.renderPerc(p, 'same_sch_lim_rate'),
        //editable:true
      },
      {
        headerName:'Minimum Rolling Validity Reads (Access)',
        field:'access_rolling_batch_num',
      },
      {
        headerName:'Access Rolling Min Exact Rate',
        field:'access_rolling_min_exact_rate',
        valueGetter: (p) => this.renderPerc(p, 'access_rolling_min_exact_rate'),
      },
      {
        headerName:'Access Rolling Min Exact Adj Rate',
        field:'access_rolling_min_exact_adj_rate',
        valueGetter: (p) => this.renderPerc(p, 'access_rolling_min_exact_adj_rate'),
      },
      {
        headerName:'Minimum Rolling Validity Reads (Rescore)',
        field:'rescore_rolling_batch_num',
      },
      {
        headerName:'Rescore Rolling Min Exact Rate',
        field:'rescore_rolling_min_exact_rate',
        valueGetter: (p) => this.renderPerc(p, 'rescore_rolling_min_exact_rate'),
      },
      {
        headerName:'Rescore Rolling Min Exact Adj Rate',
        field:'rescore_rolling_min_exact_adj_rate',
        valueGetter: (p) => this.renderPerc(p, 'rescore_rolling_min_exact_adj_rate'),
      },
      {
        headerName:'Auto Rescore Rate (for Reliability)',
        field:'auto_rescore_rate',
        valueGetter: (p) => this.renderPerc(p, 'auto_rescore_rate'),
      },
      {
        headerName:'Auto-Blocks Before Lock',
        field:'max_sendbacks',
      },
      {
        headerName:'QT Cut Score',
        field:'qt_cut_score',
      },
      {
        headerName:'Allow Clearing Score',
        field:'allow_score_clear',
        valueGetter: (r) => renderYesNo(this.lang, r.data.allow_score_clear),
      },
      {
        headerName:'Paired Marking',
        field:'is_paired_marking',
        valueGetter: (r) => renderYesNo(this.lang, r.data.is_paired_marking),
      },
      {
        headerName:'Allow Next Response',
        field:'allow_batch_next_navigate',
        valueGetter: (r) => renderYesNo(this.lang, r.data.allow_batch_next_navigate),
      },

    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  }

  constructor(
    private auth:AuthService,
    private loginGuard: LoginGuardService,
    private lang: LangService,
    private routes: RoutesService
  ) { }

  ngOnInit(): void {
    this.loadPolicies();
    this.loadSystemConstantsNumeric();
    this.loadStandardsRangeTags();
  }

  async loadPolicies() {
    this.policies = await this.auth.apiFind('public/scor-lead/batch-alloc-policies', {
      query:{
        markingWindowId: this.markingWindowId
      }
    });

    for (let i of this.policies) {
      const row: any = generateSanitizedRecords(this.batchAllocGridOption.columnDefs, i);
      this.sanitizedPolicies.push(row);
    }
  }

  async loadStandardsRangeTags(){
    this.rangeTags = await this.auth.apiFind(this.routes.SCOR_LEAD_MARKING_STANDARDS_RANGE_TAGS);
    this.rangeCategories = rangeCategorySettings.map(category => {
      return {
        ...category,
        ranges: this.rangeTags.filter(range => range.category == category.id)
      }
    }).filter((category: any) => category.ranges.length)
  }

  async loadSystemConstantsNumeric(){
    const flags = await this.auth.apiFind("public/scor-lead/sys-flags");
    this.maxBatchSizeAllowed = flags.MAX_BATCH_SIZE;
  }

  getExportFilename() {
    return `batch-allocation-policies ` +  moment().format('YYYY-MM-DD[T]HH_mm_ss');
  }

  cellEditingStarted(param){
    switch (param.colDef.field){
      case 'same_sch_lim_rate':
        this.originRate = param.data.same_sch_lim_rate;
        break;
  
      case 'max_batch_num_claim':
        this.originMaxBatchClaim = this.batchAllocGridOption.columnDefs.max_batch_num_claim;
        break;  
  
      case 'claim_dur_hours':
        this.originDurationHours = param.data.claim_dur_hours;
        break;    
  
      default: 
        break;
    }
  }
  
    // Ann WIP
    onEditingForScorer(params){
      const selectedRow = this.batchAllocGridOption.api.getSelectedRows();
      if (selectedRow.length > 0){
        this.selectedBatchAllocTable = selectedRow[0]
        this.resetRangeTagSelections();
        this.resetPairedMarkingSelectedOption();
      } else {
        this.selectedBatchAllocTable = null
      }
    }
    resetPairedMarkingSelectedOption(){
      this.pairedMarkingSelectedOptionId = this.pairedMarkingGroupOptions.find(o => o.target_group_size == this.selectedBatchAllocTable.paired_marking_target_group_size && o.backup_group_size == this.selectedBatchAllocTable.paired_marking_backup_group_size)?.id
      if (!this.pairedMarkingSelectedOptionId && this.selectedBatchAllocTable.is_paired_marking){
        this.pairedMarkingSelectedOptionId = this.pairedMarkingGroupOptions[0].id
      }
    }

    resetRangeTagSelections(){
      this.rangeTagSelections = {};
      const selectedTableExistingTags = JSON.parse(this.selectedBatchAllocTable.standards_range_tag_ids || '[]')
      this.rangeTags.forEach(range => {
        this.rangeTagSelections[range.id] = selectedTableExistingTags.includes(range.id);
      })
    }
  
    isEditingSelectedBatchAllocTable(){
      const table = this.selectedBatchAllocTable;
  
      if (table){
        return Boolean(table.__isEditing);
      }
      return false
    }
  
    makeCopyOfDataBeforeEditing(){
      this.selectedBatchAllocTable.__preEdit = {
        ... this.selectedBatchAllocTable, // make the copy from data from the table
      }
    }
  
    startEditing(){
      const table = this.selectedBatchAllocTable;
  
      table.__isEditing = true
      this.makeCopyOfDataBeforeEditing();
    }
  
    revertToPreEditState(){
      for (const editableField of [
        'max_batch_num_claim',
        'batch_size',
        'claim_dur_hours',
        'same_sch_lim_rate',
        'standards_range_tag_ids',
        'allow_score_clear',
        'paired_marking_target_group_size',
        'paired_marking_backup_group_size',
        'is_paired_marking',
        'allow_batch_next_navigate'
      ]){
        this.selectedBatchAllocTable[editableField] = this.selectedBatchAllocTable.__preEdit[editableField]
      }
      this.resetRangeTagSelections();
      this.resetPairedMarkingSelectedOption();
    }
  
    cancelEdit(){
      const table = this.selectedBatchAllocTable;
  
      this.revertToPreEditState()
      
      table.__isEditing = false
    }
  
    async saveEdit(){
      if(!this.isSchoolLimitRateValid() || !this.isClaimDurationHoursValid() || !this.isBatchSizeValid()){
        return;
      }
      const table = this.selectedBatchAllocTable;
      table.__isEditing = false
  
      const rangeTagIdList = []
      for (let range of this.rangeTags){
        if (this.rangeTagSelections[range.id]){
          rangeTagIdList.push(range.id)
        }
      }

      const rangeTagIds = JSON.stringify(rangeTagIdList);
      const newMaxBatchClaim = this.selectedBatchAllocTable.max_batch_num_claim;
      const newBatchSize = this.selectedBatchAllocTable.batch_size;
      const newRate = this.selectedBatchAllocTable.same_sch_lim_rate;
      const newDescription = this.selectedBatchAllocTable.description;
      const newDurationHours = this.selectedBatchAllocTable.claim_dur_hours;
      const is_paired_marking = this.selectedBatchAllocTable.is_paired_marking ? 1 : 0;
      const allow_score_clear = this.selectedBatchAllocTable.allow_score_clear ? 1 : 0;
      const allow_batch_next_navigate = this.selectedBatchAllocTable.allow_batch_next_navigate ? 1 : 0

      const pairedMarkingOption = this.pairedMarkingGroupOptions.find(o => o.id == this.pairedMarkingSelectedOptionId)
      const paired_marking_target_group_size = is_paired_marking ? pairedMarkingOption?.target_group_size || null : null
      const paired_marking_backup_group_size = is_paired_marking ? pairedMarkingOption?.backup_group_size || null : null
      
      const payload = {
        newRate,
        newMaxBatchClaim,
        newBatchSize,
        newDurationHours,
        rangeTagIds,
        is_paired_marking,
        allow_score_clear,
        paired_marking_target_group_size,
        paired_marking_backup_group_size,
        newDescription,
        allow_batch_next_navigate
      }

      await this.auth.apiPatch('public/scor-lead/batch-alloc-policies', this.selectedBatchAllocTable.id, payload);
      this.selectedBatchAllocTable.paired_marking_target_group_size = paired_marking_target_group_size;
      this.selectedBatchAllocTable.paired_marking_backup_group_size = paired_marking_backup_group_size;

      this.selectedBatchAllocTable.standards_range_tag_ids = JSON.stringify(rangeTagIdList)
      
     
      this.batchAllocGridOption.api.refreshCells() // refresh without user refresh the browser
    }
  
    isMaxBatchNumberValid(){
      const userInput = this.selectedBatchAllocTable.max_batch_num_claim;
      return userInput >= 1 && userInput <= 5;
    }

    isBatchSizeValid(){
      const userInput = this.selectedBatchAllocTable.batch_size;
      return userInput >= 1 && userInput <= this.maxBatchSizeAllowed;
    }
  
    isClaimDurationHoursValid(){
      const userInput = this.selectedBatchAllocTable.claim_dur_hours;
      return userInput >= 1 && userInput <= 24;
    }
  
    isSchoolLimitRateValid(){
      const userInput  = this.selectedBatchAllocTable.same_sch_lim_rate;
  
      return userInput % 1 === 0 && userInput !== '' && userInput >= 0 && userInput <= 100;
    }
  
    isEditFormValid(){
      return this.isBatchSizeValid();
    }
}
