<!--  *ngIf="trainingType == AssignedItemComponentType.TRAINING_MATERIALS" -->
<div class="page-body">
  <div>
    <div class="header-extension">
      <header 
        [breadcrumbPath]="breadcrumb" 
        [hasSidebar]="true"
        [hideSupportBtn]="true"
        [isOpaque]="true"
        techSupportDataKey="SCORING_SUPPORT"
      ></header>
    </div>
    <div class="page-content is-fullpage" *ngIf="!isLoaded"> Loading... </div>
    <div class="page-content is-fullpage" *ngIf="isLoaded">
      <div>
        <div class="panel-scoring">
          <div class="panel-score-left" style="overflow:none" [class.is-compressed-to-nil]="isFocusedOnStudentResponse">
            <div style="display:flex; flex-direction:column; height:100%;">
              <ng-container *ngIf="isEnteringHistorical">
                <div style="flex-grow:1; padding:1em;">
                  <div>Recently Inserted responses</div>
                  <ul>
                    <li *ngFor="let record of recentlyCreated">
                      <span class="tag is-info">{{record.scoreCaption}}</span>
                      <span style="margin-left:1em;">{{record.taqr_id}}</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <button (click)="endHistorical()" class="button is-danger">
                    Exit New Exemplar Mode
                  </button>
                </div>
              </ng-container>
              <ng-container *ngIf="!isEnteringHistorical">
                <div *ngIf="viewType === ViewType.STAGE">
                  <div style="display: flex; flex-direction:row; margin-bottom:0.5em; margin-top:0.5em;">
                      <button  class="button is-small" (click)="incrStageNum(-1)" style="margin-right:0em;">
                        <i class="fas fa-arrow-left"></i>
                      </button>
                      <select style="flex-grow:1" [(ngModel)]="stageNumSelection" (change)="updateView({stageNum: stageNumSelection})" class="select is-small">
                          <option value="1"><tra slug="lbl_osslt_scor_stage"></tra> 1: <tra slug="lbl_osslt_scor_range"></tra></option>
                          <option value="2"><tra slug="lbl_osslt_scor_stage"></tra> 2: <tra slug="lbl_osslt_scor_score"></tra></option>
                          <option value="3"><tra slug="lbl_osslt_scor_stage"></tra> 3: <tra slug="lbl_osslt_scor_proposal"></tra></option>
                          <option value="4"><tra slug="lbl_osslt_scor_stage"></tra> 4: <tra slug="lbl_osslt_scor_incl"></tra></option>
                      </select>
                      <button  class="button is-small" (click)="incrStageNum(1)">
                        <i class="fas fa-arrow-right"></i>
                      </button>
                  </div>
                  <div class="filterbox-container">
                    <div class="space-between">
                      <mat-slide-toggle [(ngModel)]="filterRejectedResponse" (change)="toggleFilterRejectResponse()">
                        <small>
                          <tra slug="lbl_abed_standard_confirming_filter_rejected"></tra>
                        </small>
                      </mat-slide-toggle>
                      <button (click)="sortResponses()" [disabled]="stageNumSelection==1">
                        Sort
                      </button>
                    </div>
                    <div (click)="isCountsExpanded = !isCountsExpanded" class="clickable">
                      <strong><tra slug="lbl_abed_counts_range"></tra></strong>
                      <span [ngSwitch]="isCountsExpanded" style=" margin-left:0.5em;">
                        <i *ngSwitchCase="true" class="fa fa-caret-down"></i>
                        <i *ngSwitchCase="false" class="fa fa-caret-right"></i>
                      </span>
                    </div>
                    
                    <ng-container *ngIf="isCountsExpanded">
                      <div>
                        <div><tra slug="lbl_abed_apply_filter"></tra></div>
                      </div>
                      <div style="max-height: 150px; overflow-y: scroll;">
                        <button 
                          style="margin-top: 3px; width: 97%"
                          class="button is-fullwidth is-small" 
                          [class.range-btn-selected]="isQuickFilterSelected(FilterSpecialValues.ALL)" 
                          (click)="applyRangeQuickFilter(FilterSpecialValues.ALL)" 
                        >
                          <div style="text-align: left; width: 100%">
                            <span>
                              <span>
                                  <strong>{{ loadedResponses.length }}</strong>
                              </span>
                              <span style="margin: 0 6px">|</span>
                              <span><tra slug="lbl_all"></tra></span>
                            </span>
                            <span style="margin-left: 6px">
                              <i class="fa fa-caret-right  toggle-caret"></i>
                            </span>
                          </div>
                        </button>
                        <button 
                          style="margin-top: 3px; width: 97%"
                          class="button is-fullwidth is-small" 
                          [class.range-btn-selected]="isQuickFilterSelected(FilterSpecialValues.NO_VALUE)" 
                          (click)="applyRangeQuickFilter(FilterSpecialValues.NO_VALUE)" 
                        >
                        <div style="text-align: left; width: 100%">
                          <span>
                            <span>
                                <strong>{{ getCountForRangefilter(FilterSpecialValues.NO_VALUE) }}</strong>
                            </span>
                            <span style="margin: 0 6px">|</span>
                            <span><tra slug="lbl_unselected"></tra></span>
                          </span>
                          <span style="margin-left: 6px">
                            <i class="fa fa-caret-right  toggle-caret"></i>
                          </span>
                        </div>
                      </button>
                        <div *ngFor="let rangeCategory of rangeCategories">
                          <div *ngFor="let range of rangeCategory.ranges">
                            <button 
                              *ngIf="getCountForRangefilter(range.props.tag1)"
                              style="margin-top: 3px; width: 97%;"
                              [ngStyle]="{ 'background': rangeCategory.bgColor }"
                              class="button is-fullwidth is-small" 
                              [class.range-btn-selected]="isQuickFilterSelected(range.props.tag1)" 
                              (click)="applyRangeQuickFilter(range.props.tag1)" 
                            >
                              <div style="text-align: left; width: 100%">
                                <span>
                                  <span>
                                      <strong>{{ getCountForRangefilter(range.props.tag1) }}</strong>
                                  </span>
                                  <span style="margin: 0 6px">|</span>
                                  {{range.caption}}
                                </span>
                                <span style="margin-left: 6px">
                                  <i class="fa fa-caret-right  toggle-caret"></i>
                                </span>
                              </div>
                            </button>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>
                  <div class="filterbox-container" style="background-color: rgba(0,0,60,0.1);">
                    <div style="display:flex; flex-direction:row; justify-content: space-between; align-items:center;">
                      <strong style="margin-right:0.5em;">
                        <tra slug="lbl_osslt_scor_filter"></tra>
                      </strong>
                      <div [ngSwitch]="!!isFilterEditing">
                        <div *ngSwitchCase="false">
                          <button (click)="isFilterEditing=true" class="button is-small">
                            <tra slug="sa_classrooms_edit"></tra>
                          </button>
                        </div>
                        <div  *ngSwitchCase="true">
                          <button (click)="isFilterEditing=false" class="button is-small">
                            <tra slug="btn_close"></tra>
                          </button>
                          <button (click)="updateSubFilter()" class="button is-small">
                            <tra slug="lbl_apply_filter"></tra>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div *ngIf="isFilterEditing" style="margin-top:0.5em;">
                      <div *ngFor="let filterOption of getFilterOptions()" style="border-top:1px solid #bbb; padding-top:0.3em; margin-bottom:0.3em; display:flex; flex-direction:row;">
                        <div style="width: 6em; flex-shrink:0;">
                          {{filterOption.caption}}:
                        </div>
                        <input  *ngIf="filterOption.isUserInput" class="input is-small" [(ngModel)]="filterOption.val">
                        <select *ngIf="!filterOption.isUserInput" multiple style="flex-grow:1;" [(ngModel)]="filterOption.val">
                          <option [value]="FilterSpecialValues.ALL">(<tra slug="lbl_all"></tra>)</option>
                          <option [value]="FilterSpecialValues.NO_VALUE">(<tra slug="lbl_unselected"></tra>)</option>
                          <option *ngFor="let option of filterOption.options" [value]="option.props[filterOption.getProp]">{{option.caption}}</option>
                        </select>
                      </div>
                      <div style="margin-top:0.5em;">
                        <button (click)="updateSubFilter()" class="button is-small is-fullwidth">Apply Filters</button>
                      </div>
                    </div>
                    <!-- <div style="margin-top:0.5em;">
                      <button (click)="updateSubFilter()"><i class="fas fa-sync-alt"></i></button>
                    </div> -->
                  </div>
                </div>
                <div style="overflow-y:scroll; font-size:0.85em; flex-grow:1;">
                  <table class="selection-table">
                    <thead style="position: sticky; top: 0">
                      <tr>
                        <th rowspan="2">ID</th>
                        <th rowspan="2"><tra slug="lbl_osslt_scor_range"></tra></th>
                        <th [attr.colspan]="isCodeColumnCollapsed ? 1 : markingWindowItems.length">
                          <tra slug="lbl_osslt_scor_score"></tra>
                          <i style="margin-left: 0.5em" class="fa has-text-grey-light" (click)="isCodeColumnCollapsed = !isCodeColumnCollapsed" [class.fa-caret-right]="isCodeColumnCollapsed" [class.fa-caret-left]="!isCodeColumnCollapsed"></i>
                        </th>
                        <th rowspan="2"><tra slug="lbl_expert_score_abr"></tra></th>
                        <th rowspan="2"><tra slug="lbl_osslt_scor_prop_abr"></tra></th>
                        <th rowspan="2"><tra slug="lbl_osslt_scor_incl_abr"></tra></th>
                      </tr>
                      <tr *ngIf="!isCodeColumnCollapsed">
                        <th *ngFor="let mwi of markingWindowItems">
                          {{mwi.scale_skill_code}}
                        </th>
                      </tr>
                    </thead>
                    <tr 
                      *ngFor="let response of responses" 
                      style="cursor:pointer;" 
                      [class.is-response-selected]="response.id === currentResponseId" 
                      [class.is-response-presence]="isResponsePresence(response)" 
                      (click)="selectResponse(response)"
                    >
                      <td >
                        <div class="response-id">
                          {{response.id}}
                          <div class="presence-indic-container">
                            <ng-container *ngFor="let user of getResponsePresence(response)">
                              <div class="presence-indic" [title]="user.display_name">
                                {{user.initials}}
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </td>
                      <td [class.is-focused]="checkStageSelection([1])" [class.is-filled]="!!response.tag1">{{renderRangeSlug(response.tag1)}}</td>
                      <ng-container *ngIf="!isCodeColumnCollapsed" >
                        <td *ngFor="let mwi of markingWindowItems" [class.has-background-danger]="response.isCombinationInvalid">
                          <span> {{renderScoreOptionSlug(response.mwi_id_to_score_option_id[mwi.id])}} </span>
                        </td>
                      </ng-container>
                      <td *ngIf="isCodeColumnCollapsed" [class.has-background-danger]="response.isCombinationInvalid">
                        <span *ngIf="isSomeScaleScored(response)" [class.has-text-grey-light]="!areAllScalesScored(response)">
                          <i class="fa fa-check"></i>
                        </span>
                      </td>
                      <td>
                        <span *ngIf="response.is_expert_score">
                          <i class="fa fa-check"></i>
                        </span>
                      </td>
                      <td [class.is-focused]="checkStageSelection([3])" [class.is-filled]="!!response.tag2"> {{renderRefProp(responseSetRef, setTypeToSetID(response.tag2), 'slug')}} </td>
                      <td [class.is-focused]="checkStageSelection([4])" [class.is-filled]="hasInclusions(response)" [class.has-text-danger]="getResponseInclusions(response).length > 1"> {{hasInclusions(response) ? renderResponseInclusionSlugList(response) : ''}} </td>
                    </tr>
                  </table>
                  <div *ngIf="isResponsesLoading" class="has-text-centered" style="margin-top: 1em">
                    Loading responses... {{loadPercent}} %
                  </div>
                </div>
                <div>
                  <button [disabled]="isRangeFinder" class="button is-small is-fullwidth is-link" [class.is-loading]="isPrintDocProcessing" (click)="onLoadResponsesDocxClick()"><tra slug="btn_abed_download_for_print"></tra></button>
                </div>
                <ng-container *wlCtx="'RAFI_INSERT_HISTORICAL'">
                  <div *ngIf="viewType === ViewType.STAGE">
                    <!-- Historical exemplars blocked for now -->
                    <button class="button is-small is-fullwidth" (click)="startHistorical()" [disabled]="true">
                      Insert Historical
                    </button>
                  </div>
                </ng-container>
              </ng-container>
            </div>
          </div>
          <div class="panel-score-middle">
            <div class="columns" style="padding: 1em;">
              <div class="column">
                <div style="font-size:1.2em;"> {{itemCaption}} </div>
                <div style="font-weight: 600;"> {{itemGroupName}} </div>
              </div>
              <div *ngIf="currentResponse?.id" class="column is-narrow">
                <span><strong>Response ID: </strong></span>
                <span>{{currentResponse.id}}</span>
              </div>
            </div>
            <div *ngIf="isEnteringHistorical" style="padding:1em">
              <h1>Historical Exemplar Loading</h1>
              <button class="button" [class.is-info]="newResponse.isPaper" (click)="newResponse.isPaper = !newResponse.isPaper">Paper</button>
              <button class="button" [class.is-info]="newResponse.isOnline" (click)="newResponse.isOnline = !newResponse.isOnline">Online</button>
              <!-- upload -->
              <div *ngIf="newResponse.isPaper">
                Upload image: 
                <capture-image 
                  [element]="newResponse"
                  urlProp="scan_url"
                  fileType="image"
                  [isCondensed]="false"
                  [displayAltText]="false"
                  [ignoreDisablingService]="true"
                ></capture-image>
              </div>
              <!-- entry -->
              <ng-container *ngIf="newResponse.isOnline">
                <panel-score-content
                  responseType="TEXT"
                  [windowItemId]="getMwiId()"
                  [isLeaderView] = "true"
                  [isRangeFinderView]="true"
                  [isEditable]="true"
                  [responseRawContainer]="newResponse"
                  [isFocusedOnStudentResponse]="isFocusedOnStudentResponse"
                  (toggleFocusView)="toggleFocusView()"
                ></panel-score-content>
              </ng-container>
              <!-- annotation -->
              <hr>
              <strong><tra slug="lbl_osslt_scor_notes"></tra></strong>
              <textarea 
                [(ngModel)]="newResponse.rationale"  
                class="textarea"
              ></textarea>
            </div>
            <ng-container *ngIf="!isEnteringHistorical && isCurrentResponse()">
              <div style="display:flex; flex-direction:column; justify-content: space-between; min-height:100%;">
                <panel-score-content
                  responseType="TEXT"
                  [isLeaderView] = "true"
                  [isRangeFinderView]="true"
                  [rawResponseId]="getCurrentResponseId()"
                  [windowItemId]="getMwiId()"
                  [isFocusedOnStudentResponse]="isFocusedOnStudentResponse"
                  (toggleFocusView)="toggleFocusView()"
                ></panel-score-content>
                <div *ngIf="currentResponse" style="padding:1em;">
                  <div style="display:flex; flex-direction:row; justify-content: space-between;">
                    <div>
                      <strong><tra slug="lbl_osslt_scor_notes"></tra></strong>
                    </div>
                    <div>
                      <button *ngIf="!isEditingRationale" [disabled]="isLocked" (click)="isEditingRationale = true" class="button is-small">
                        <tra slug="sa_classrooms_edit"></tra>
                      </button>
                      <button *ngIf="isEditingRationale" [disabled]="isLocked" (click)="saveRationaleEdit(currentResponse.rationale)" class="button is-small">
                        <tra slug="btn_save"></tra>
                      </button>
                    </div>
                  </div>
                  <div [ngSwitch]="!!isEditingRationale">
                    <textarea 
                      *ngSwitchCase="true" 
                      [(ngModel)]="currentResponse.rationale"  
                      class="textarea"
                    ></textarea>
                    <tra-md
                      *ngSwitchCase="false" 
                      [slug]="currentResponse.rationale" 
                    ></tra-md>
                  </div>
                  <div *ngIf="currentResponse && currentResponse.commentTree">
                    <hr/>
                    <strong><tra slug="lbl_osslt_scor_comments"></tra></strong>
                    <div style="margin: 1em 0">
                      <button [disabled]="isLocked" (click)="launchNewCommentModal()" class="button is-small">
                        <tra slug="auth_new_comment"></tra>
                      </button>
                    </div>
                    <div *ngFor="let comment of currentResponse.commentTree" style="margin-top: 1em">
                      <widget-comment
                        [comment]="comment"
                        [response]="currentResponse"
                        [markingWindowItems]="markingWindowItems"
                        (newReply)="launchCommentReplyModal($event)"
                        (patchComment)="launchCommentEditModal($event)"
                        (deleteComment)="deleteComment($event)"
                      ></widget-comment>
                    </div>
                  </div>

                </div>
              </div>
            </ng-container>
            
          </div>
          <div class="panel-score-right" *ngIf="viewType === ViewType.STAGE" [class.is-compressed-to-nil]="isFocusedOnStudentResponse">
            <div style="display:flex; flex-direction:column; justify-content: space-between; height: 100%; padding: 1em;">
              <ng-container *ngIf="isEnteringHistorical">
                <div style="flex-grow:1">
                  <div style="margin-bottom:0.5em; margin-top: 1em;">
                    <tra-md slug="txt_osslt_scor_assign_set"></tra-md>
                  </div>
                  <div class="select is-fullwidth">
                    <select [(ngModel)]="newResponseTargetSet"> 
                      <option></option>
                      <option *ngFor="let set of responseSets" [value]="set.props.set_id">{{set.caption}}</option>
                    </select>
                  </div>
                  <div style="margin-bottom:0.5em; margin-top: 1em;">
                    <tra-md slug="txt_osslt_scor_assign_score"></tra-md>
                  </div>
                  <div *ngFor="let scoreOption of scoreOptions">
                    <button 
                      class="button is-fullwidth" 
                      [class.is-info]="scoreOption.props.score_option_id === newResponse.score_option_id" 
                      (click)="newResponse.score_option_id = scoreOption.props.score_option_id" 
                    >
                      {{scoreOption.caption}}
                    </button>
                  </div>
                </div>
                <div>
                  <button (click)="saveHitorical()" [disabled]="newResponse.isSaving" class="button is-success is-fullwidth" style="margin-top:3em;">
                    Save New Exemplar
                  </button>
                </div>
              </ng-container>
              <ng-container *ngIf="!isEnteringHistorical">
                <div *ngIf="currentResponseId" [ngSwitch]="stageNum" style="padding-right:0.5em;">
                  <div class="score-nav-container">
                    <div class="nav-progress">
                      <span *ngIf="currentResponseId">{{currentResponseIndex+1}} of </span> 
                      {{responses.length}}
                      <span *ngIf="!currentResponseId">
                        <tra slug="snip_responses"></tra>
                      </span> 
                    </div>
                    <div class="nav-buttons">
                      <button 
                        class="button is-small"
                        (click)="gotoPrevResponse()"
                      >
                        <tra slug="btn_prev_resp"></tra>
                      </button>
                      <button 
                        class="button is-small"
                        (click)="gotoNextResponse()"
                      >
                        <tra slug="btn_next_resp"></tra>
                      </button>
                    </div>
                  </div>
                  <div class="response-score-info" *ngIf="currentResponse">
                    <table>
                      <tr *ngIf="checkStageSelection([2,3,4])">
                        <td><strong><tra slug="lbl_osslt_scor_range"></tra></strong></td>
                        <td> {{renderRefProp(rangeRef, currentResponse.tag1, 'caption')}}   </td>
                        <td></td>
                      </tr>
                      <tr *ngIf="checkStageSelection([3,4])">
                        <td><strong><tra slug="lbl_osslt_scor_score"></tra></strong></td>
                        <td> 
                          <span *ngFor="let mwi of markingWindowItems">
                            <span *ngIf="currentResponse.mwi_id_to_score_option_id[mwi.id]" >
                              <b>{{mwi.scale_skill_code}}: </b> {{renderScoreOptionSlug(currentResponse.mwi_id_to_score_option_id[mwi.id])}}
                            </span>
                          </span>
                        </td>
                        <td></td>
                      </tr>
                      <tr *ngIf="checkStageSelection([4])">
                        <td><strong><tra slug="lbl_osslt_scor_proposal"></tra></strong></td>
                        <td> {{renderRefProp(responseSetRef, setTypeToSetID(currentResponse.tag2), 'caption')}}   </td>
                        <td></td>
                      </tr>
                      <tr *ngIf="checkStageSelection([4])"> 
                        <td><strong><tra slug="lbl_osslt_scor_incl"></tra></strong></td>
                        <td> 
                          <div *ngFor="let inclusion of getResponseInclusions(currentResponse)">
                            {{renderRefProp(responseSetRef, inclusion.set_id, 'caption')}}
                          </div>
                        </td>
                        <td >
                          <div *ngFor="let inclusion of getResponseInclusions(currentResponse)">
                            <ng-container [ngSwitch]="!!checkSetSelection(inclusion.set_id)">
                              <ng-container *ngSwitchCase="false">
                                <a *ngIf="inclusion.response_set_num" (click)="selectSetById(inclusion.set_id)"><tra slug="btn_focus_on"></tra></a>
                              </ng-container>
                              <em *ngSwitchCase="true"><tra slug="lbl_in_focus"></tra></em>
                            </ng-container>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                  <div *ngSwitchCase="1">
                    <div style="margin-bottom:0.5em; margin-top: 1em;">
                      <tra-md slug="txt_osslt_scor_assign_range"></tra-md>
                    </div>
                    <div style="display: flex; flex-direction: column; gap: 1.5em">
                      <div *ngIf="!rangeCategories.length" class="notification is-warning">
                        No range tags available, check the relevant scoring policy.
                      </div>
                      <div *ngFor="let rangeCategory of rangeCategories">
                        <h4 *ngIf="rangeCategory.caption" style="margin-bottom: 0.3em" class="has-text-centered"><tra [slug]="rangeCategory.caption"></tra></h4>
                        <div *ngFor="let range of rangeCategory.ranges">
                          <button 
                            style="margin-top: 3px;"
                            [ngStyle]="{ 'background': rangeCategory.bgColor }"
                            class="button is-fullwidth is-small" 
                            [disabled]="isLocked"
                            [class.range-btn-selected]="range.props.tag1 === currentResponse.tag1" 
                            (click)="assignTag(range.props.tag1)" 
                          >
                            {{range.caption}}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div *ngSwitchCase="2">
                    <div style="margin-bottom:0.5em; margin-top: 1em;">
                      <tra-md slug="txt_osslt_scor_assign_score"></tra-md>
                    </div>

                    <widget-score-options
                    [responseId]="currentResponseId"
                    [itemGroupName]="itemGroupName"
                    [isLocked]="isLocked || currentResponse.is_expert_score"
                    [markingWindowItems]="markingWindowItems"
                    [mwi_id_to_score_option_id]="currentResponse.mwi_id_to_score_option_id"
                    (assignScore)="assignScore($event.score_option_id, $event.mwi_id)"
                    >
                    </widget-score-options>

                    <div *ngIf="isExpertScoreEdit && areAllScalesScored(currentResponse)">
                      <button *ngIf="!currentResponse.is_expert_score" (click)="onClickAssignExpertScore()" class="button is-success is-light is-fullwidth" [disabled]="isLocked">
                        <tra slug="btn_mark_expert_score"></tra>
                      </button>
                      <button  *ngIf="currentResponse.is_expert_score" (click)="onClickUnassignExpertScore()" class="button is-success is-inverted is-fullwidth" [disabled]="isLocked">
                        <tra slug="btn_unmark_expert_score"></tra>
                      </button>
                    </div>
                    <div *ngIf="currentResponse.is_expert_score && !isExpertScoreEdit" class="notification is-success is-light">
                      <tra slug="lbl_sf_expert_score_no_edit_msg"></tra>
                    </div>
                  </div>
                  <div *ngSwitchCase="3">
                    <div style="margin-top:1em;">
                      <tra-md slug="txt_osslt_scor_assign_propose"></tra-md>
                    </div>
                    <div *ngFor="let setType of responseSetTypes">
                      <button 
                        class="button is-fullwidth" 
                        [disabled]="isLocked"
                        [class.is-info]="setType.props.id === currentResponse.tag2" 
                        (click)="assignProposed(setType.props.id)" 
                      >
                        {{setType.caption}}
                      </button>
                      
                    </div>
                  </div>
                  <div *ngSwitchCase="4">
                    <div *ngIf="getResponseInclusions(currentResponse).length > 1" class="notification is-danger is-light">
                      <tra-md slug="msg_rf_multi_incl_warn"></tra-md>
                    </div>
                    <div style="margin-top:1em;">
                      <tra-md slug="txt_osslt_scor_assign_set"></tra-md>
                    </div>
                    <div class="select is-fullwidth">
                      <select [(ngModel)]="targetResponseSet" (change)="onTargetResponseSetChange()">
                        <option *ngFor="let set of responseSets" [value]="set.props.set_id">{{set.caption}}</option>
                      </select>
                    </div>
                    
                    <div 
                      *ngIf="targetResponseSet"
                      class="notification is-light is-narrow has-text-centered"
                      style="margin: 1em 0"
                      [class.is-link]="currentResponse.inclusions[targetResponseSet]"
                    >
                      <tra [slug]="currentResponse.inclusions[targetResponseSet] ? 'lbl_osslt_scor_incl' : 'lbl_osslt_scor_not_incl'"></tra>
                    </div>
                    <div *ngIf="!currentResponse.inclusions[targetResponseSet]">
                      <button 
                        (click)="assignInclusion(1)" 
                        [disabled]="!targetResponseSet"  
                        class="button is-fullwidth" 
                        [class.is-info]="!!currentResponse.inclusions[targetResponseSet]" 
                      >
                        <tra slug="btn_include"></tra>
                      </button>
                    </div>
                    <div *ngIf="!!currentResponse.inclusions[targetResponseSet]">
                      <button (click)="assignInclusion(0)" class="button is-fullwidth">
                        <tra slug="btn_dont_use"></tra>
                      </button>
                    </div>


                    <ng-container *ngIf="targetResponseSet">
                      <div class="checkbox-header-container">
                        <div class="checkboxes">
                          <span class="check-container" *ngFor="let set of responseSets">
                            {{set.slug}}
                          </span>
                        </div>
                      </div>
                    
                      <div 
                        cdkDropList  
                        (cdkDropListDropped)="setOrderDrop(currentResponseSet, $event);" 
                        class="set-container"
                      >
                        <div 
                          cdkDrag 
                          [cdkDragDisabled]="isLocked"
                          *ngFor="let response of currentResponseSet; let index = index;" 
                          class="response-set-element"
                          [class.is-selected]="response.id === currentResponse.id"
                          [class.out-of-focus]="!response.inFocusedSet"
                        >
                          <!-- to do: drag -->
                          <div style="width:3em; text-align:center;"> 
                            <!-- {{index + 1}}  -->
                            {{response.inFocusedSet ? response.inclusions[targetResponseSet].response_set_order : ''}}
                          </div>
                          <button cdkDragHandle [disabled]="isSavingSetOrder" class="button is-small is-handle">
                            <i class="fas fa-arrows-alt"></i>
                          </button>
                          <div style="flex-grow:0.03">
                            <a (click)="selectResponse(response)">
                              {{response.id}}
                            </a>
                          </div>
                          <div class="tag is-info">
                            {{renderRefProp(scoreOptionRef, response.score_option_id, 'caption')}}
                          </div>
                          <div class="checkboxes">
                            <span class="check-container" *ngFor="let set of responseSets">
                              <mat-checkbox 
                                color="primary" 
                                [disabled]="isLocked"
                                [(ngModel)]="response.inclusions[set.props.set_id]"
                                (ngModelChange)="clickedCheckbox($event, response, set)">
                              </mat-checkbox>
                            </span>
                          </div>
                        </div>
                      </div>
                      <!-- <button (click)="makeSetAvailable()" [disabled]="isSetReleasing" class="button is-fullwidth is-success" >
                        <tra slug="btn_make_set_avail"></tra>
                      </button> -->
                    </ng-container>
                  </div>
                </div>
                <div *ngIf="false">
                  <button (click)="excludeItem()" class="button is-small is-fullwidth is-danger">Exclude this Response from Range Finding Pool</button>
                </div>
              </ng-container>
            </div>
            <!-- [scoreProfile]="scoreProfile"
            [isPrevRespNav]="!isPrevResponseLocked()"
            [isNextRespNav]="!isNextResponseLocked()"
            (gotoPrevResponse)="gotoPrevResponse()"
            (gotoNextResponse)="gotoNextResponse()" -->
          </div>
        </div>
      <!-- <div div *ngSwitchCase="false" > Loading... </div> -->
      </div>
    </div>
    
  </div>
  <footer [hasLinks]="false" techSupportDataKey="SCORING_SUPPORT"></footer>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">

    <ng-container *ngIf="cModal().type == RangeFindingModalType.MULTI_EXPERT_SCORE">
      <div class="space-between">
        <div class="notification">
          <tra slug="lbl_rf_expert_score_multi_msg"></tra>
        </div>
        <div>
          <mat-slide-toggle [(ngModel)]="isModalColumnsWrapped">
            Wrap columns? (for small screens)
          </mat-slide-toggle>
        </div>
      </div>
      <div class="panel-columns" [class.is-columns-wrapped]="isModalColumnsWrapped" style="height: 100%;">
        <div style="height: 100%; overflow: auto; min-width: 20em;">
          <ng-container *ngFor="let responseTab of cmc().responseTabs">
            <panel-score-content
              responseType="TEXT"
              [isLeaderView] = "true"
              [isRangeFinderView]="true"
              [rawResponseId]="responseTab.response.id"
              [windowItemId]="responseTab.markingWindowItems[0].id"
              [isFocusedOnStudentResponse]="isFocusedOnStudentResponse"
              [isNoInnerScroll]="true"
            ></panel-score-content>
          </ng-container>
        </div>
        <div style="height: 100%; overflow: auto; min-width: 30em;">
          <ng-container *ngFor="let responseTab of cmc().responseTabs">
            <widget-score-options
              [responseId]="responseTab.response.id"
              [itemGroupName]="responseTab.caption"
              [markingWindowItems]="responseTab.markingWindowItems"
              [mwi_id_to_score_option_id]="responseTab.response.mwi_id_to_score_option_id"
              (assignScore)="assignScore($event.score_option_id, $event.mwi_id, responseTab.response, responseTab.id)"
            >
            </widget-score-options>
          </ng-container>
        </div>
      </div>
      <modal-footer [isEditDisable]="isEditDisabledExpertScore()" [isConfirmAlert]="true" confirmationMessage="lbl_rf_expert_score_multi_confirm_msg" [pageModal]="pageModal"></modal-footer>
    </ng-container>

    <ng-container *ngIf="cModal().type !== RangeFindingModalType.MULTI_EXPERT_SCORE">
      <div>
        <mat-slide-toggle [(ngModel)]="isModalColumnsWrapped">
          Wrap columns? (for small screens)
        </mat-slide-toggle>
      </div>
      <div class="panel-columns" [class.is-columns-wrapped]="isModalColumnsWrapped" style="max-height: 75vh">
        <div  style="height: 100%; overflow: auto; min-width: 20vw">
          <panel-score-content
          responseType="TEXT"
          [isLeaderView] = "true"
          [isRangeFinderView]="true"
          [rawResponseId]="getCurrentResponseId()"
          [windowItemId]="getMwiId()"
          [isFocusedOnStudentResponse]="isFocusedOnStudentResponse"
          (toggleFocusView)="toggleFocusView()"
          ></panel-score-content>
        </div>
        <div  style="height: 100%; overflow: auto; min-width: 30vw;">
          <h1 *ngIf="cModal().type == RangeFindingModalType.NEW_COMMENT">New Comment</h1>
          <h1 *ngIf="cModal().type == RangeFindingModalType.COMMENT_REPLY">New Reply</h1>
          <h1 *ngIf="cModal().type == RangeFindingModalType.COMMENT_EDIT">Edit Comment</h1>

          <div class="columns" *ngIf="cModal().type == RangeFindingModalType.COMMENT_REPLY" style="border: 2px solid lightgrey; border-radius: 0.5em;">
            <div class="column">
                <h4>Replying to:</h4>
                <div *ngIf="isSomeScoreGiven(cmc().parent_mwi_id_to_score_option_id)">
                  <widget-score-options
                  [responseId]="currentResponseId"
                  [itemGroupName]="itemGroupName"
                  [markingWindowItems]="markingWindowItems"
                  [mwi_id_to_score_option_id]="cmc().parent_mwi_id_to_score_option_id"
                  [isLocked]="true"
                  >
                  </widget-score-options>
                </div>
                <div style="margin-top: 1em" class="has-text-grey is-italic">{{cmc().parentComment}}</div>
              </div>
              <div class="column is-narrow icon is-large is-flex is-justify-content-center">
                <i class="fas fa-reply fa-thin has-text-grey"></i>
              </div>
          </div>

          <mat-slide-toggle [(ngModel)]="cmc().includeSuggestedScore" (change)="toggleCommentIncludeSuggestedScore($event)">
            Include suggested score
          </mat-slide-toggle>
          <div *ngIf="cmc().includeSuggestedScore" style="margin-top: 2em">
            <widget-score-options
            [responseId]="currentResponseId"
            [itemGroupName]="itemGroupName"
            [markingWindowItems]="markingWindowItems"
            [mwi_id_to_score_option_id]="cmc().mwi_id_to_score_option_id"
            (assignScore)="assignScoreInComment($event.score_option_id, $event.mwi_id)"
            >
            </widget-score-options>
          </div>
          <textarea
          style="margin-top: 2em"
          class="textarea"
          placeholder="Enter comment text..."
          [(ngModel)]="cmc().comment"
          cdkTextareaAutosize
          [cdkTextareaAutosize]="true"
          [cdkAutosizeMinRows]="2"
          ></textarea>

        </div>
      </div>
      <modal-footer [isEditDisable]="isCommentSaveDisabled()" [pageModal]="pageModal"></modal-footer>
    </ng-container>

  </div>
</div>


