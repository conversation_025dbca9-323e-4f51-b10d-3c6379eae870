@import '../../../styles/page-types/standard.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/pseudo-objects/dashboard-view-summary.scss';
@import '../../../styles/pseudo-objects/panel-scoring';
@import '../../../styles/partials/_modal.scss';

.custom-modal {
  @extend %custom-modal;
}

.page-body {
    @extend %page-body;
    background-color:$off-white;
    .page-container {
    }
}

.set-container {
    margin-bottom:1em; 
    padding:0.5em; 
    border-radius:0.5em; 
    background-color:#fff;
    overflow-y: auto;
    max-height: 45vh;
}

.panel-columns {
    display: flex;
    flex-direction: row;
    &.is-columns-wrapped {
        flex-wrap: wrap;
    }
}


.score-nav-container {
    padding:0.5em;
    display: flex;
    flex-direction: row;;
    justify-content: space-between;
    align-items: center;
    border-bottom:1px solid #ccc;
    .nav-progress {
        margin-right:1em;
    }
}

table.selection-table {
    th,td {
        text-align: center;
        border-width: 1px;
        vertical-align: middle;
        background: white;
        &.response-id-cell {
            padding:0em;
        }
    }
    .response-id {
        color: #3298dc;
        position: relative;
        padding:0.8em;
        .presence-indic-container {
            overflow: hidden;
            justify-content: flex-end;
            position: absolute;
            display: flex;
            top: -0.4em;
            right: -0.5em;
            font-size: 0.6em; /* Adjust text size to fit inside the circle */
            .presence-indic {
                width: 1.8em;
                height: 1.8em;
                margin-left:0.2em;
                background-color: rgba(0,0,0,0.8);
                color: white;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    tr.is-response-presence td {
        background-color: #c35d00;
        color: #fff;
        .response-id {
            color: #f6ff00;
        }
        &.is-focused {
            background-color: rgb(95, 61, 34);
        }
    }
    tr.is-response-selected td {
        background-color: rgb(37, 80, 196);
        color: #fff;
        .response-id {
            color: #fff;
        }
        &.is-focused {
            background-color: rgb(11, 44, 134);
        }
    }
}

.response-set-element, .checkbox-header-container {
    position:relative;
}
.checkbox-header-container{
    height: 2em;
    margin-right: 1.4em;
    margin-bottom: 1.1em;
}
.checkboxes{
    position: absolute;
    left: 55%;
    top: 6px;
    .check-container{
        width: 1.4em;
        display: inline-block;
        text-align:center;
    }
}

.response-sets-container {
    margin-bottom: 20em;
}

.out-of-focus{
    filter: opacity(0.5);
}

.range-btn-selected{
  font-weight: bold;
  border: 1.3px solid black
}

.filterbox-container{
  display:flex;
  flex-direction: column;
  margin-bottom:1em;
  padding:0.5em;
  margin:0.5em;
  border-radius:0.5em;
}

.clickable {
    cursor: pointer;
}

.panel-scoring {
    .panel-score-left, 
    .panel-score-right {
        &.is-compressed-to-nil {
            display:none;
        }
    }
}