import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ViewTtDashboardComponent } from './view-tt-dashboard/view-tt-dashboard.component';
import { ViewTtSessionBookerComponent } from './view-tt-session-booker/view-tt-session-booker.component';
import { ViewTtSessionInviteComponent } from './view-tt-session-invite/view-tt-session-invite.component';
import { ViewTtTestRunnerComponent } from './view-tt-test-runner/view-tt-test-runner.component';
import { ViewTtReportComponent } from './view-tt-report/view-tt-report.component';
import {ViewTtShowCamComponent} from './view-tt-show-cam/view-tt-show-cam.component';
import { ViewTtCreateAccountComponent } from './view-tt-create-account/view-tt-create-account.component';
import { ViewTtFileAppealComponent } from './view-tt-file-appeal/view-tt-file-appeal.component';
import { ViewConstrRespComponent } from '../ui-testrunner/view-constr-resp/view-constr-resp.component';

const routes: Routes = [
  { path: `dashboard`, component: ViewTtDashboardComponent },
  { path: `book-session`, component: ViewTtSessionBookerComponent, pathMatch: 'full' },
  { path: `book-session/accomm/:accommRequirement`, component: ViewTtSessionBookerComponent },
  { path: `book-session/invite/:sessionId/:secretKey`, component: ViewTtSessionInviteComponent },
  { path: `test-runner/:sessionId`, component: ViewTtTestRunnerComponent },
  // { path: `print-report/:attemptId`, component: ViewTtReportComponent },
  { path: `monitor/:sessionId`, component: ViewTtShowCamComponent },
  { path: `create-account`, component: ViewTtCreateAccountComponent },
  { path: `file-appeal`, component: ViewTtFileAppealComponent },
  { path: `file-appeal/:appealType/:recordId`, component: ViewTtFileAppealComponent },
  { path: `constr-resp`, component: ViewConstrRespComponent },
  { path: `constr-resp/:variant`, component: ViewConstrRespComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UiTestTakerRoutingModule { }
