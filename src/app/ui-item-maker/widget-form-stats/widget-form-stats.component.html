<div class="form-stats-container">

   <mode-toggle 
    [itemBankCtrl]="itemBankCtrl" 
    [saveLoadCtrl]="saveLoadCtrl" 
    [frameworkCtrl]="frameworkCtrl"
  ></mode-toggle>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-indicator">
    <i class="fa fa-spinner fa-spin"></i> Loading stats...
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="error-message">
    {{error}}
  </div>

  <!-- Stats Display -->
  <div *ngIf="statsData && !isLoading" class="stats-content">
    <!-- Meta Information -->
    <div class="meta-section">
      <div style="font-size: 1.2em">
        Item Analysis for {{getAssessmentName()}}
      </div>
      <ng-container *ngIf="hasDataLoaded()">
        <div>
          Course: {{getFormInfo('course_name_full')}}
        </div>
        <div>
          Job  <code>{{statsData.export_id}}</code> Generated on: {{formatDate(statsData.created_on)}} - 
        </div>
      </ng-container>
    </div>


    <h3>Overview</h3>
    <div class="info-panel-container">
      <div class="info-panel" style="flex-grow:1">
        <div class="panel-header">Form Overview</div>
        <div>
          <h4>Assessment Design</h4>
          <table>
            <tr>
              <td><strong>Form</strong></td>
              <td>{{getFormInfo('form_code')}}</td>
            </tr>
            <tr>
              <td><strong>No. of Alternatives</strong></td>
              <td>{{getNumberOfForms()}}</td>
            </tr>
            <tr>
              <td><strong>Number of Items</strong></td>
              <td>{{getFormInfo('num_questions')}}</td>
            </tr>
          </table>

          <h4 style="margin-top: 1em;">Administration Overview</h4>
          <table>
            <tr>
              <td><strong>Number of Examinees</strong></td>
              <td>{{getFormInfo('num_attempts')}}</td>
            </tr>
            <tr>
              <td><strong>Mean in Percent</strong></td>
              <td>{{roundStatValue(getFormInfo('mean_in_percent'))}}</td>
            </tr>
            <tr>
              <td><strong>Std. Dev. in Percent</strong></td>
              <td>{{roundStatValue(getFormInfo('std_dev_percent'))}}</td>
            </tr>
            <tr>
              <td><strong>Mean Score</strong></td>
              <td>{{roundStatValue(getFormInfo('mean'))}}</td>
            </tr>
            <tr>
              <td><strong>Cutoff - High</strong></td>
              <td>{{getFormInfo('cutoff_high')}}</td>
            </tr>
            <tr>
              <td><strong>Cutoff - Low</strong></td>
              <td>{{getFormInfo('cutoff_low')}}</td>
            </tr>
            <tr>
              <td><strong>Variance</strong></td>
              <td>{{roundStatValue(getFormInfo('variance'))}}</td>
            </tr>
            <tr>
              <td><strong>Standard Deviation</strong></td>
              <td>{{roundStatValue(getFormInfo('std_dev'))}}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="info-panel" style="flex-grow:1">
        <div class="panel-header">Frequency Distribution of Total Scores</div>
        <table>
          <tr>
            <th>Score</th>
            <th>Frequency</th>
            <th>Freq. %</th>
            <th>Cum. %</th>
          </tr>
          <tr *ngFor="let scoreFreqCat of scoreFreq">
            <td>{{scoreFreqCat.score}}</td>
            <td>
              <span >{{scoreFreqCat.freq}}</span>
            </td>
            <td>
              <span class="num-prog-companion">{{renderPerc(scoreFreqCat.freqPerc)}}</span>
              <progress
                [value]="scoreFreqCat.freqPerc" 
                [max]="maxFreqPerc" 
                style="width:4em"
              ></progress>
            </td>
            <td>
              <span class="num-prog-companion">{{renderPerc(scoreFreqCat.freqPercCumul)}}</span>
              <progress
                [value]="scoreFreqCat.freqPercCumul" 
                [max]="1" 
                style="width:4em"
              ></progress>
            </td>
          </tr>
        </table>
      </div>
      <div class="info-panel" style="max-width:30%; flex-grow:1">
        <div class="panel-header">Glossary</div>
        <table>
          <tr>
            <td>Dif</td>
            <td>Difficulty - The proportion of examinees answering the item correctly</td>
          </tr>
          <tr>
            <td>RPB</td>
            <td>Point-BiSerial Correlation between Item and Total Test Score</td>
          </tr>
          <tr>
            <td>CRPB</td>
            <td>Corrected RPB - RPB between Item and Total Score not including the present item</td>
          </tr>
          <tr>
            <td>IRI</td>
            <td>Item Reliability Index - RPB times the square root of Dif times 1-Dif</td>
          </tr>
          <tr>
            <td>N</td>
            <td>Number of Examinees</td>
          </tr>
          <tr>
            <td>NR</td>
            <td>Number of Examinees NOT responding to this item</td>
          </tr>
          <tr>
            <td>NF</td>
            <td>Number of Examinees NOT finishing the test. Examinees did NOT respond to this item or subsequent items. This is a running total and may include examinees who quit the test on an earlier item.</td>
          </tr>
          <tr>
            <td>Omit</td>
            <td>Number of Examinees skipping this item but answering at least one item after it</td>
          </tr>
          <tr>
            <td>High</td>
            <td>Approximately 27.000% of the Total Group Scoring</td>
          </tr>
          <tr>
            <td>Low</td>
            <td>Same proportion as High having the lowest scores</td>
          </tr>
        </table>
      </div>
    </div>

    <ng-container *ngIf="itemsStats" style="margin-top: 2em;">
      <div style="margin-top: 2em;">
        <h3>
          Items
          <span class="tag" *ngIf="saveLoadCtrl.isLoading">
            Loading...
          </span>
        </h3>
      </div>
      <div class="info-panel-container">
        <div class="info-panel">
          <table>
            <tr>
              <th></th>
              <th>#</th>
              <th>Item ID</th>
              <th>Dif.</th>
              <th>Crpb.</th>
            </tr>
            <ng-container *ngFor="let item of itemsStats">
              <tr (click)="selectItem(item)" class="item-row" [class.is-selected]="isSelectedItem(item)">
                <td>
                  <input type="radio" [checked]="isSelectedItem(item)" [disabled]="true">
                </td>
                <td>
                  <code>{{item.item_nbr}}</code>
                </td>
                <td>
                  <span class="tag" [class.is-info]="isSelectedItem(item)">{{item.item_id}}</span>
                </td>
                <td>
                  <div class="stat-num" [class.is-bad-stat]="(item.p < 0.3)" [class.is-warn-stat]="(item.p > 0.85)" >
                    {{roundStatValue(item.p)}}
                  </div>
                  <div>
                    <progress
                      [value]="item.p" 
                      [max]="1" 
                      style="width:4em"
                    ></progress>
                  </div>
                </td>
                <td>
                  <div class="stat-num" [class.is-bad-stat]="item.crpb < 0.2" >
                    {{roundStatValue(item.crpb)}}
                  </div>
                </td>
                <!-- <td>
                  <code>{{item.crpb}}</code>
                </td>
                <td>
                  <code>{{item.iri}}</code>
                </td> -->
              </tr>
            </ng-container>
          </table>
        </div>
        <div class="info-panel" style="flex-grow: 1; min-height: 80vh;">
          <div *ngIf="!isAnySelectedItem()" style="padding:6em;">
            <ng-container *ngIf="saveLoadCtrl.isLoading">
              Loading items...
            </ng-container>
            <ng-container *ngIf="!saveLoadCtrl.isLoading">
              Select an item to view it's content, comments, response distributions, and other statistics.
            </ng-container>
          </div>
          <ng-container *ngIf="isAnySelectedItem()">
            <div style="padding: 0.5em;">
              <button class="button is-small" [class.is-info]="isItemModeSelected('ITEM')" (click)="toggleSelectItemMode('ITEM')">
                Item Content
              </button>
              <button class="button is-small" [class.is-info]="isItemModeSelected('RESPONSES')" (click)="toggleSelectItemMode('RESPONSES')">
                Stats & Response List
              </button>
              <button class="button is-small" [class.is-info]="isItemModeSelected('COMMENTS')" (click)="toggleSelectItemMode('COMMENTS')">
                Comments ({{getCurrentItemComments().length}})
              </button>
            </div>
            <div class="item-split-container" style="display:flex; flex-direction:row;">
              <div *ngIf="isItemModeSelected('ITEM')" class="item-split is-content">
                <question-runner style="margin-left: 1em"
                  [currentQuestion]="getCurrentQuestionContent(true)"
                  [questionState]="{}">
                </question-runner>
              </div>
              <div *ngIf="isItemModeSelected('RESPONSES')" class="item-split is-stats">
                <div class="space-between">
                  <div class="tags">
                    <span class="tag is-dark">Item ID: {{getCurrentQuestionContent().id}}</span>
                    <span class="tag ">Item Label: {{getCurrentQuestionContent().label}}</span>
                  </div>
                  <span>
                    N
                    <mat-slide-toggle [(ngModel)]="statsEmphasizePerc" ></mat-slide-toggle>
                    %
                  </span>
                </div>
                <div>
                  <table class="quick-stats-table">
                    <tr>
                      <td class="is-header">N</td>
                      <td class="is-header">NR</td>
                      <td class="is-header">NF</td>
                      <td class="is-header">Omit</td>
                    </tr>
                    <tr>
                      <td>{{getCurrentItemStat('n_total')}}</td>
                      <td>{{getCurrentItemStat('NR')}}</td>
                      <td>{{getCurrentItemStat('NF')}}</td>
                      <td>{{getCurrentItemStat('omit')}}</td>
                    </tr>
                  </table>
                </div>
                <div>
                  <table class="quick-stats-table">
                    <tr>
                      <td class="is-header">RPB</td>
                      <td class="is-header">CRPB</td>
                      <td class="is-header">IRI</td>
                    </tr>
                    <tr>
                      <td>{{roundStatValue(getCurrentItemStat('rpb'))}}</td>
                      <td>{{roundStatValue(getCurrentItemStat('crpb'))}}</td>
                      <td>{{roundStatValue(getCurrentItemStat('iri'))}}</td>
                    </tr>
                  </table>
                </div>
                <div>
                  <div class="progress-chunk-center" style="margin-bottom: 1em;">
                    <div>
                      <strong>Mean Score</strong>: {{renderPerc( getCurrentItemStat('mean_score') )}}
                    </div>
                    <progress
                      [value]="getCurrentItemStat('mean_score')" 
                      style="width: 12em;"
                      [max]="1" 
                    ></progress>
                  </div>
                  <div class="space-between" style="justify-content: space-evenly;">
                    <div class="progress-chunk-center">
                      <div>
                        <span class="tag is-success">
                          High:&nbsp;
                          <span *ngIf="!statsEmphasizePerc">
                            {{getCurrentItemStat('n_high')}}
                          </span>
                          <span *ngIf="statsEmphasizePerc">
                            {{renderPerc(getCurrentItemStat('p_high'))}}
                          </span>
                        </span>
                      </div>
                      <progress
                        [value]="getCurrentItemStat('p_high')" 
                        style="width: 4em;"
                        [max]="1" 
                      ></progress>
                    </div>
                    <div class="progress-chunk-center">
                      <div>
                        <span class="tag is-warning">
                          Mid:&nbsp;
                          <span *ngIf="!statsEmphasizePerc">
                            {{getCurrentItemStat('n_mid')}}
                          </span>
                          <span *ngIf="statsEmphasizePerc">
                            {{renderPerc(getCurrentItemStat('p_mid'))}}
                          </span>
                        </span>
                      </div>
                      <progress
                        [value]="getCurrentItemStat('p_mid')" 
                        style="width: 4em;"
                        [max]="1" 
                      ></progress>
                    </div>
                    <div class="progress-chunk-center">
                      <div>
                        <span class="tag is-danger">
                          Low:&nbsp;
                          <span *ngIf="!statsEmphasizePerc">
                            {{getCurrentItemStat('n_low')}}
                          </span>
                          <span *ngIf="statsEmphasizePerc">
                            {{renderPerc(getCurrentItemStat('p_low'))}}
                          </span>
                        </span>
                      </div>
                      <progress
                        [value]="getCurrentItemStat('p_low')" 
                        style="width: 4em;"
                        [max]="1" 
                      ></progress>
                    </div>
                    
                  </div>
                </div>
                <div style="margin-top:2em;">
                  <h4>Response List</h4>
                  <div class="space-between" style="padding:0.5em; border-radius: 0.3em; background: #f1f1f1; font-size:0.8em;;">
                    <strong>Sort by</strong>
                    <div>
                      <button 
                        *ngFor="let sortOption of sortOptions"
                        class="button is-small" 
                        (click)="setSortProp(sortOption.prop)"
                        [class.is-info]="isSortProp(sortOption.prop)"
                      >
                        {{sortOption.caption}}
                        <span class="icon" *ngIf="isSortProp(sortOption.prop)" style="margin-left:1em;">
                          <i *ngIf="currentSortDir == 'asc'" class="fas fa-sort-up"></i>
                          <i *ngIf="currentSortDir == 'desc'" class="fas fa-sort-down"></i>
                        </span>
                      </button>
                    </div>
                  </div>
                  <div>
                    <em>You can click on a response-score to override it.</em>
                    <table>
                      <tr>
                        <th>Score</th>
                        <th>Fmt. Response</th>
                        <th>N</th>
                      </tr>
                      <tr 
                        *ngFor="let response of getCurrentItemResponses()" 
                        class="response-row" 
                        [class.is-correct]="isResponseCorrect(response)"
                      >
                        <td>
                          <div *ngIf="!getScoreOverride(response)">
                            <button 
                              style="
                                font-size:1.4em;     
                                background-color: #fff;
                                white-space: nowrap;
                                padding: 0.5em;"
                              (click)="patchScore(response)"
                            >
                              {{response.score}} / {{response.score_max}}
                            </button>
                          </div>
                          <div *ngIf="getScoreOverride(response)">
                            <div>
                              <span class="is-strikethrough">
                                {{response.score}} / {{response.score_max}}
                              </span>
                            </div>
                            <div>
                              <span class="tag is-danger is-large">
                                {{getScoreOverrideScore(response)}} / {{response.score_max}}
                              </span>
                            </div>
                            <div>
                              <button (click)="cancelOverride(response)">Cancel Override</button>
                            </div>
                          </div>
                        </td>
                        <td>
                          <span class="tag is-info is-light is-large">
                            {{response.formatted_response}}
                          </span>
                        </td>
                        <td>
                          <span class="num-prog-companion" style="font-size:1.2em;">
                            <span *ngIf="!statsEmphasizePerc">
                              {{response.n_total}}
                            </span>
                            <span *ngIf="statsEmphasizePerc">
                              {{renderPerc(response.n_total / getCurrentItemStat('n_total'))}}
                            </span>
                          </span>
                          <progress
                            [value]="response.total" 
                            style="width: 4em;"
                            [max]="1" 
                          ></progress>
                          <div class="high-mid-low-dot-heat">
                            <span style="margin-right:1em;">
                              <i class="fa fa-circle" style="color:#48c774; margin-right:0.5em;" [style.opacity]="response.high"></i>
                              H:&nbsp;
                              <span *ngIf="!statsEmphasizePerc">
                                {{response.n_high}}
                              </span>
                              <span *ngIf="statsEmphasizePerc">
                                {{renderPerc(response.high)}}
                              </span>
                            </span>
                            <span style="margin-right:1em;">
                              <i class="fa fa-circle" style="color:#ffdd57; margin-right:0.5em;" [style.opacity]="response.mid"></i>
                              M:&nbsp;
                              <span *ngIf="!statsEmphasizePerc">
                                {{response.n_mid}}
                              </span>
                              <span *ngIf="statsEmphasizePerc">
                                {{renderPerc(response.mid)}}
                              </span>
                            </span>
                            <span style="margin-right:1em;">
                              <i class="fa fa-circle" style="color:#f14668; margin-right:0.5em;" [style.opacity]="response.low"></i>
                              L:&nbsp;
                              <span *ngIf="!statsEmphasizePerc">
                                {{response.n_low}}
                              </span>
                              <span *ngIf="statsEmphasizePerc">
                                {{renderPerc(response.low)}}
                              </span>
                            </span>
                          </div>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div *ngIf="isItemModeSelected('COMMENTS')" class="item-split is-comments">
                Teacher Comments
                <ul>
                  <li *ngFor="let comment of getCurrentItemComments()">{{comment.text}}</li>
                </ul>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </ng-container>



    <!-- Download Links -->
    <div class="downloads-section" style="margin-top: 5em;">
      <h3>Download Report Data Files</h3>
      <div *ngFor="let asset of statsData.urlsSigned">
        <a 
            (click)="downloadStatsReport(asset)" 
            class="button is-small"
        >
          <i class="fa fa-download"></i> 
          {{asset.caption}}
        </a>
      </div>
      

    <!-- Regenerate Stats Button -->
    <div class="actions-section">
      <button 
        class="button is-small"
        [disabled]="isRegenerating"
        (click)="regenerateStats()"
      >
        <i class="fa fa-refresh"></i> 
        Regenerate Statistics
        <span *ngIf="isRegenerating" class="is-pulled-right">
          <i class="fa fa-spinner fa-spin"></i>
        </span>
      </button>
    </div>
  </div>
</div> 