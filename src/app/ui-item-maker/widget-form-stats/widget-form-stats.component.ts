import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { LangService } from '../../core/lang.service';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { downloadFile } from 'src/app/core/download-string';
import { mtz } from 'src/app/core/util/moment';
import * as _ from 'lodash';
import { RoutesService } from 'src/app/api/routes.service';

interface IStatsAsset {
  slug: string;
  signedUrl: string;
  caption: string;
}

@Component({
  selector: 'widget-form-stats',
  templateUrl: './widget-form-stats.component.html',
  styleUrls: ['./widget-form-stats.component.scss']
})
export class WidgetFormStatsComponent implements OnInit {
  @Input() itemBankCtrl: ItemBankCtrl;
  @Input() saveLoadCtrl: ItemBankSaveLoadCtrl;
  @Input() frameworkCtrl: ItemSetFrameworkCtrl;
  // @Output() selectItemId: EventEmitter<number> = new EventEmitter<number>();

  statsData: any = null;
  isLoading: boolean = false;
  error: string | null = null;
  isRegenerating: boolean = false;
  
  statsEmphasizePerc: boolean = false;
  

  constructor(
    private auth: AuthService,
    private lang: LangService,
    private routes:RoutesService,
    private loginGuard: LoginGuardService
  ) {}

  ngOnInit() {
    this.loadStats();
  }

  async loadStats(export_id?:number) {
    if (!this.itemBankCtrl.testWindowAllocRuleId) {
      this.error = 'No test window allocation rule ID found';
      return;
    }

    try {
      this.isLoading = true;
      this.error = null;
      
      // Call the stats endpoint with the twtar_id and is_full=true to get complete stats
      this.statsData = await this.auth.apiGet(
        'public/test-auth/twtar/stats',
        this.itemBankCtrl.testWindowAllocRuleId,
        { query: { is_full: true } }
      );
      console.log('this.statsData', this.statsData)
      this.packageItemStats()
    } catch (err) {
      this.error = 'Failed to load statistics: ' + (err.message || 'Unknown error');
      console.error('Stats loading error:', err);
    } finally {
      this.isLoading = false;
    }

    if (export_id){
      if (this.statsData.export_id == export_id){
        this.isRegenerating = false;
        this.loginGuard.quickPopup('Statistics regeneration complete');
      }
      else {
        this.loginGuard.quickPopup(`Statistics regeneration (${export_id}) in progress...`);
      }
    }
  }

  activeForm;
  itemsStats;
  itemsStatsRef = new Map();
  scoreFreq;
  maxFreqPerc = 1
  uniqueSitems = new Set() // todo: shouldnt really need this
  packageItemStats(){

    const form_code = this.statsData?.form_code
    this.activeForm = (this.statsData?.data?.forms || []).find(form => form.form_code == form_code);

    if (!this.activeForm) {
      return;
    }

    this.itemsStatsRef = new Map()
    let items = this.statsData?.data?.items || [];
    items = items.filter(item => item.form_code == form_code &&  item.lang == this.lang.c() );
    items = _.orderBy(items, ['item_nbr']);
    this.itemsStats = items;

    this.uniqueSitems = new Set()
    for (let item of items){
      item.responses = [];
      item.scoreOverrides = {};
      this.itemsStatsRef.set(item.item_id, item)
      this.uniqueSitems.add(item.item_id);
    }

    // todo: this should come from a lakehouse asset
    let scoreCats:any[] = []
    const scoreCatRef:any = {}
    let attempts = this.statsData?.data?.total_scores || [];
    attempts = attempts.filter(attempt => attempt.form_code == form_code);
    for (let attempt of attempts){
      const key = ''+attempt.total_score
      if (!scoreCatRef[key]){
        scoreCatRef[key] = {
          score: attempt.total_score,
          freq: 0,
        }
        scoreCats.push(scoreCatRef[key]);
      }
      scoreCatRef[key].freq ++
    }
    scoreCats = _.orderBy(scoreCats, ['score'], ['desc']);
    let nTotal = attempts.length
    let freqPercCumul = 0;
    let maxFreqPerc = 0
    for (let scoreCat of scoreCats){
      const freqPerc = scoreCat.freq / nTotal;
      freqPercCumul += freqPerc;
      scoreCat.freqPerc = freqPerc;
      scoreCat.freqPercCumul = freqPercCumul;
      maxFreqPerc = Math.max(maxFreqPerc, freqPerc)
    }
    this.maxFreqPerc = maxFreqPerc || 1
    this.scoreFreq = scoreCats;

    const responses = (this.statsData?.data?.responses || []).filter(response => response.form_code == form_code); 
    for (let response of responses){
      const item = this.itemsStatsRef.get(response.item_id)
      item.responses.push(response)
    }

    // todo: load current overrides 

  }

  
  async loadScoreOverrides(){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    const item = this.itemsStatsRef.get(item_id)
    if (item){
      const scoreOverrides:any = {};
      let expectedAnswerRecords = await this.auth.apiFind('public/student/extract-item-response', {query:{test_question_id: item_id}})
      for (let record of expectedAnswerRecords){
        if (record.lang == this.lang.c()){
          scoreOverrides[record.formatted_response] = record;
        }
      }
      item.scoreOverrides = scoreOverrides;
    }
  }
  getScoreOverride(response){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    const item = this.itemsStatsRef.get(item_id)
    if (item){
      const expectedAnswerRecord = item.scoreOverrides[response.formatted_response]
      if (expectedAnswerRecord?.is_item_score_exceptions == 1){
        return expectedAnswerRecord
      }
    }
  }
  async patchScore(response){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    const item = this.itemsStatsRef.get(item_id)
    const score_override = prompt('Please provide a score override')
    if (item && item_id && score_override){
      const expectedAnswerRecord = item.scoreOverrides[response.formatted_response]
      if (!expectedAnswerRecord){
        await this.auth.apiCreate(`public/student/extract-item-response`,{
          "response_raw":"{}", // todo: get it from the sample TAQR (when available)
          "test_question_id":item_id,
          "item_set_id": this.itemBankCtrl.customTaskSetId,
          "lang": this.lang.c(),
          "create_expected_response":true,
          preProcessedResponse: {
            formatted_response: response.formatted_response,
            score: response.score,
            weight: response.score_max,
          }
        })
        item.scoreOverrides[response.formatted_response] = {
          is_item_score_exceptions: 0
        }
      }
      await this.auth.apiPatch(`public/student/extract-item-response`,item_id, {
        formatted_response: response.formatted_response,
        is_item_score_exceptions: 1,
        lang: this.lang.c(),
        patchMode: "SCORE_OVERRIDE",
        score_override,
      })
      const expectedAnswer = item.scoreOverrides[response.formatted_response]; // todo: risk if switching languages in between?
      expectedAnswer.is_item_score_exceptions = 1
      expectedAnswer.score_override = score_override
    }
  }
  async cancelOverride(response){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    const item = this.itemsStatsRef.get(item_id)
    const isCertain = confirm('Are you sure you want to cancel this score override?')
    if (item && item_id && isCertain){
      await this.auth.apiPatch(`public/student/extract-item-response`,item_id, {
        formatted_response: response.formatted_response,
        is_item_score_exceptions: 0,
        lang: this.lang.c(),
        patchMode: "SCORE_OVERRIDE",
        score_override: -1,
      })
      const expectedAnswer = item.scoreOverrides[response.formatted_response]; // todo: risk if switching languages in between?
      expectedAnswer.is_item_score_exceptions = 0
    }
  }
  getScoreOverrideScore(response){
    const override = this.getScoreOverride(response);
    return override.score_override
  }
  isResponseCorrect(response){
    // todo: consider overrides
    let score = response.score;
    const override = this.getScoreOverride(response);
    if (override){
      score = override.score_override
    }
    if (score >= response.score_max){
      return true
    }
  }


  itemComments = new Map()
  async loadCurrentItemComments(){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    if (item_id){
      const {notes} = await this.auth.apiFind(this.routes.TEST_AUTH_NOTES, {query: {
        item_id,
        itemType: 'teacher', // todo: centralize constant
      }})
      this.itemComments.set(item_id, notes)
    }
  }
  getCurrentItemComments(){
    const item_id = this.itemBankCtrl.currentQuestion?.id
    if (item_id){
      const notes = this.itemComments.get(item_id)
      if (notes){
        return notes;
      }
    }
    return []
  }

  renderPerc(value: number): string {
   return (100*value || 0).toFixed(2) + '%'; 
  }
  roundStatValue(value: number): string {
   return (value || 0).toFixed(3); 
  }

  itemSelectModes:any = {
    ITEM: true,
    RESPONSES: true,
    COMMENTS: false,
  };
  itemCommentsRef = new Map(); // fetch by item id
  tqerRef = new Map(); // fetch by item id
  toggleSelectItemMode(slug:string){
    this.itemSelectModes[slug] = !this.itemSelectModes[slug];
  }
  isItemModeSelected(slug:string){
    return this.itemSelectModes[slug] || false;
  }

  isAnySelectedItem(){
    if (this.uniqueSitems.has(this.itemBankCtrl?.currentQuestion?.id)){
      return true;
    }
  }
  isSelectedItem(item:any){
    return this.itemBankCtrl.currentQuestion?.id == item.item_id;
  }
  getCurrentQuestionContent(isLangConsidered = false){
    const itemConfig = this.itemBankCtrl.currentQuestion;
    if (isLangConsidered && this.lang.c() == 'fr'){
      return itemConfig.langLink
    }
    return itemConfig
  }

  async selectItem(item:any){
    const item_id = item.item_id
    await this.itemBankCtrl.selectQuestionById(item_id);
    this.sortCurrentItemResponses()
    this.loadScoreOverrides()
    this.loadCurrentItemComments()
  }
  

  getCurrentItemResponses(){
    return this.getCurrentItemStat('responses')||[]
  }
  getCurrentItemStat(prop:string){
    const item = this.itemsStatsRef.get(this.itemBankCtrl.currentQuestion?.id)
    if (item){
      return item[prop]
    }
  }

  currentSort = 'score'
  currentSortDir = 'desc'
  sortOptions = [
    {caption:'Score', prop: 'score'},
    {caption:'Response', prop: 'formatted_response'},
    {caption:'Freq', prop: 'n_total'},
  ]
  isSortProp(prop:string){
    return this.currentSort == prop
  }
  setSortProp(prop:string){
    if (this.currentSort == prop){
      if (this.currentSortDir == 'asc'){
        this.currentSortDir = 'desc'
      }
      else {
        this.currentSortDir = 'asc'
      }
    }
    else {
      this.currentSort = prop
    }
    this.sortCurrentItemResponses()
  }
  sortCurrentItemResponses(){
    const item = this.itemsStatsRef.get(this.itemBankCtrl.currentQuestion?.id)
    if (item){
      item.responses = _.orderBy(item.responses, [this.currentSort], [this.currentSortDir])
    }
  }

  async regenerateStats() {
    if (!this.itemBankCtrl.testWindowAllocRuleId) {
      this.error = 'No test window allocation rule ID found';
      return;
    }

    try {
      this.isLoading = true;
      this.error = null;

      // Create new stats generation job
      const response = await this.auth.apiCreate('public/test-auth/twtar/stats', {
        twtar_id: this.itemBankCtrl.testWindowAllocRuleId,
        test_window_id: null,
        type_slug: this.itemBankCtrl.currentSetSlug
      });

      // Show estimated completion time if available
      if (response.estimated_completion_time) {
        this.error = `Stats regeneration started. Estimated completion time: ${response.estimated_completion_time} minutes`;
      } else {
        this.error = 'Stats regeneration started. Please check back in a few minutes.';
      }

      const export_id = response.export_id; // this is the export id of the job that was created
      this.delayedRecheckFormStats(export_id, response.estimated_completion_time);
      
      // Reload stats after a delay to show new data
    } catch (err) {
      this.error = 'Failed to regenerate statistics: ' + (err.message || 'Unknown error');
      console.error('Stats regeneration error:', err);
    } finally {
      this.isLoading = false;
    }
  }

  delayedRecheckFormStats(export_id:number, estimated_completion_time_m?:number){
    const recheckMinutes = estimated_completion_time_m || 5;
    const recheckMs = recheckMinutes * 60 * 1000;
    this.isRegenerating = true;
    setTimeout(() => {
      this.loadStats(export_id);
    }, recheckMs);
  }


  getNumAttempts(): number {
    if (!this.statsData?.data?.forms) {
      return 0;
    }
    return this.statsData.data.forms.reduce((sum, form) => sum + (form.num_attempts || 0), 0);
  }

  getNumberOfForms(){
    return this.statsData?.data?.forms?.length
  }

  getAssessmentName() {
    const assessmentCode = this.getFormInfo('type_slug');
    if (assessmentCode) {
      return assessmentCode
    }
    return '~ '+ this.frameworkCtrl.getAsmtFmrkName();
  }

  hasDataLoaded(){
    return !!this.statsData?.data?.forms?.length;
  }

  getFormInfo(prop:string){
    return this.activeForm?.[prop] || '';
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleString();
  }

  getPropFromAssetSlug(assetSlug:string){
    switch(assetSlug){
      case 'trfm_form_stats':                   return 'forms';
      case 'trfm_item_scale_stats':             return 'items'; 
      case 'trfm_item_response_value_stats':    return 'responses';
    }

  }

  async downloadStatsReport(asset: IStatsAsset) {
    const records = this.statsData.data[this.getPropFromAssetSlug(asset.slug)];
    if (!records) {
      this.loginGuard.quickPopup('No statistics data available to download');
      return;
    }
    const timestamp = mtz().format('YYYY-MM-DD-HH-mm-ss');
    const filename = `stats-report-${this.getAssessmentName()}-${asset.slug}-${timestamp}`;
    const res = await this.auth.jsonToExcel(records, filename);
    downloadFile(res.url);
  }

} 