.form-stats-container {
  padding: 1rem;

  .loading-indicator {
    text-align: center;
    padding: 2rem;
    color: #666;
  }

  .error-message {
    color: #dc3545;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8d7da;
    border-radius: 4px;
  }

  .stats-content {
    .meta-section {
      margin-bottom: 2rem;
      text-align:center;

      h3 {
        margin-bottom: 1rem;
        font-size: 1.2rem;
        font-weight: 600;
      }

      table {
        width: 100%;
        border-collapse: collapse;

        td {
          padding: 0.5rem;
          border-bottom: 1px solid #eee;

          &:first-child {
            font-weight: 500;
            width: 40%;
          }
        }
      }
    }

    .downloads-section {
      margin-bottom: 2rem;

      h3 {
        margin-bottom: 1rem;
        font-size: 1.2rem;
        font-weight: 600;
      }

      .button {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;

        i {
          margin-right: 0.5rem;
        }
      }
    }

    .actions-section {
      text-align: center;
      margin-top: 2rem;

      .button {
        i {
          margin-right: 0.5rem;
        }
      }
    }
  }
} 

.stats-container {
  background-color: #f1f1f1;
}
.info-panel-container {
  display: flex;
  flex-direction: row;
  justify-content: stretch;
  align-items: flex-start;
  gap: 0.5em;
  .info-panel {
    border-radius: 0.5em;
    position:relative;
    box-shadow: 0 0em 0.5em rgba(0, 0, 0, 0.2);
    background-color: #fff;
    max-height: 80vh; 
    overflow-y: auto;
    .panel-header {
      font-weight: bold;
      padding: 0.5em;
    }
  }
}

tr.item-row {
  cursor: pointer;
  &:hover {
    td {
      background-color: #f9f9f9;
    }
  }
}


.num-prog-companion {
  display: inline-block;
  width: 5em;
  margin-right:1em;
}
.stat-num {
  // display: block;
  // padding: 0.3em;
  // border-radius:0.3em;
  &.is-bad-stat {
    color: red;
    // background-color: rgb(255, 134, 134);
  }
  &.is-warn-stat {
    color: orange;
    // background-color: rgb(255, 215, 134);
  }
}

.item-split-container {
  position:absolute;
  top: 3em;
  bottom:0em;
  left:0em;
  right:0em;
  .item-split {
    padding: 1em;
    max-height: 80vh;
    border-right: 1px solid #ccc;
    overflow: auto;
    &.is-content {
      width: 40em;
    }
  }
}

.response-row {
  &.is-correct  {
    background-color: #c6ffc6;
  }
}


.progress-chunk-center {
  text-align: center;
}

.quick-stats-table {
  margin-top:0.5em;
  margin-bottom:1.5em;
  td.is-header {
    font-weight: bold; 
    background-color: #dcecff;
  }
}

.high-mid-low-dot-heat {
  white-space: nowrap;
  margin-top: 0.5em;
  font-size: 0.8em;
}

.is-strikethrough {
  text-decoration: line-through;
  color: gray;
  opacity: .6;
}