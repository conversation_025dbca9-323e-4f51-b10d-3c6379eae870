import { Component, Input, OnInit } from '@angular/core';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { mtz } from 'src/app/core/util/moment';
import { LangService } from 'src/app/core/lang.service';
import { COMPARISON_TYPES, ComparisonTypes, getLanguageCaption, ICutScoreProfile, ICutScoreSchema, ICutScoreSchemaConfig, IDomainCutScore, ReportingSubprofileColumns } from '../view-im-reporting-profiles/model/types';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { KeyValue } from '@angular/common';

export enum Modal {
  ITEM_DOMAIN = "ITEM_DOMAIN",
  CUT_SCORES = "CUT_SCORES"
}

const CURRENT_SUBPROFILE_COLUMN = ReportingSubprofileColumns.rp_cut_score_schema;
const SCOPE_TYPES = ['', 'entry_domain', 'overall', 'section_slug']

@Component({
  selector: 'widget-rp-domain-schemas',
  templateUrl: './widget-rp-domain-schemas.component.html',
  styleUrls: ['./widget-rp-domain-schemas.component.scss']
})
export class WidgetRpDomainSchemasComponent implements OnInit {
  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private login: LoginGuardService,
    private lang: LangService,
    private pageModalService: PageModalService
  ) { }

  @Input() reportingProfileId: number;
  @Input() authoringGroupId: number;

  // Modal
  pageModal: PageModalController;
  Modal = Modal;
  currRoute: string;

  ScopeTypes = SCOPE_TYPES;

  ngOnInit(): void {
    this.currRoute = this.routes.TEST_AUTH_RP_CUT_SCORE_SCHEMAS;
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadCutScoreProfiles();
    this.loadselectedProfileCtx();
  }


  generalListCtx: {isLoading: boolean, data: ICutScoreSchema[], isCollapsed: boolean, isSaving: boolean} = {isLoading: false, data: null, isCollapsed: false, isSaving: false};
  selectedProfileCtx: {isLoading: boolean, data: ICutScoreSchema, hasChanges: boolean, isSaving: boolean} = {isSaving:false, isLoading: false, data: null, hasChanges: false};

  onSelectedProfileChange() {
    this.selectedProfileCtx.hasChanges = true;
    console.log(this.selectedProfile, 'new change')
  }

  async loadCutScoreProfiles() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    this.generalListCtx.isLoading = true;
    try {
      this.generalListCtx.data = await this.auth.apiFind(this.currRoute, {
        query: {
          authoring_group_id: this.authoringGroupId,
          reporting_profile_id: this.reportingProfileId
        }
      });
    } finally {
      this.generalListCtx.isLoading = false;
    }

    console.log(this.generalListCtx, 'ctx');
  }

  async loadselectedProfileCtx() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    this.selectedProfileCtx.isLoading = true;
    try {
      this.selectedProfileCtx.data = await this.auth.apiGet(this.currRoute, this.reportingProfileId);
    } finally {
      this.selectedProfileCtx.isLoading = false;
    }
    console.log(this.selectedProfileCtx, 'ctx');
  }

  isAllPanelsBalanced(){
    return !(this.generalListCtx.isCollapsed)
  }

  renderDate(db_date:string){
    return mtz(db_date).format(this.lang.tra('datefmt_timestamp'))
  }

  get selectedProfile() {
    return this.selectedProfileCtx.data ?? null;
  }

  isProfileLatest() {
    if(!this.selectedProfile) {
      return false;
    }

    return this.selectedProfile.latest_id == this.selectedProfile.id;
  }

  addElement(langs: string[]) {
    langs.push('');
    this.onSelectedProfileChange();
  }

  onElementChange(arr: any[], index: number, $event) {
    arr[index] = $event;

    this.onSelectedProfileChange();
  }

  /**
   * For *ngFor input bug
   */
  trackByIndex(index: number, item: any): any {
    return index;
  }

  /**
   * Deletes a specified combination row
   * @param combinationIdx the index of the row to delete
   */
    deleteElement(arr: any[], index: number) {
    if (!arr) return;
    this.login.confirmationReqActivate({
      caption: 'Are you sure you would like to delete this element?',
      confirm: () => {
        arr.splice(index, 1);
        this.onSelectedProfileChange();
      }
    })
  }
  async upgradeReportingProfile() {
    if(!this.reportingProfileId || !this.selectedProfile.latest_id || this.selectedProfile.latest_id == this.selectedProfile.id) {
      return;
    }
    if(this.selectedProfileCtx.hasChanges) {
      this.login.quickPopup('You cannot upgrade versions when you have changes that have not been saved.')
      return;
    }
  
    try {
      this.selectedProfileCtx.isSaving = true;
      await this.auth.apiUpdate(this.currRoute, this.reportingProfileId, {})
      await this.loadselectedProfileCtx();
      await this.loadCutScoreProfiles();
    } catch(err) {
      this.popupError(err)
    }  finally {
      this.selectedProfileCtx.isSaving = false;
    }
  }

  async setSubprofile(subprofile: ICutScoreSchema) {
    if(!this.reportingProfileId) {
      return;
    }

    try {
      const subprofileId = subprofile.id;
      this.generalListCtx.isSaving = true;
      const patchData: any = {}
      patchData[CURRENT_SUBPROFILE_COLUMN] = subprofileId
      await this.auth.apiPatch(this.routes.TEST_AUTH_RP_REPORTING_PROFILES, this.reportingProfileId, patchData);
      
      const selectedSubprofile = this.getSelectedSubprofile();
      if(selectedSubprofile) {
        selectedSubprofile.is_selected = 0;
      }

      subprofile.is_selected = 1;
      await this.loadselectedProfileCtx();
    } catch(err) {
      this.popupError(err)
    } finally {
      this.generalListCtx.isSaving = false;
    }
  }

  getSelectedSubprofile(): ICutScoreSchema | undefined {
    const index = this.generalListCtx.data.findIndex((subprofile) => subprofile.is_selected && subprofile.is_selected == 1);

    if(index < 0) {
      return undefined;
    }

    return this.generalListCtx.data[index];
  }

  async saveChanges() {
    if(!this.reportingProfileId) {
      return;
    }

    try {
      const profileId = this.selectedProfile.id
      const data = this.selectedProfile;
      this.selectedProfileCtx.isSaving = true;
      await this.auth.apiPatch(this.currRoute, this.reportingProfileId, data)
      this.selectedProfileCtx.hasChanges = false;
      await this.loadselectedProfileCtx();
      await this.loadCutScoreProfiles();
    } catch(err) {
      this.popupError(err)
    } finally {
      this.selectedProfileCtx.isSaving = false;
    }
  }

  popupError(err) {
    this.login.quickPopup(`Something went wrong: ${err.message}`);
    console.error(err);
  }

  isOutdated() {
    return this.selectedProfile.latest_id != this.selectedProfile.id;
  }

  closeModal() {
    this.pageModal.closeModal();
  }

  newSubprofileSlug: string = '';
  async createSubprofile() {
    if(!this.authoringGroupId) {
      return;
    }
    if(this.newSubprofileSlug.trim().length < 3) {
      this.login.quickPopup('A profile code must have a length of 3 or more')
      return;
    }

    this.generalListCtx.isSaving = true;
    try {
      const newSubprofile = await this.auth.apiCreate(this.currRoute, {slug: this.newSubprofileSlug.trim(), authoring_group_id: this.authoringGroupId});
      this.generalListCtx.data.push({...newSubprofile, is_selected: 0});
    } catch (err) {
      this.popupError(err)
    } finally {
      this.generalListCtx.isSaving = false;
    }
  }

  async revokeSubprofile(id: number, i: number) {
    const revokeSubprofile = async () => {
      this.generalListCtx.isSaving = true;
      try {
        await this.auth.apiRemove(this.currRoute, id);
        this.generalListCtx.data.splice(i, 1);
      } catch (err) {
        this.popupError(err);
      } finally {
        this.generalListCtx.isSaving = false;
      }
    }
    this.login.confirmationReqActivate({
      caption: 'Are you sure you would like to revoke this subprofile?',
      confirm: () => {
        revokeSubprofile();
      }
    })
  }
}