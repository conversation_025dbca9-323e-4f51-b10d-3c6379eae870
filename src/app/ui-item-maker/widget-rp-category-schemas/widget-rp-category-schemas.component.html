<div *ngIf="generalListCtx.isLoading" class="notification is-light">
    Loading...
</div>
<div 
    style="display: flex; gap: 1em;"
>
    <div 
        *ngIf="!generalListCtx.isLoading"
        class="twtar-panel"
        [class.is-collapsed]="generalListCtx.isCollapsed"
        [class.is-balanced]="!this.generalListCtx.isCollapsed"
    >
        <div *ngIf="!generalListCtx.data || !generalListCtx.data.length">
            No profiles found for this authoring group.
        </div>
        <ng-container *ngIf="generalListCtx.data  && generalListCtx.data.length">
            <div class="panel-header">
                <h4 *ngIf="!generalListCtx.isCollapsed">
                    Select a profile
                </h4>
                <button class="button is-small is-light" (click)="generalListCtx.isCollapsed = !generalListCtx.isCollapsed ">
                    <span class="icon">
                    <i class="fas" [ngClass]="generalListCtx.isCollapsed  ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
                    </span>
                </button>
            </div>
            <div [class.is-hidden]="generalListCtx.isCollapsed " class="panel-content">
                <div 
                    *ngFor="let profile of generalListCtx.data; let i = index;" 
                    class="tw-row"
                    [class.is-selected]="profile.is_selected && profile.is_selected == 1"
                >
                    <div>
                        <div>
                            <strong>{{profile.id}}</strong> | {{profile.slug}}
                        </div>
                        <div>
                            {{profile.created_on}}
                        </div>
                    </div>
                    <div style="display: flex;">
                        <div>
                            <button
                                class="button is-small"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                                (click)="revokeSubprofile(profile.id, i)"
                            >
                                <tra slug="auth_revoke"></tra>
                            </button>
                        </div>
                        <div>
                            <button 
                                class="button is-small"
                                [class.is-info]="profile.is_selected && profile.is_selected == 1"
                                (click)="setSubprofile(profile)"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                            >Select</button>
                        </div>
                    </div>
                </div>
                <div class="flex-row">
                    <input class="input" [(ngModel)]="newSubprofileSlug" [disabled]="generalListCtx.isSaving">
                    <button class="button is-small" (click)="createSubprofile()" [disabled]="newSubprofileSlug.length <= 0 || generalListCtx.isSaving">Create</button>
                </div>
            </div>
        </ng-container>
    </div>
    <div style="flex-grow: 1; overflow: auto;" *ngIf="!selectedProfileCtx.isLoading &&  selectedProfile">
        <div             
            class="twtar-panel"
        >
            <div class="panel-header">
                <h4>
                    Review profile
                </h4>
                <strong>{{renderDate(selectedProfile.created_on)}}</strong>
            </div>
            <div class="panel-content" style="overflow:auto;">
                <div class="space-between">
                    <span class="tag is-dark">{{selectedProfile.slug}}</span>
                    <span class="tag is-success" *ngIf="!isOutdated()">Up to date</span>
                    <span class="tag is-warning" *ngIf="isOutdated()">Outdated</span>
                </div>
                <div class="space-between" style="margin-top: 1em;">
                    <div>
                        Current version: {{selectedProfile.id}}
                    </div>
                    <div class="flex-row">
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="selectedProfileCtx.hasChanges"
                                [disabled]="!selectedProfileCtx.hasChanges"
                                (click)="saveChanges()"
                            ><tra slug="btn_save"></tra></button>
                        </div>
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="isOutdated()"
                                [disabled]="!isOutdated()"
                                (click)="upgradeReportingProfile()"
                            ><tra slug="Upgrade Version"></tra></button>
                        </div>
                    </div>
                </div>
                <div class="flex-row" style="margin-top: 1em;">
                    <div class="card">
                        <strong>Categories</strong>
                        <div class="flex-column indent">
                            <div *ngFor="let category of selectedProfile.config; let i = index; trackBy: trackByIndex" class="space-between separator" style="align-items: flex-start">
                                <div class="flex-column" style="width: 100%;">
                                    <div class="flex-column">
                                        Slug
                                        <div style="width: 6em;">
                                            <input class="input" [(ngModel)]="category.slug" (change)="this.onSelectedProfileChange()">
                                        </div>
                                    </div>
                                    <div class="flex-column">
                                        Caption (EN)
                                        <div>
                                            <input class="input" [(ngModel)]="category.caption.en" (change)="this.onSelectedProfileChange()">
                                        </div>
                                    </div>
                                    <div class="flex-column">
                                        Caption (FR)
                                        <div>
                                            <input class="input" [(ngModel)]="category.caption.fr" (change)="this.onSelectedProfileChange()">
                                        </div>
                                    </div>
                                    <div class="flex-column">
                                        Color
                                        <div style="width: 10em;">
                                            <input class="input" [(ngModel)]="category.color" (change)="this.onSelectedProfileChange()">
                                        </div>
                                    </div>
                                    <div class="flex-column">
                                        Order
                                        <div style="width: 5em;">
                                            <input class="input" type="number" [(ngModel)]="category.order" (change)="this.onSelectedProfileChange()">
                                        </div>
                                    </div>
                                </div>
                                <a class="button" (click)="deleteElement(selectedProfile.config, i)">
                                    <i class="fas fa-trash"  aria-hidden="true"></i>
                                </a> 
                            </div>
                        </div>
                        <button class="button is-primary" (click)="addElement(selectedProfile.config)"><tra slug="btn_add"></tra></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>