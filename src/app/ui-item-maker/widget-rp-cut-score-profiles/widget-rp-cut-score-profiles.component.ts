import { Component, Input, OnInit } from '@angular/core';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { mtz } from 'src/app/core/util/moment';
import { LangService } from 'src/app/core/lang.service';
import { COMPARISON_TYPES, ComparisonTypes, getLanguageCaption, ICutScoreProfile, IDomainCutScore, ReportingSubprofileColumns } from '../view-im-reporting-profiles/model/types';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { KeyValue } from '@angular/common';

export enum Modal {
  ITEM_DOMAIN = "ITEM_DOMAIN",
  CUT_SCORES = "CUT_SCORES"
}

const CURRENT_SUBPROFILE_COLUMN = ReportingSubprofileColumns.rp_cut_score_profile;
@Component({
  selector: 'widget-rp-cut-score-profiles',
  templateUrl: './widget-rp-cut-score-profiles.component.html',
  styleUrls: ['./widget-rp-cut-score-profiles.component.scss']
})
export class WidgetRpCutScoreProfilesComponent implements OnInit {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private login: LoginGuardService,
    private lang: LangService,
    private pageModalService: PageModalService
  ) { }

  @Input() reportingProfileId: number;
  @Input() authoringGroupId: number;

  // Modal
  pageModal: PageModalController;
  Modal = Modal;
  currRoute: string;

  ngOnInit(): void {
    this.currRoute = this.routes.TEST_AUTH_RP_CUT_SCORE_PROFILES;
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadCutScoreProfiles();
    this.loadselectedProfileCtx();
  }


  generalListCtx: {isLoading: boolean, data: ICutScoreProfile[], isCollapsed: boolean, isSaving: boolean} = {isLoading: false, data: null, isCollapsed: false, isSaving: false};
  selectedProfileCtx: {isLoading: boolean, data: ICutScoreProfile, hasChanges: boolean, isSaving: boolean} = {isSaving:false, isLoading: false, data: null, hasChanges: false};

  onSelectedProfileChange() {
    this.selectedProfileCtx.hasChanges = true;
    console.log(this.selectedProfile, 'new change')
  }

  async loadCutScoreProfiles() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    this.generalListCtx.isLoading = true;
    try {
      this.generalListCtx.data = await this.auth.apiFind(this.currRoute, {
        query: {
          authoring_group_id: this.authoringGroupId,
          reporting_profile_id: this.reportingProfileId
        }
      });
    } finally {
      this.generalListCtx.isLoading = false;
    }

    console.log(this.generalListCtx, 'ctx');
  }

  async loadselectedProfileCtx() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    this.selectedProfileCtx.isLoading = true;
    try {
      this.selectedProfileCtx.data = await this.auth.apiGet(this.currRoute, this.reportingProfileId);
    } finally {
      this.selectedProfileCtx.isLoading = false;
    }
    console.log(this.selectedProfileCtx, 'ctx');
  }

  isAllPanelsBalanced(){
    return !(this.generalListCtx.isCollapsed)
  }

  renderDate(db_date:string){
    return mtz(db_date).format(this.lang.tra('datefmt_timestamp'))
  }

  get selectedProfile() {
    return this.selectedProfileCtx.data ?? null;
  }

  isProfileLatest() {
    if(!this.selectedProfile) {
      return false;
    }

    return this.selectedProfile.latest_id == this.selectedProfile.id;
  }

  getLanguageCaption(langSlug: 'en' | 'fi' | 'fr') {
    return getLanguageCaption(langSlug, this.lang);
  }

  comparisonTypes = ComparisonTypes

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  get cmc() { return this.cModal().config; }

  addItemDomModal(entry_domain: {[domain: string]: IDomainCutScore[]}) {
    const config = {
      message: "Enter a domain name and select where you want the new domain to apply.",
      domainName: "",
      entry_domain,
      error: null
    };
    this.pageModal.newModal({type: Modal.ITEM_DOMAIN, config, finish: async () => {
      this.pageModal.closeModal();
    }, cancel: () => {
    }});
  }

  addItemDomCurrent(entry_domain: {[domain: string]: IDomainCutScore[]}, domainName: string) {
    if(!domainName || !domainName.length) {
      this.cmc.error = "Domain name cannot be empty."
      return;
    }
    if(entry_domain[domainName]) {
      this.cmc.error = "This domain already exists."
      return;
    }
    entry_domain[domainName] = [];
    this.onSelectedProfileChange();
    this.pageModal.closeModal();
  }

  addItemDomCommon(domainName: string) {
    if(!domainName || !domainName.length) {
      this.cmc.error = "Domain name cannot be empty."
      return;
    }

    try {
      for(let lang in this.selectedProfile.config) {
        if(!this.selectedProfile.config[lang].entry_domain[domainName]) {
          this.selectedProfile.config[lang].entry_domain[domainName] = [];
        }
      }
      this.onSelectedProfileChange();
    } finally {
      this.pageModal.closeModal();
    }
  }

  addCutScoreModal(cutScores: IDomainCutScore[]) {
    const config = {
      message: "Select where you want the new cut score to apply.",
      cutScores,
      error: null
    };
    this.pageModal.newModal({type: Modal.CUT_SCORES, config, finish: async () => {
      this.pageModal.closeModal();
    }, cancel: () => {
    }});
  }

  addCutScoreCurrent(cutScores: IDomainCutScore[]) {
    const DEFAULT_CUT_SCORE = {
      slug: '',
      cut_score: null,
      comparison_type: COMPARISON_TYPES.LT
    }
    cutScores.push(DEFAULT_CUT_SCORE);
    this.onSelectedProfileChange();

    this.pageModal.closeModal();
  }

  addCutScoreCommon() {
    const DEFAULT_CUT_SCORE = {
      slug: '',
      cut_score: null,
      comparison_type: COMPARISON_TYPES.LT
    }

    try {
      for(let lang in this.selectedProfile.config) {
        for(let domain in this.selectedProfile.config[lang].entry_domain) {
          this.selectedProfile.config[lang].entry_domain[domain].push(DEFAULT_CUT_SCORE)
        }
      }
      this.onSelectedProfileChange();
    } finally {
      this.pageModal.closeModal();
    }
  }

  /**
 * Deletes a specified combination row
 * @param combinationIdx the index of the row to delete
 */
  deleteCutScore(cutScores: IDomainCutScore[], index: number) {
    if (!cutScores) return;
    this.login.confirmationReqActivate({
      caption: 'Are you sure you would like to delete this cut score?',
      confirm: () => {
        cutScores.splice(index, 1);
        this.onSelectedProfileChange();
      }
    })
  }

  deleteDomain(entry_domain: {[domain: string]: IDomainCutScore[]}, domainName: string) {
    this.login.confirmationReqActivate({
      caption: 'Are you sure you would like to delete this domain?',
      confirm: () => {
        delete entry_domain[domainName];
        this.onSelectedProfileChange();
      }
    })
  }

  keepOriginalOrder = (a: KeyValue<string, any>, b: KeyValue<string, any>): number => {
    return 0; // returning 0 means "leave the order unchanged"
  };

  async upgradeReportingProfile() {
    if(!this.reportingProfileId || !this.selectedProfile.latest_id || this.selectedProfile.latest_id == this.selectedProfile.id) {
      return;
    }
    if(this.selectedProfileCtx.hasChanges) {
      this.login.quickPopup('You cannot upgrade versions when you have changes that have not been saved.')
      return;
    }
 
    try {
      this.selectedProfileCtx.isSaving = true;
      await this.auth.apiUpdate(this.currRoute, this.reportingProfileId, {})
      await this.loadselectedProfileCtx();
      await this.loadCutScoreProfiles();
    } catch(err) {
      this.popupError(err)
    }  finally {
      this.selectedProfileCtx.isSaving = false;
    }
  }

  async setSubprofile(subprofile: ICutScoreProfile) {
    if(!this.reportingProfileId) {
      return;
    }

    try {
      const subprofileId = subprofile.id;
      this.generalListCtx.isSaving = true;
      const patchData: any = {}
      patchData[CURRENT_SUBPROFILE_COLUMN] = subprofileId
      await this.auth.apiPatch(this.routes.TEST_AUTH_RP_REPORTING_PROFILES, this.reportingProfileId, patchData);

      const selectedSubprofile = this.getSelectedSubprofile();
      if(selectedSubprofile) {
        selectedSubprofile.is_selected = 0;
      }

      subprofile.is_selected = 1;
      await this.loadselectedProfileCtx();
    } catch(err) {
      this.popupError(err)
    } finally {
      this.generalListCtx.isSaving = false;
    }
  }

  getSelectedSubprofile(): ICutScoreProfile | undefined {
    const index = this.generalListCtx.data.findIndex((subprofile) => subprofile.is_selected && subprofile.is_selected == 1);

    if(index < 0) {
      return undefined;
    }

    return this.generalListCtx.data[index];
  }

  async saveChanges() {
    if(!this.reportingProfileId) {
      return;
    }

    try {
      const profileId = this.selectedProfile.id
      const data = this.selectedProfile;
      this.selectedProfileCtx.isSaving = true;
      await this.auth.apiPatch(this.currRoute, this.reportingProfileId, data)
      this.selectedProfileCtx.hasChanges = false;
      await this.loadselectedProfileCtx();
      await this.loadCutScoreProfiles();
    } catch(err) {
      this.popupError(err)
    } finally {
      this.selectedProfileCtx.isSaving = false;
    }
  }

  onDomainNameChange(oldKey: string, newKey: string, lang: 'en' | 'fr' | 'fi', inputElement: HTMLInputElement): void {
    if (oldKey !== newKey) {
      if(this.selectedProfile.config[lang].entry_domain[newKey]) {
        inputElement.value = oldKey;
        this.login.quickPopup('This domain already exists.')
        return;
      }

      const value = this.selectedProfile.config[lang].entry_domain[oldKey];
      delete this.selectedProfile.config[lang].entry_domain[oldKey];
      this.selectedProfile.config[lang].entry_domain[newKey] = value;

      this.onSelectedProfileChange();
    }
  }

  popupError(err) {
    this.login.quickPopup(`Something went wrong: ${err.message}`);
    console.error(err);
  }

  isOutdated() {
    return this.selectedProfile.latest_id != this.selectedProfile.id;
  }

  closeModal() {
    this.pageModal.closeModal();
  }

  newSubprofileSlug: string = '';
  async createSubprofile() {
    if(!this.authoringGroupId) {
      return;
    }
    if(this.newSubprofileSlug.trim().length < 3) {
      this.login.quickPopup('A profile code must have a length of 3 or more')
      return;
    }

    this.generalListCtx.isSaving = true;
    try {
      const newSubprofile = await this.auth.apiCreate(this.currRoute, {slug: this.newSubprofileSlug.trim(), authoring_group_id: this.authoringGroupId});
      this.generalListCtx.data.push({...newSubprofile, is_selected: 0});
    } catch (err) {
      this.popupError(err)
    } finally {
      this.generalListCtx.isSaving = false;
    }
  }

  async revokeSubprofile(id: number, i: number) {
    const revokeSubprofile = async () => {
      this.generalListCtx.isSaving = true;
      try {
        await this.auth.apiRemove(this.currRoute, id);
        this.generalListCtx.data.splice(i, 1);
      } catch (err) {
        this.popupError(err);
      } finally {
        this.generalListCtx.isSaving = false;
      }
    }
    this.login.confirmationReqActivate({
      caption: 'Are you sure you would like to revoke this subprofile?',
      confirm: () => {
        revokeSubprofile();
      }
    })
  }
}