@import '../view-im-dashboard/view-im-dashboard.component.scss';
@import '../../../styles/partials/_modal.scss';
@import '../../../styles/partials/_media.scss';
@import '../widget-rp-assessment-structures/widget-rp-assessment-structures.component.scss';

.twtar-panel {
    flex-grow: 0;
}

.is-balanced {
    min-width: 30em !important;
}

.flex-row {
    display: flex;
    gap: 1em;
}

.card {
    flex-grow: 1;
    padding: 1em;
    border-radius: 0.5em;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.separator {
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 1em;
}

.small-icon {
    color: black;
    &:hover {
        color: #555766;
        cursor: pointer;
    }
}