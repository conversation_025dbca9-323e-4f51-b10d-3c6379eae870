<div *ngIf="generalListCtx.isLoading" class="notification is-light">
    Loading...
</div>
<div 
    style="display: flex; gap: 1em;"
>
    <div 
        *ngIf="!generalListCtx.isLoading"
        class="twtar-panel"
        [class.is-collapsed]="generalListCtx.isCollapsed"
        [class.is-balanced]="!this.generalListCtx.isCollapsed"
    >
        <div *ngIf="!generalListCtx.data || !generalListCtx.data.length">
            No profiles found for this authoring group.
        </div>
        <ng-container *ngIf="generalListCtx.data  && generalListCtx.data.length">
            <div class="panel-header">
                <h4 *ngIf="!generalListCtx.isCollapsed">
                    Select a profile
                </h4>
                <button class="button is-small is-light" (click)="generalListCtx.isCollapsed = !generalListCtx.isCollapsed ">
                    <span class="icon">
                    <i class="fas" [ngClass]="generalListCtx.isCollapsed  ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
                    </span>
                </button>
            </div>
            <div [class.is-hidden]="generalListCtx.isCollapsed " class="panel-content">
                <div 
                    *ngFor="let profile of generalListCtx.data; let i = index;" 
                    class="tw-row"
                    [class.is-selected]="profile.is_selected && profile.is_selected == 1"
                >
                    <div>
                        <div>
                            <strong>{{profile.id}}</strong> | {{profile.slug}}
                        </div>
                        <div>
                            {{profile.created_on}}
                        </div>
                    </div>
                    <div style="display: flex;">
                        <div>
                            <button
                                class="button is-small"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                                (click)="revokeSubprofile(profile.id, i)"
                            >
                                <tra slug="auth_revoke"></tra>
                            </button>
                        </div>
                        <div>
                            <button 
                                class="button is-small"
                                [class.is-info]="profile.is_selected && profile.is_selected == 1"
                                (click)="setSubprofile(profile)"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                            >Select</button>
                        </div>
                    </div>
                </div>
                <div class="flex-row">
                    <input class="input" [(ngModel)]="newSubprofileSlug" [disabled]="generalListCtx.isSaving">
                    <button class="button is-small" (click)="createSubprofile()" [disabled]="newSubprofileSlug.length <= 0 || generalListCtx.isSaving">Create</button>
                </div>
            </div>
        </ng-container>
    </div>
    <div style="flex-grow: 1; overflow: auto;" *ngIf="!selectedProfileCtx.isLoading &&  selectedProfile">
        <div             
            class="twtar-panel"
        >
            <div class="panel-header">
                <h4>
                    Review profile
                </h4>
                <strong>{{renderDate(selectedProfile.created_on)}}</strong>
            </div>
            <div class="panel-content" style="overflow:auto;">
                <div class="space-between">
                    <span class="tag is-dark">{{selectedProfile.slug}}</span>
                    <span class="tag is-success" *ngIf="!isOutdated()">Up to date</span>
                    <span class="tag is-warning" *ngIf="isOutdated()">Outdated</span>
                </div>
                <div class="space-between" style="margin-top: 1em;">
                    <div>
                        Current version: {{selectedProfile.id}}
                    </div>
                    <div class="flex-row">
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="selectedProfileCtx.hasChanges"
                                [disabled]="!selectedProfileCtx.hasChanges"
                                (click)="saveChanges()"
                            ><tra slug="btn_save"></tra></button>
                        </div>
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="isOutdated()"
                                [disabled]="!isOutdated()"
                                (click)="upgradeReportingProfile()"
                            ><tra slug="Upgrade Version"></tra></button>
                        </div>
                    </div>
                </div>
                <div class="flex-row" style="margin-top: 1em;">
                    <div class="card" *ngFor="let language of selectedProfile.config | keyvalue">
                        <h4>
                            {{getLanguageCaption(language.key)}}
                        </h4>
                        <strong>Item Domains</strong>
                        <div class="flex-column separator" style="gap: 0.5em;" *ngFor="let itemDomain of language.value.entry_domain | keyvalue: keepOriginalOrder">
                            <!-- <strong>{{itemDomain.key}}</strong> -->
                            <div class="space-between" style="align-items: flex-start;">
                                <div class="flex-column" style="width: 10em;">
                                    Domain name
                                    <input class="input" 
                                        [ngModel]="itemDomain.key"
                                        (blur)="onDomainNameChange(itemDomain.key, $event.target.value, language.key, $event.target)"
                                    >
                                </div>
                                <a class="button" (click)="deleteDomain(language.value.entry_domain, itemDomain.key)" (change)="onSelectedProfileChange()">
                                    <i class="fas fa-trash"  aria-hidden="true"></i>
                                </a> 
                            </div>
                            <div *ngFor="let domain of itemDomain.value; let i = index;" class="flex-row">
                                <div class="flex-column" style="width: 5em;">
                                    Short name
                                    <input class="input" [(ngModel)]="domain.slug" (change)="onSelectedProfileChange()">
                                </div>
                                <div class="flex-column"  style="width: 5em;">
                                    Cut score
                                    <input class="input" type="number" [(ngModel)]="domain.cut_score" (change)="onSelectedProfileChange()">
                                </div>
                                <div class="flex-column">
                                    Comparison type
                                    <div class="select" style="width: 11em;">
                                        <select 
                                          style="width: 100%;"
                                          [(ngModel)]="domain.comparison_type"
                                          (change)="onSelectedProfileChange()"
                                        >
                                          <option *ngFor="let type of comparisonTypes" [value]="type.comparison_type">{{type.caption}}</option>
                                        </select> 
                                    </div>
                                </div>
                                <i class="fas fa-trash small-icon" style="align-self: center;" aria-hidden="true" (click)="deleteCutScore(itemDomain.value, i)"></i>
                            </div>
                            <div style="margin-top: 0.5em;">
                                <button class="button is-small" (click)="addCutScoreModal(itemDomain.value)"><tra slug="btn_add"></tra></button>
                            </div>
                        </div>
                        <button class="button is-primary" (click)="addItemDomModal(language.value.entry_domain)">
                            <tra slug="btn_add"></tra>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
            <div *ngSwitchCase="Modal.ITEM_DOMAIN" style="width: 35em; ">
                <div>
                    <tra [slug]="cmc.message"></tra>
                </div>
                <div style="margin-top: 1em;">
                    <input class="input" [(ngModel)]="cmc.domainName">
                </div>
                <div class="is-error" *ngIf="cmc.error" style="margin-top: 1em;">
                    <tra [slug]="cmc.error"></tra>
                </div>
                <div style="display: flex; gap: 1em; margin-top: 1em">
                    <div>
                        <button class="button" (click)="closeModal()">
                            Cancel
                        </button>
                    </div>
                    <div>
                        <button class="button" (click)="addItemDomCurrent(cmc.entry_domain, cmc.domainName)">
                            Current
                        </button>
                    </div>
                    <div>
                        <button class="button is-info" (click)="addItemDomCommon(cmc.domainName)">
                            All
                        </button>
                    </div>
                </div>
            </div>
            <div *ngSwitchCase="Modal.CUT_SCORES" style="width: 35em; ">
                <div>
                    <tra [slug]="cmc.message"></tra>
                </div>
                <div style="display: flex; gap: 1em; margin-top: 1em">
                    <div>
                        <button class="button" (click)="closeModal()">
                            Cancel
                        </button>
                    </div>
                    <div>
                        <button class="button" (click)="addCutScoreCurrent(cmc.cutScores)">
                            Current
                        </button>
                    </div>
                    <div>
                        <button class="button is-info" (click)="addCutScoreCommon()">
                            All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>