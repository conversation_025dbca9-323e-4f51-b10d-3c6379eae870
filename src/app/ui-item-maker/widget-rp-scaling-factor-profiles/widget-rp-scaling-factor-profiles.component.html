<div *ngIf="generalListCtx.isLoading" class="notification is-light">
    Loading...
</div>
<div 
    style="display: flex; gap: 1em;"
>
    <div 
        *ngIf="!generalListCtx.isLoading"
        class="twtar-panel"
        [class.is-collapsed]="generalListCtx.isCollapsed"
        [class.is-balanced]="!this.generalListCtx.isCollapsed"
    >
        <div *ngIf="!generalListCtx.data || !generalListCtx.data.length">
            No profiles found for this authoring group.
        </div>
        <ng-container *ngIf="generalListCtx.data  && generalListCtx.data.length">
            <div class="panel-header">
                <h4 *ngIf="!generalListCtx.isCollapsed">
                    Select a profile
                </h4>
                <button class="button is-small is-light" (click)="generalListCtx.isCollapsed = !generalListCtx.isCollapsed ">
                    <span class="icon">
                    <i class="fas" [ngClass]="generalListCtx.isCollapsed  ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
                    </span>
                </button>
            </div>
            <div [class.is-hidden]="generalListCtx.isCollapsed " class="panel-content">
                <div 
                    *ngFor="let profile of generalListCtx.data; let i = index;" 
                    class="tw-row"
                    [class.is-selected]="profile.is_selected && profile.is_selected == 1"
                >
                    <div>
                        <div>
                            <strong>{{profile.id}}</strong> | {{profile.slug}}
                        </div>
                        <div>
                            {{profile.created_on}}
                        </div>
                    </div>
                    <div style="display: flex;">
                        <div>
                            <button
                                class="button is-small"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                                (click)="revokeSubprofile(profile.id, i)"
                            >
                                <tra slug="auth_revoke"></tra>
                            </button>
                        </div>
                        <div>
                            <button 
                                class="button is-small"
                                [class.is-info]="profile.is_selected && profile.is_selected == 1"
                                (click)="setSubprofile(profile)"
                                [disabled]="(profile.is_selected && profile.is_selected == 1) || generalListCtx.isSaving"
                            >Select</button>
                        </div>
                    </div>
                </div>
                <div class="flex-row">
                    <input class="input" [(ngModel)]="newSubprofileSlug" [disabled]="generalListCtx.isSaving">
                    <button class="button is-small" (click)="createSubprofile()" [disabled]="newSubprofileSlug.length <= 0 || generalListCtx.isSaving">Create</button>
                </div>
            </div>
        </ng-container>
    </div>
    <div style="flex-grow: 1; overflow: auto;" *ngIf="!selectedProfileCtx.isLoading &&  selectedProfile">
        <div             
            class="twtar-panel"
        >
            <div class="panel-header">
                <h4>
                    Review profile
                </h4>
                <strong>{{renderDate(selectedProfile.created_on)}}</strong>
            </div>
            <div class="panel-content" style="overflow:auto;">
                <div class="space-between">
                    <span class="tag is-dark">{{selectedProfile.slug}}</span>
                    <span class="tag is-success" *ngIf="!isOutdated()">Up to date</span>
                    <span class="tag is-warning" *ngIf="isOutdated()">Outdated</span>
                </div>
                <div class="space-between" style="margin-top: 1em;">
                    <div>
                        Current version: {{selectedProfile.id}}
                    </div>
                    <div class="flex-row">
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="selectedProfileCtx.hasChanges"
                                [disabled]="!selectedProfileCtx.hasChanges"
                                (click)="saveChanges()"
                            ><tra slug="btn_save"></tra></button>
                        </div>
                        <div>
                            <button 
                                class="button"
                                [class.is-success]="isOutdated()"
                                [disabled]="!isOutdated()"
                                (click)="upgradeReportingProfile()"
                            ><tra slug="Upgrade Version"></tra></button>
                        </div>
                    </div>
                </div>
                <div class="flex-column">
                    Grade
                    <div style="width: 10em;">
                        <input class="input" [(ngModel)]="selectedProfile.config.grade" (change)="onSelectedProfileChange()"> 
                    </div>
                </div>
                <div class="flex-row" style="margin-top: 1em;">
                    <div class="card" *ngFor="let language of selectedProfile.config.lang | keyvalue">
                        <h4>
                            {{getLanguageCaption(language.key)}}
                        </h4>
                        <strong>Scale Information</strong>
                        <div class="flex-column">
                            Total weight
                            <input class="input" [(ngModel)]="language.value.total_weight" (change)="onSelectedProfileChange()">
                        </div>
                        <div class="flex-column">
                            Total max score
                            <input class="input" [(ngModel)]="language.value.total_max_score" (change)="onSelectedProfileChange()">
                        </div>
                        <div class="flex-column">
                            Scalings
                            <div class="indent">
                                <div class="flex-column separator" *ngFor="let scale of language.value.scalings; let i = index;">
                                    <div class="flex-column">
                                        Entry Domain
                                        <input class="input" [(ngModel)]="scale.entry_domain" (change)="onSelectedProfileChange()">
                                    </div>
                                    <div class="flex-column">
                                        Weight
                                        <input class="input" [(ngModel)]="scale.weight" (change)="onSelectedProfileChange()">
                                    </div>
                                    <div class="flex-column">
                                        Max score
                                        <input class="input" [(ngModel)]="scale.max_score" (change)="onSelectedProfileChange()">
                                    </div>
                                    <a class="button is-delete-button" (click)="deleteElement(language.value.scalings, i)">
                                        <i class="fas fa-trash"  aria-hidden="true"></i>
                                    </a> 
                                </div>
                                <button class="button is-primary" (click)="addScaling(language.value.scalings)" style="margin-top: 0.5em;">
                                    <tra slug="btn_add"></tra>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>