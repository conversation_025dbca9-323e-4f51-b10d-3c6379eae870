import { FormControl } from '@angular/forms';
import * as moment from 'moment';
import { downloadFile, downloadStr } from '../../../core/download-string';
// app services
import { AuthService } from '../../../api/auth.service';
import { LangService } from '../../../core/lang.service';
import { IQuestionConfig, IQuestionScoringInfo, IScoringCodes } from '../../../ui-testrunner/models';
import { IItemAuthNote } from '../../element-notes/element-notes.component';
import { PARAM_SPECIAL_FLAGS } from '../../framework-dimension-editor/model';
import { ItemMakerService } from '../../item-maker.service';
import { ItemType } from '../../models';
import { DimensionCategory, DimensionType, ENextBtnOpt, IAccessibilitySetting, IAssessmentFrameworkDef, IAssessmentFrameworkDetail, IAssessmentFrameworkDimensionDetail, IAssessmentPartition, NEXT_BTN_OPTS, QUESTION_WORDING_OPTS, TestFormConstructionMethod, ISectionItemsMap, ISplitScreenSettings, getPartitionPropValue, getFormPartitionObj } from '../models/assessment-framework';
import { stripTabs, TAB, NEWLINE } from '../models/constants';
import { QuestionView } from '../models/types';
import { ItemBankCtrl } from './item-bank';
import { ItemFilterCtrl } from './item-filter';
import { PanelCtrl } from './mscat';
import { ItemSetPublishingCtrl } from './publishing';
import { FrameworkQuadrantCtrl } from './quadrants';
import { ItemBankSaveLoadCtrl } from './save-load';
import { TestletCtrl } from './testlets';
import { ItemBankUtilCtrl } from './util';
import { Destroyable } from './destroyable';
import { CLICK_TO_DRAG_DEFAULT } from 'src/app/ui-testrunner/accessibility.service';
import { randomName } from 'src/app/constants/fakenames';
import { randArrEntry } from 'src/app/ui-testadmin/demo-data.service';
import { WhitelabelService } from '../../../domain/whitelabel.service';
import { partition } from 'cypress/types/lodash';
import { ISeItem, ISEItemSet, IAsmtStructuresDB, TItemSetSlugMapping } from '../../widget-score-entry-settings/types';

type SimplifiedQuestion = { id: number, label?: string }[];
type RemovedQuestionMap = {[key:string]: string[]};

export class ItemSetFrameworkCtrl implements Destroyable {
  
  selectedNewItemBank = new FormControl();

  accessibilitySettings: {
    [key:string]:IAccessibilitySetting
  } = {
    clickToDragDrop: {
      caption: 'click_to_drag_drop',
      isConfigurable: false,
      defaultVal: CLICK_TO_DRAG_DEFAULT
    }
  }
  asmtFmrk: IAssessmentFrameworkDetail;
  isFrameworkParamImportExport:boolean;
  isScoringScalesExpanded: boolean;
  frameworkParamImportExport = new FormControl();
  isEditRawFramework:boolean;
  rawFrameworkJson = new FormControl();
  paramExpansionMap:Map<IAssessmentFrameworkDimensionDetail, boolean> = new Map();
  isParamImportDryRun = new FormControl(false);
  public isParamDetailExpanded:boolean = false;
  public isAFParamDetailExpanded:boolean = false;
  public isAFParamDetailOrdering:boolean = false;
  public isFrameworkView:boolean;
  jsonImport = new FormControl();
  isImportOpen: boolean;
  isImporting: boolean;
  isImportFlushing: boolean;
  assessmentFrameworks: IAssessmentFrameworkDef[] = [{__id: '1', caption: ''}];
  activeAssessmentFramework = new FormControl();
  activeAsmtFwrkDimensions: Array<IAssessmentFrameworkDimensionDetail[]>; // an array of arrays...
  isEditingFramework: boolean;
  questionFrameworkMap: {d1: string[], d2: string[], count: Map<string, number>};
  selectedQuestionView = QuestionView.QUESTION_BANK;
  public testFormConstructionMethod = new FormControl(TestFormConstructionMethod.TLOFT);
  public questionWordingFc = new FormControl(QUESTION_WORDING_OPTS[0]);
  availableItemSets;
  isReadyToAddItemSet;
  allowNewItemsDuringParamImport = new FormControl(false);
  requireItemBankRefresh: boolean;
  importingQuestionLabel: string;
  public util = new ItemBankUtilCtrl();
  
  public saveLoadCtrl: ItemBankSaveLoadCtrl;
  public panelCtrl: PanelCtrl;
  public quadrantCtrl: FrameworkQuadrantCtrl;
  public testletCtrl: TestletCtrl;
  public itemFilterCtrl: ItemFilterCtrl;
  public publishingCtrl: ItemSetPublishingCtrl;

  partitionIdToPreambleFc: {[key:number]: FormControl} = {};
  partitionIdToPostambleFc: {[key:number]: FormControl} = {};

  isParamSaveRequired = false;
  isAsmtStructureSaveRequired = false;
  isAsmtStructureOutdated = false;

  // framework settings
  splitScreenSettings: { caption: string, prop: string }[] = [
    {caption: 'Split-screen links in nav bar', prop: 'isSplitScreenlinksInNavbar'},
    {caption: 'Hide read selection links from question header', prop: 'isHideReadSelLinksInQueHeader'},
    {caption: 'Show custom split screen controls (left, split, right)', prop: 'isShowCutsomSplitScreenControls'}
  ]

  constructor(
    public auth: AuthService,
    public myItems: ItemMakerService,
    public itemBankCtrl: ItemBankCtrl,
    private lang: LangService,
    private whiteLabel: WhitelabelService
  ){
    
  }

  destroy() {
    
  }

  isTestformMsCat() { return this.testFormConstructionMethod.value === TestFormConstructionMethod.MSCAT; }
  isTestformLinear() { return this.testFormConstructionMethod.value === TestFormConstructionMethod.LINEAR; }
  isTestBuckets() { return this.testFormConstructionMethod.value === TestFormConstructionMethod.BUCKETS; }
  isTestformLOFT() { return this.testFormConstructionMethod.value === TestFormConstructionMethod.TLOFT; }
  isTestFormDirectConstruction = () => this.isTestformLinear() && (this.selectedQuestionView === QuestionView.FORM_CONSTRUCTION);
  isTestFormScoreEntry() { return this.testFormConstructionMethod.value === TestFormConstructionMethod.SCORE_ENTRY; }

  expandParam(param:IAssessmentFrameworkDimensionDetail, isExpand=true){
    this.paramExpansionMap.set(param, isExpand);
  }
  isParamExpanded(param:IAssessmentFrameworkDimensionDetail){
    return this.paramExpansionMap.get(param);
  }
  getParamCategNaming(dimLevel: number) {
    if (dimLevel === 0) {
      return 'Dimension';
    }
    return this.lang.tra('ie_parameter');
  }

  getPairableHumanScoringScales() {
    // console.log("pair", this.asmtFmrk.pairableHumanScoringScales)
    return this.asmtFmrk.pairableHumanScoringScales || [];
  }

  addPairableHumanScoringScales() {
    if(!this.asmtFmrk.pairableHumanScoringScales) this.asmtFmrk.pairableHumanScoringScales = [];
    this.asmtFmrk.pairableHumanScoringScales.push([
      { id: 0, name: '', description: '' },
      { id: 0, name: '', description: '' }
    ]);
    // console.log(this.asmtFmrk.pairableHumanScoringScales)
  }

  updatePairableHumanScoringScales(pairIdx: number, scaleIdx: number, scale: IScoringCodes) {
    if(!this.asmtFmrk.pairableHumanScoringScales) return;
    const existingScale = this.asmtFmrk.pairableHumanScoringScales[pairIdx][scaleIdx];
    existingScale.name = scale.short_name
    existingScale.id = scale.id
    if(this.lang.c() === 'fr'){
      existingScale.description =  scale.description_fr;
    } else {
      existingScale.description = scale.description_en;
    }

  }

  // removePairableHumanScoringScaleIds(pairIdx) {
  //   if(!this.asmtFmrk.pairableHumanScoringScaleIds) return;
  //   this.asmtFmrk.pairableHumanScoringScaleIds.splice(pairIdx, 1);
  // }
  
  checkIsAllReady() {
    let isAllReady = true;
    this.itemBankCtrl.getItems().forEach(question => {
      if (!question.isInfoSlide && !question.isReady) {
        isAllReady = false;
      }
    });
    return isAllReady;
  }

  getNumModules = () => ''; // deprecated
  
  previewQuestionFrameworkMapping() {
    console.log('previewQuestionFrameworkMapping 1');
    this.questionFrameworkMap = {
      d1: [],
      d2: [],
      count: new Map()
    };
    let d1Def = this.asmtFmrk.primaryDimensions[0];
    let d2Def = this.asmtFmrk.primaryDimensions[1];
    let d1Tags = d1Def.config.tags;
    let d2Tags = d2Def.config.tags;
    // let d1_pre = this.questionFrameworkMap.d1 = d1Tags.map(tag => tag.code);
    let d2 = this.questionFrameworkMap.d2 = d2Tags.map(tag => tag.code);
    let joiner, joinerCode;
    this.asmtFmrk.secondaryDimensions.forEach(sec => { // grossnes....
      if (sec.code === 'D1b') {
        joinerCode = sec.code;
        joiner = sec.config.tags.map(tag => tag.code);
      }
    });
    
    let d1 = this.questionFrameworkMap.d1 = joiner;
    d1.forEach(tag1 => {
      d2.forEach(tag2 => {
        let key = tag1 + '/' + tag2;
        this.questionFrameworkMap.count.set(key, 0);
      });
    });
    this.itemBankCtrl.getItems().forEach(question => {
      let tag1 = question['meta'][joinerCode];
      let tag2 = question['meta'][d2Def.code];
      if (tag1 && tag2) {
        let key = tag1 + '/' + tag2;
        let count = this.questionFrameworkMap.count.get(key);
        this.questionFrameworkMap.count.set(key, count + 1);
      }
    });
    console.log('previewQuestionFrameworkMapping 2');
  }

  refreshBucketCount(bucket){
    const param = this.asmtFmrk.bucketParam;
    let num = 0;
    this.itemBankCtrl.questions.forEach(question => {
      if (question.meta && question.meta[param] === bucket.label){
        num ++;
      }
    })
    bucket.count = num
  }
  
  getSectionQuestionsBucket = (asmtFmrk:IAssessmentFrameworkDetail, partition: IAssessmentPartition) =>  {
    const sectionId = this.getPartitionPropValue(partition, 'id');
    const param = this.asmtFmrk.bucketParam;
    const buckets = this.getPartitionPropValue(partition, 'buckets');
    const questions = buckets.map(bucket => {
      const availableQuestions:{label:string, id:number}[] = [];
      this.itemBankCtrl.questions.forEach(question => {
        if (question.meta && question.meta[param] === bucket.label){
          availableQuestions.push({
            id: question.id,
            label: question.label,
          });
        }
      })
      if (availableQuestions.length === 0){
        console.error('Bucket too empty', bucket.label)
      }
      return randArrEntry(availableQuestions)
    });
    let sectionItems = {
      questions
    };
    if (!sectionItems) {
      sectionItems = this.asmtFmrk.sectionItems[sectionId] = {
        questions: []
      };
    }
    return sectionItems;
  }

  getSectionQuestionsLinear = (asmtFmrk:IAssessmentFrameworkDetail, partition: IAssessmentPartition) => {
    const sectionId = this.getPartitionPropValue(partition, 'id');
    if (!this.asmtFmrk.sectionItems) {
      this.asmtFmrk.sectionItems = {};
    }
    let sectionItems = this.asmtFmrk.sectionItems[sectionId];
    if (!sectionItems) {
      sectionItems = this.asmtFmrk.sectionItems[sectionId] = {
        questions: []
      };
    }
    return sectionItems;
  }
  
  removeQuestionFromTestform(questionIdentifier, partition: IAssessmentPartition) {
    if (confirm('Are you sure you want to remove this item? ' + `(${questionIdentifier.label})`)) {
      const sectionQuestions = this.getSectionQuestionsLinear(this.asmtFmrk, partition);
      const i = sectionQuestions.questions.indexOf(questionIdentifier);
      if (i !== -1) {
        sectionQuestions.questions.splice(i , 1);
      }
    }
  }
  /**
   * 
   * @returns A map containing removed questions depending on the type of assesment it'll map to testlets, sections, and/or panels
   */
  getRemovedQuestionInFramework() {
    const removedQuestionsMap: RemovedQuestionMap = {};
    switch (this.testFormConstructionMethod.value as TestFormConstructionMethod) {
      default:
      case TestFormConstructionMethod.LINEAR:
        this.getRemovedQuestionFromLinearForm(removedQuestionsMap);
        break;
      case TestFormConstructionMethod.TLOFT:
        this.getRemovedQuestionFromTestlets(removedQuestionsMap);
        break;
      case TestFormConstructionMethod.MSCAT:
        this.getRemovedQuestionFromMSCATPanel(removedQuestionsMap);
        this.getRemovedQuestionFromTestlets(removedQuestionsMap);
        break;
    }
    return removedQuestionsMap
  }

  /**
   * Function that takes a map and populates with removed question in the current frameworks linear forms
   * @param removedQuestionsMap a map of removed question
   */
  getRemovedQuestionFromLinearForm(removedQuestionsMap: RemovedQuestionMap){
    this.asmtFmrk.partitions.forEach((partition) => {
      const questionInCurrentPartition = this.getSectionQuestionsLinear(this.asmtFmrk, partition).questions;
      const removedInCurrentPartition = this.getRemovedQuestions(questionInCurrentPartition as SimplifiedQuestion);
      if (removedInCurrentPartition.length > 0) {
        removedQuestionsMap[partition.description] = removedInCurrentPartition;
      }
    });
  }

  /**
   * Function that takes a map and populates with removed question in the current frameworks testlets
   * @param removedQuestionsMap a map of removed question
   */
  getRemovedQuestionFromTestlets(removedQuestionsMap: RemovedQuestionMap){
    this.asmtFmrk.testlets.forEach(testlet => {
      const removedInCurrentTestlet = this.getRemovedQuestions(testlet.questions as SimplifiedQuestion);
      if (removedInCurrentTestlet.length > 0) {
        removedQuestionsMap[`Testlet ${this.lang.tra('ie_id')} ${testlet.id}`] = removedInCurrentTestlet;
      }
    });
  }


  getRemovedQuestionFromMSCATPanel(removedQuestionsMap: RemovedQuestionMap) {
    this.asmtFmrk.panels?.forEach(panel => {
      const panelKey = `${this.lang.tra('ie_panel')} ${this.lang.tra('ie_id')} ${panel.id}`;
      let removedQuestionsFormatted = [];
  
      panel.modules.forEach(module => {
        const questions = module.__cached_itemIds.map((id, index) => {
          const question = this.itemBankCtrl.getQuestionById(id);
          return question ? question : { id: id, label: module.itemLabels[index] };
        });        
        const removedInCurrentModule = this.getRemovedQuestions(questions as SimplifiedQuestion);
        if (removedInCurrentModule.length) {
          removedQuestionsFormatted.push(`Module ${module.moduleId}: ${removedInCurrentModule.join(', ')}`);
        }
      });
  
      if (removedQuestionsFormatted.length) {
        removedQuestionsMap[panelKey] = removedQuestionsFormatted;
      }
    });
  }
  

  /**
   * 
   * @param frmwrkQ question that you want to check whether or not they are a part of the available item banks
   * @returns all questions that were removed in an array of strings formatted: label (id)
   */
  getRemovedQuestions = (frmwrkQ: SimplifiedQuestion) => {
    const removedQuestions = [];
    frmwrkQ.forEach(fq => {
      const isInBankQ = this.itemBankCtrl.questions.some(bq => bq.id === fq.id);
      if (!isInBankQ) {
        removedQuestions.push(fq);
      }
    });
    return removedQuestions.map(q => `${q.label} (${q.id})`);
  }

  alertRemovedQuestions(removedQuestions: RemovedQuestionMap){
    if (Object.keys(removedQuestions).length) {
      Object.entries(removedQuestions)
        .map(([location, questions]) => `\n\t ${location}:${questions.join(', ')}`)
        .join();
      alert( this.lang.tra('framework_remove_missing_item_prompt'));
    }
  }

  removeSectionFromTestForm = (partition: IAssessmentPartition) => {

    const partitionId = partition.id
    const msg = `${this.lang.tra('ie_remove_section')} ${partitionId} ?`;
    if(confirm(msg)) {
      // remove from framework.partitions
      const i = this.asmtFmrk.partitions.indexOf(partition);
      if (i !== -1) {
        this.asmtFmrk.partitions.splice(i, 1);
      }

      // remove from framework.sections
      delete this.asmtFmrk.sectionItems[partitionId];
    }
  }
  
  editRawFramework(){
    this.isEditRawFramework = true;
    this.rawFrameworkJson.setValue(JSON.stringify(this.asmtFmrk, null, 2));
  }
  cancelEditRawFramework(){
    this.isEditRawFramework = false;
  }
  saveRawFramework(){
    const json = JSON.parse(this.rawFrameworkJson.value);
    this.asmtFmrk = json;
    alert('Save and refresh for best results')
    this.isEditRawFramework = false;
  }
  
  openFrameworkParamExport(){
    if (this.isFrameworkParamImportExport){
      this.isFrameworkParamImportExport = false;
    }
    else{
      const data = JSON.stringify(this.activeAsmtFwrkDimensions);
      this.frameworkParamImportExport.setValue(data);
      this.isFrameworkParamImportExport = true;
    }
  }
  
  importFrameworkParams(isInsertOnly:boolean=false){
    const dataJson = this.frameworkParamImportExport.value;
    const newData = JSON.parse(dataJson)
    console.log('isInsertOnly', isInsertOnly)
    if (isInsertOnly){
      const existingFields = new Map();
      const paramLists = [
        this.asmtFmrk.primaryDimensions,
        this.asmtFmrk.secondaryDimensions,
      ]
      paramLists.forEach(paramList =>{
        paramList.forEach(param => {
          existingFields.set(param.code, true)
        })
      })
      for (let list_i=0; list_i<newData.length; list_i++){
        const srcList = newData[list_i];
        const targetList = paramLists[list_i];
        srcList.forEach(param => {
          if (!existingFields.has(param.code)){
            targetList.push(param)
          }
        })
      }
    }
    else{
      this.activeAsmtFwrkDimensions = newData;
      this.asmtFmrk.primaryDimensions = newData[0];
      this.asmtFmrk.secondaryDimensions = newData[1];
    }
    this.isFrameworkParamImportExport = false;
    // this.saveLoadCtrl.saveChanges();
  }
  
  
  addRefDoc(){
    if (!this.asmtFmrk.referenceDocumentPages){
      this.asmtFmrk.referenceDocumentPages = [];
    }
    const itemId = +prompt('Item ID');

    const inputPrompt = (lang: string) => prompt(`Resource Name ${lang}`);
    const captionEn = this.itemBankCtrl.isLangEnabled('en') ? inputPrompt('(EN)') : '';
    const captionFr = this.itemBankCtrl.isLangEnabled('fr') ? inputPrompt('(FR)') : '';

    this.asmtFmrk.referenceDocumentPages.push({itemId, caption: captionEn, captionFr});
  }
  

  

  getLinearFormQuestions(){
    const questions: IQuestionConfig[] = [];
    this.asmtFmrk.partitions.forEach(partition => {
      const questionInfoContainer = this.getSectionQuestionsLinear(this.asmtFmrk, partition);
      
      questionInfoContainer.questions.forEach(questionInfo => {
        let question;
        if (questionInfo.label && !questionInfo.id){
          question = this.itemBankCtrl.getQuestionByLabel(questionInfo.label);
          if (question){
            questionInfo.id = question.id;
          }
        }
        if (!question){
          question = this.itemBankCtrl.getQuestionById(questionInfo.id);
        }
        if (question) {
          questions.push(question);  
        }
      });
      // load simplied list of questions
    });
    return questions;
  }

  /**
   * 
   * @param qIds the question IDs to find
   * @returns an array of references to questions determined by their IDs
   */
  getQuestionsById(qIds: number[]){
    const questions: IQuestionConfig[] = [];
    qIds.forEach((id) => {
      let question;
      question = this.itemBankCtrl.getQuestionById(id);
      if (question) {
        questions.push(question);  
      }
    })
    return questions;
  }
  
  getNumQuestions() {
    return this.itemBankCtrl.getItems().length;
  }
  
  getNumQuadrants() {
    if (this.asmtFmrk && this.asmtFmrk.quadrantItems) {
      return this.asmtFmrk.quadrantItems.length;
    }
    return '--';
  }
  getNumTestlets() {
    if (this.asmtFmrk && this.asmtFmrk.testlets) {
      return this.asmtFmrk.testlets.length;
    }
    return '--';
  }
  getNumStoredPanels() {
    if (this.asmtFmrk && this.asmtFmrk.assembledPanels) {
      return this.asmtFmrk.assembledPanels.length;
    }
    return '--';
  }
  getNumTestForms() {
    if (this.asmtFmrk && this.asmtFmrk.testForms) {
      return this.asmtFmrk.testForms.length;
    }
    return '--';
  }
  
  async createNewSection(createDefault:boolean = false) {
    const id = this.util.nextId(this.asmtFmrk.partitions);
    const description = createDefault ? '' : prompt('new section name', 'New Section');

    const sectionProps: IAssessmentPartition = { id, description };
    if (this.asmtFmrk.useLangSpecificSectionProps) {
      sectionProps.langLink = {...sectionProps, isLangLink: true}; // keeping the id same and assign new description other languages
    }

    this.asmtFmrk.partitions.push(sectionProps);  
    if (!createDefault && this.isTestFormScoreEntry()) {
      await this.ensureItemInSection();
    }

    this.refreshPartitionToPreambleFcMap();
  }
  
  selectQuestionView(id: QuestionView) {
    this.selectedQuestionView = id;
    this.panelCtrl.activePanel = null;
    this.quadrantCtrl.activeQuadrant = null;
    this.testletCtrl.activeTestlet = null;
    this.itemFilterCtrl.updateItemFilter();
    if (id === QuestionView.TESTLETS) {
      this.testletCtrl.updateTestletFilter();
    }
    if (id !== QuestionView.QUESTION_BANK) {
      this.itemBankCtrl.currentQuestion = null;
    }
  }
  
  parseStats(stats: any) {
    let arr = [];
    Object.keys(stats).forEach(key => {
      arr.push({key, val: stats[key]});
    });
    return arr;
  }

  iteratePrePostAmbles(){
    
  }
  
  
  newItemBankAdd() {
    const newItemBankId = +this.selectedNewItemBank.value;
    let newItemBank;
    this.availableItemSets.forEach(itemBank => {
      if (itemBank.id === newItemBankId) {
        newItemBank = itemBank;
      }
    });
    // console.log('newItemBank', newItemBankId, newItemBank)
    if (newItemBank) {
      this.saveLoadCtrl.itemBanksUsed.push(newItemBank);
      this.saveLoadCtrl.saveChanges(); // to do: dedicated api call
      this.requireItemBankRefresh = true;
      this.saveLoadCtrl.fakeItemSetSaveBuffer();
    }
    this.isReadyToAddItemSet = false;
  }
  
  removeItemBank(i) {
    this.saveLoadCtrl.itemBanksUsed.splice(i, 1);
    this.saveLoadCtrl.saveChanges(); // to do: dedicated api call
    this.requireItemBankRefresh = true;
    this.saveLoadCtrl.fakeItemSetSaveBuffer();
  }

  newItemBankStart() {
    if ( this.isReadyToAddItemSet) {
      this.isReadyToAddItemSet = false;
    } 
    else if (this.availableItemSets) {
      this.isReadyToAddItemSet = true;
    } 
    else {
      this.isReadyToAddItemSet = true;
      this.myItems
        .loadMyItemSets()
        .then(itemSets => {
          this.saveLoadCtrl.isLoadingItemSets =  false;
          this.availableItemSets = itemSets;
        });
    }
  }

  scrollToQuestionListing() {
    setTimeout(() => {
      // const el = this.questionListingEl.nativeElement;
      // const options = {behavior: 'smooth', block: 'start'};
      // try {
      //   el.scrollIntoView(options);
      // } catch (e) {
      //   el.scrollIntoViewIfNeeded(options);
      // }
    }, 100);
  }
  
  getCurrentParameters (): IAssessmentFrameworkDimensionDetail[] {
    if (this.asmtFmrk) {
      return []
      .concat(this.asmtFmrk.primaryDimensions)
      .concat(this.asmtFmrk.secondaryDimensions);
    }
    return [];
  }
  
  identifySpecialParams(key: string) {
    const params = this.getCurrentParameters();
    const matchingParams: IAssessmentFrameworkDimensionDetail[] = [];
    params.forEach(param => {
      if (param.config && param.config.special) {
        if (param.config.special[key]) {
          matchingParams.push(param);
        }
      }
    });
    return matchingParams;
  }
  
  identifySingleEntryParams() {
    return this.identifySpecialParams(PARAM_SPECIAL_FLAGS.SINGLE_RESPONSE_ENTRY);
  }
  
  identifyBooleanParams() {
    const paramRef: Map<string, boolean> = new Map();
    const params = this.getCurrentParameters();
    params.forEach(param => {
      if (param.type === 'binary') {
        paramRef.set(param.code, true);
      }
    });
    return paramRef;
  }
  
  parseParamColor(color: string) {
    if (color) {
      return color;
    }
    return 'inherit';
  }
  
  openImport() {
    this.isImportOpen = true;
    this.jsonImport.reset();
  }
  closeImport() {
    this.jsonImport.reset();
    this.isImportOpen = false;
  }
  importTarget: FileList
  updateImportTarget(importTarget) {
    this.importTarget = importTarget;
  }
  async startImport() {
    let rows;
    let i = 0;
    const allowNew = this.allowNewItemsDuringParamImport.value
    const isParamImportDryRun = this.isParamImportDryRun.value
    const dimensions = [this.asmtFmrk.primaryDimensions, this.asmtFmrk.secondaryDimensions];
    const PRIMARY_KEY = 'question_label';
    const CONTENT_KEY = 'content';
    const LANG_KEY = 'lang';
    const ISSUES_KEY = 'issues';
    const NOTES_KEY = 'notes';
    const reservedKeys = [PRIMARY_KEY,CONTENT_KEY, LANG_KEY, ISSUES_KEY, NOTES_KEY];
    try {
      const file = this.importTarget.item(0);
      const fileTypeFull = file.type;
      const fileType = fileTypeFull.split('/')[0];
      const fileExt = file.name.split('.').pop();
      if (fileExt !== 'xlsx') {  
        alert(this.lang.tra('sa_unsupported_file',undefined,{file_type:fileExt})+'. Need XLSX for upload.');
        return 
      }
      const res:any = await this.auth.excelToJson(file);
      rows = res.json;
      console.log(rows)
      // rows = JSON.parse(this.jsonImport.value);
      
    } catch (e) {
      alert('invalid JSON input');
      return;
    }
    // identify parameters
    const paramRef: Map<string, IAssessmentFrameworkDimensionDetail> = new Map();
    dimensions.forEach(dim => {
      dim.forEach(param => {
        paramRef.set(param.code, param);
      });
    });
    // identify changes
    type ParamVal = boolean | string | number;
    const changedQuestionProp:{question_label:string, param:string, val_old:ParamVal, val_new:ParamVal }[] = [];
    const unknownParameters:{[key:string]:boolean} = {};
    // sanitize labels
    rows.forEach(questionRow => {
      questionRow.question_label = ''+questionRow.question_label;
    });
    // identify questions
    const questionsByLabel = new Map();
    this.itemBankCtrl.getItems().forEach(question => questionsByLabel.set(question.label, question));
    const missingQuestions = [];
    if (!allowNew){
      rows.forEach(questionRow => {
        const label = questionRow.question_label;
        if (!questionsByLabel.has(label)){
          missingQuestions.push(label);
        }
      });
      if (missingQuestions.length > 0){
        console.log('new items', missingQuestions);
        alert( missingQuestions.length + ' new question labels detected. Please select the checkbox to allow new items to be created with the import, or remove the new item labels from your import (see the log for a full list of the question_labels at issue).');
        return;
      }
    }
    // apply modifications
    this.isImporting = true;
    const increment = () => {
      i ++;
      if (i < rows.length) {
        applyNextQuestion();
      } 
      else {
        this.isImporting = false;
        this.isImportFlushing = true;
        console.log('Unknown Parameters', Object.keys(unknownParameters))
        console.log('Changed Question Props', changedQuestionProp)
        setTimeout(() => {
          this.isImportFlushing = false;
        }, 500);
        this.itemFilterCtrl.updateItemFilter();
        this.saveLoadCtrl.saveContent();
        this.closeImport();
      }
    };
    const selectQuestionByLabel = (questionLabel:string)=> {
      this.importingQuestionLabel = questionLabel;
      const question = questionsByLabel.get(questionLabel);
      if (!question) {
        if (allowNew){
          console.warn('Creating a new item', questionLabel);
          return this.itemBankCtrl.createNewQuestion(ItemType.ITEM).then(question=>{
            question.label = questionLabel;
            return question;
          })
        }
        else{
          increment();
        }
      }
      else{
        return this.itemBankCtrl.selectQuestion(question).then(res => question);
      }
    }
    const applyNextQuestion = () => {
      const row = rows[i];
      const questionLabel = row[PRIMARY_KEY];
      return selectQuestionByLabel(questionLabel)
      .then((question:IQuestionConfig) => {
        // identify changes
        const rowValues:{[key:string]:number | string | boolean} = {};
        Object.keys(row).forEach(paramCode => {
          let val = row[paramCode];
          if ( reservedKeys.indexOf(paramCode) === -1) {
            const param = paramRef.get(paramCode);
            if (!param) { unknownParameters[paramCode] = true }
            else {
              if (param.type === DimensionType.BINARY){
                switch (val){
                  case 'TRUE':
                  case 'true':
                  case '1':
                  case 1:
                    val = true;
                    break;
                  case 'false':
                  case 'FALSE':
                  case 0:
                  default:
                    val = false;
                    break;
                }
              }
              if (param.type === DimensionType.NUMERIC){
                if (val || val === 0){
                  val = ''+val;
                }
              }
              const val_old = question.meta[paramCode];
              const val_new = val;
              const isBothFalsey = ( (!val_old && val_old!==0) && (!val_new && val_new!==0) ) // except zeroes, those are important to handle NaN errors
              const isSame = (''+val_old === ''+val_new);
              if (!isSame && !isBothFalsey){ // intentionally less strict due to high number of undefined etc. being picked up
                changedQuestionProp.push({
                  question_label: questionLabel, 
                  param: paramCode, 
                  val_old, 
                  val_new,
                })
                rowValues[paramCode] = val;
              }
            }
          }
        })
        if (!isParamImportDryRun){
          // apply changes
          Object.keys(rowValues).forEach(paramCode => {
            const val = rowValues[paramCode];
            const param = paramRef.get(paramCode);
            if (param) {
              if (param.type === DimensionType.SELECT){
                let isMatchFound = false;
                if (!param.config.tags){ param.config.tags = []; }
                param.config.tags.forEach(tag => {
                  if (tag.code === val){
                    isMatchFound = true;
                  }
                })
                if (!isMatchFound){
                  param.config.tags.push({code:<string>val, name:''})
                }
              }
            }
            else{
              console.warn(paramCode, ' meta data is not defined in framework');
            }
            question.meta[paramCode] = val;
          });
          if (row[CONTENT_KEY]){
            if (row[LANG_KEY] === 'fr'){
              question.langLink.content = row[CONTENT_KEY];
            }
            else{
              question.content = row[CONTENT_KEY];
            }
          }
          if (row[NOTES_KEY]){
            question.notes = (question.notes || '') + '\n\n' + row[NOTES_KEY]
          }
        }
        
        if (isParamImportDryRun){
          increment();
        }
        else{
          this.itemBankCtrl.selectQuestion(question).then(()=>{ // this forces a save
            // console.log('ISSUES_KEY', row[ISSUES_KEY])
            if (row[ISSUES_KEY]){
              row[ISSUES_KEY].map(issueCaption => {
                this.createIssue(question.id, issueCaption);
              })
            }
            increment();
          })
        }
        // console.log(question.label, 'updated question')
      });
    };
    console.log('rows', rows);
    applyNextQuestion();
  }
  
  
  async exportTable(excludeHidden: boolean) {
    console.log('exportTable')
    const rows = [];
    const dimensions = [this.asmtFmrk.primaryDimensions, this.asmtFmrk.secondaryDimensions];
    const paramRef: Map<string, IAssessmentFrameworkDimensionDetail> = new Map();
    const params = this.getCurrentParameters();
    params.forEach(param => {
      paramRef.set(param.code, param);
    });

    const getVal = (param: IAssessmentFrameworkDimensionDetail, question:IQuestionConfig, scoringInfo: IQuestionScoringInfo ) => {
      switch(param.category){
        case DimensionCategory.SCORING:
          return sanitizeParameterValue(param.code, scoringInfo[param.code])
        case DimensionCategory.META:
        default:
          return sanitizeParameterValue(param.code, question.meta[param.code]);
      }
    }

    const sanitizeParameterValue = (paramCode, val) => {
      const param = paramRef.get(paramCode);
      if (param){
        switch(param.type){
          case DimensionType.BINARY:  return !!val;
          case DimensionType.SELECT:  return val || ''  ; 
          case DimensionType.NUMERIC: return +(val || 0); 
          case DimensionType.LABEL:   return val || ''  ; 
          case DimensionType.TEXT:    return val ? val.caption : '' ; 
          // case DimensionType.IMAGE:   return val ? JSON.stringify(val) : ''; 
          // case DimensionType.COORD:   return val ? JSON.stringify(val) : ''; 
        }
      }
      return val ? JSON.stringify(val) : ''
    }

    const getQuestionExpectedAnswer = (questionId: number) => {
      if (!this.itemBankCtrl.hasExpectedAnswer(questionId))
        return '...'

      return this.itemBankCtrl.getEAOptions(questionId).join('\n');
    }

    const getQuestionWeight = (questionId: number) => {
      if (!this.itemBankCtrl.hasExpectedAnswer(questionId))
        return '...'

      return this.itemBankCtrl.getEAWeight(questionId);
    }

    this.itemFilterCtrl.filteredQuestions.forEach(question => {
      const row = {
        item_id: question.id, 
        question_label: question.label,
        quadrantFreq: question.quadrantFreq,
        estimatedExposure: question.estimatedExposure,
        expectedAnswer: getQuestionExpectedAnswer(question.id),
        weight: getQuestionWeight(question.id)
      };
      dimensions.forEach(dim => {
        dim.forEach(param => {
          if (!excludeHidden) {
            row[param.code] = getVal(param, question, this.itemBankCtrl.getQuestionScoringInfo(question.id) );
          } else {
            if (!param.isHidden) {
              row[param.code] = getVal(param, question, this.itemBankCtrl.getQuestionScoringInfo(question.id) );
            }
          }
        });
      });
      rows.push(row);
    });
    const filename = `qb-${this.itemBankCtrl.currentSetName.value}-${moment().format('YYYY-MM-DD[T]HH_mm_ss')}`;
    const res = <any> await this.auth.jsonToExcel(rows, filename)
    downloadFile(res.url);
  }
  
  private createIssue(item_id, text){
    const user = this.auth.user().getValue();
    const data: IItemAuthNote = {
      text,
      parent_note_id: null,
      item_id,
      has_child: 0,
      is_resolved: 0,
      assigned_uid: null,
      created_by_first_name: user.firstName, // get user info from auth in API.
      created_by_last_name: user.lastName,
      created_by_uid: user.uid,
    };
    return this.auth.apiCreate('public/test-auth/notes', data);
  }

  refreshPartitionToPreambleFcMap() {
    for(const partition of this.asmtFmrk.partitions) {
      const partitionId = this.getPartitionPropValue(partition, 'id');
      if(!this.partitionIdToPreambleFc[partitionId]) {
        this.partitionIdToPreambleFc[partitionId] = new FormControl();
      }
      if(!this.partitionIdToPostambleFc[partitionId]) {
        this.partitionIdToPostambleFc[partitionId] = new FormControl();
      }
    }
  }

  hoistAssessmentFrameworkForEditing() {
    // console.log('Assessment Framework: ', asmtFwrk);
    const asmtFwrk = this.asmtFmrk;
    this.refreshPartitionToPreambleFcMap();
    this.asmtFmrk.quadrantIdAI = this.asmtFmrk.quadrantIdAI || 0;
    this.asmtFmrk.parititionIdAI = this.asmtFmrk.parititionIdAI || 0;
    this.asmtFmrk.showDocumentsSplitScreen = this.asmtFmrk.showDocumentsSplitScreen || false;
    this.asmtFmrk.showQHeader = this.asmtFmrk.showQHeader || false;
    this.asmtFmrk.nextButtonOpt = this.asmtFmrk.nextButtonOpt || ENextBtnOpt.NEXT;
    if (!this.asmtFmrk.partitions) {
      this.asmtFmrk.partitions = [];
    }
    if (!this.asmtFmrk.quadrants) {
      this.asmtFmrk.quadrants = [];
    }
    if (!this.asmtFmrk.testlets) {
      this.asmtFmrk.testlets = [];
    }
    this.activeAsmtFwrkDimensions = [
      asmtFwrk.primaryDimensions,
      asmtFwrk.secondaryDimensions,
      asmtFwrk.standardParameters
    ];

    this.asmtFmrk.styleProfile = this.itemBankCtrl.styleProfileSelector.value;

    this.publishingCtrl.loadTestDesignReleaseHistory();

    this.questionWordingFc.setValue(this.asmtFmrk.questionWordSlug);

    this.testFormConstructionMethod.setValue(this.asmtFmrk.testFormType || TestFormConstructionMethod.LINEAR);
    if (this.asmtFmrk.testFormType === TestFormConstructionMethod.LINEAR && this.asmtFmrk.partitions) {
      this.itemBankCtrl.selectSection(this.asmtFmrk.partitions[this.asmtFmrk.partitions.length - 1]);
      this.selectQuestionView(QuestionView.FORM_CONSTRUCTION);
    }
    this.quadrantCtrl.refreshQuadrantItems();
  }

  newTag(){
    const slug = prompt('Which tag?')
    if (!this.asmtFmrk.tags){
      this.asmtFmrk.tags = []
    }
    this.asmtFmrk.tags.push({slug})
  }

  newPreservedParam(){
    const slug = prompt('Which parameter?')
    if (!this.asmtFmrk.preservedMetaParams){
      this.asmtFmrk.preservedMetaParams = []
    }
    this.asmtFmrk.preservedMetaParams.push(slug)
  }

  defineNewParameter(arr: IAssessmentFrameworkDimensionDetail[]) {
    arr.push({
      name: 'New Parameter', // ex: Number Sense
      code: '', // ex: NS
      type: DimensionType.BINARY,
      config: {

      }
    });
  }

  isPsychoParam(param: IAssessmentFrameworkDimensionDetail) {
    return param && param.config && param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.PSYCHO_EDIT];
  }

  // only deals with boolean values
  getFrameworkConfigSettingStateBool(asmtProp: string, propConf: {type : 'object' | 'prop', objectProp?: string}) {
    const {type, objectProp} = propConf;
    
    if(type !== 'object') {
      if(!this.asmtFmrk.hasOwnProperty(asmtProp)) this.asmtFmrk[asmtProp] = false;
      return this.asmtFmrk[asmtProp];
    }


    if(!this.asmtFmrk.hasOwnProperty(asmtProp) || (typeof this.asmtFmrk[asmtProp] !== 'object')) {
      this.asmtFmrk[asmtProp] = {};
    }
    if(!this.asmtFmrk[asmtProp][objectProp]) {
      this.asmtFmrk[asmtProp][objectProp] = false;
    }
    return this.asmtFmrk[asmtProp][objectProp];

  }

  getSplitScreenSettingVal(prop: string){
    return this.getFrameworkConfigSettingStateBool('splitScreenSettings', {
      type: 'object',
      objectProp: prop
    })
  }



  getAccSettingConfigVal(settingProp: string, configProp: string) {
    if(!this.asmtFmrk.accessibilitySettings || (typeof this.asmtFmrk.accessibilitySettings !== 'object')) {
      this.asmtFmrk.accessibilitySettings = {};
    }
    if(!this.asmtFmrk.accessibilitySettings[settingProp]) {
      this.asmtFmrk.accessibilitySettings[settingProp] = this.accessibilitySettings[settingProp];
    }
    return this.asmtFmrk.accessibilitySettings[settingProp][configProp];
  }

  getAccSettingConfigurable(settingProp: string) {
    return this.getAccSettingConfigVal(settingProp, 'isConfigurable');
  }

  getAccSettingDefault(settingProp: string) {
    return this.getAccSettingConfigVal(settingProp, 'defaultVal');
  }

  async calculateTestFormParams(){

    const isRealQuestion = (config: IQuestionConfig) => {
      try {
        if(this.whiteLabel.isABED()) {
          const qScoreInfo = this.itemBankCtrl.getQuestionScoringInfo(config.id)
          // has expected answer or human readable with score profile
          return (this.itemBankCtrl.hasExpectedAnswer(config.id) || (qScoreInfo.is_human_scored && !!qScoreInfo.scales.length)) && !config.isReadingSelectionPage
        }
        if(this.whiteLabel.isBCED()){
          return config.meta && config.meta['isQ'] === true;
        }
      }
      catch(e){ /* problematic lookup */ }
      return false;
    }

    const questionById: Map<number, IQuestionConfig> = new Map()
    this.itemBankCtrl.questions.forEach(q => questionById.set(q.id, q));
    try {
      if(this.whiteLabel.isABED() && !this.itemBankCtrl.expectedAnswers.size) await this.itemBankCtrl.loadExpectedAnswers();
    } catch {
      alert('An error occured loading expected answers.')
    }
    try {
      this.itemBankCtrl.loadResponseEntries();
    } catch {
      alert('An error occured loading response entries.')
    }

    let sectionItems: ISectionItemsMap = this.asmtFmrk.sectionItems;
    if (!sectionItems) return;

    let partitions: IAssessmentPartition[] = this.asmtFmrk.partitions;
    if (!partitions) return;

    let student_question = 1;

    // calculate and store student_question 
    Object.keys(sectionItems).forEach(key => {
      sectionItems[key].questions.forEach(question => {
        if ('label' in question && 'id' in question) {
          const config = questionById.get(question.id)
          if(isRealQuestion(config)){
            question['QN'] = student_question++; 
          } else {
            delete question['QN'];
          }
        }
      })
    }); 

  }

  getPartitionPropValue = <K extends keyof IAssessmentPartition>(
    partition: IAssessmentPartition,
    prop: K
  ): IAssessmentPartition[K] => {
    const lang = this.lang.c();
    const useLangSpecificSectionProps = this.asmtFmrk.useLangSpecificSectionProps;
    return getPartitionPropValue(partition, prop, lang, useLangSpecificSectionProps);

  }

  getFormPartitionObj = (partition: IAssessmentPartition) => {

    const lang = this.lang.c();
    const useLangSpecificSectionProps = this.asmtFmrk.useLangSpecificSectionProps;
    return getFormPartitionObj(partition, lang, useLangSpecificSectionProps);

  };

  getAsmtFmrkName = () => {
    return this.lang.c() == 'en' ? this.asmtFmrk.assessmentName : this.asmtFmrk.assessmentNameFr;
  }

  getAsmtFmrkNameProp = (): keyof IAssessmentFrameworkDetail => {
    return this.lang.c() == 'en' ? 'assessmentName' : 'assessmentNameFr'; 
  }
  
  async ensureItemInSection () {
      const framework = this.asmtFmrk;
      if (!framework.sectionItems) {
        framework.sectionItems = {};
      }
      const sectionItems = framework.sectionItems

      const sections = framework.partitions;
      const items = this.itemBankCtrl.getItems()
      const itemsReference = new Map() as Map<number, IQuestionConfig>;
      for (let i = 0; i < items.length; i++) {
        itemsReference.set( items[i].id, items[i] );
      } 

      // Determine items already used in sections
      for (const sectionId in sectionItems) {
        const questions = sectionItems[sectionId].questions;
          for (let i = 0; i < questions.length; i++) {
            if (itemsReference.has(questions[i].id)) {
              itemsReference.delete(questions[i].id)
            }
          }
      }
      
      for (let i = 0; i < sections.length; i++) {
        const specifiedSectionItem = sectionItems[sections[i].id]
        if (!specifiedSectionItem) {
          // Need to add in section items to the newly created Section
          sectionItems[sections[i].id] = {
            questions: []
          };
        }
        const questions = sectionItems[sections[i].id].questions;
        if (!questions.length) {
          const firstKey = itemsReference.keys().next().value
          if (!firstKey) {
            // If no items left, need to create new item
            const question = await this.itemBankCtrl.createNewQuestion(ItemType.ITEM);
            questions.push({
              label: question.label,
              id: question.id
            })

          }
          else {
            questions.push({
              label: itemsReference.get(firstKey).label,
              id: firstKey,
            })
            itemsReference.delete(firstKey);
          }
        }
      }
  }

  async getLatestAsmtStructure(): Promise<{asmtStructure: IAsmtStructuresDB, itemSetSlugMapping: TItemSetSlugMapping}> {
    if(!this.isTestFormScoreEntry()) {
      return;
    }

    return await this.auth.apiGet('public/test-auth/reporting-profiles/assessment-structures', this.itemBankCtrl.customTaskSetId)
  }
}