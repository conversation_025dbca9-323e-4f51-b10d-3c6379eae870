<div class="page-body">
    <div>
        <header
        [breadcrumbPath]="breadcrumb"
        ></header>
        <div class="page-content is-fullpage" >

            <div style="display: flex; flex-direction: row; width: 100%">
                <div>
   
                  <div style="margin-bottom:0em; margin-top: 1.0em; max-width: 80em" class="accounts-table-container">
                    <table class="accounts-table">
                        <tr style="background-color: white" class="header-row">
                            <th *ngFor="let col of columns">
                                <table-header [id] = "col.prop"  [caption] = "col.caption" [ctrl] = "logTable" [disableFilter]="!col.prop"></table-header>
                            </th>
                        </tr>
                        <tr *ngFor ="let entry of logTable.getCurrentPageData()">
                            <td *ngFor="let col of columns">
                                <div [ngSwitch]="col.id">
                                    <ng-container *ngSwitchCase="LogCol.ITEM">
                                        <a [routerLink]="getItemLink(entry)">{{renderProperty(entry, col.id)}}</a>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="LogCol.PREVIEW_ITEM">
                                        <div style="display: flex; flex-direction: row; justify-content: center; align-items: center">
                                            <button (click)="previewItem(entry)" class="button is-small" [class.is-info]="selectedEntry === entry">    
                                                <ng-container *ngIf="selectedEntry === entry"><i class="fa fa-eye-slash"></i></ng-container>
                                                <ng-container *ngIf="selectedEntry !== entry"><i class="fa fa-eye"></i></ng-container>
                                            </button>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngSwitchDefault>
                                        {{renderProperty(entry, col.id)}}
                                    </ng-container>
                                </div>
                            </td>
                        </tr>
                    </table>
                
                        <paginator [model]="logTable.getPaginatorCtrl()" [page]="logTable.getPage()" [numEntries]="logTable.numEntries()"></paginator>
                    </div>
                </div>

                <div *ngIf="selectedEntry && selectedEntry.questionContent">
                    <div style="display: flex; flex-direction: row">
                        <h2 style="margin-right: 0.5em">Item Preview</h2>
                        <a [routerLink]="getItemLink(selectedEntry)" class="button is-small">Edit</a>
                    </div>

                    <div class="section-question-review">
                        <question-runner style="margin-left: 1em"
                        [currentQuestion]="selectedEntry.questionContent"
                        [questionState]="{}">
                        </question-runner>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer [hasLinks]="true"></footer>
</div>