import { Component, OnInit, Input } from '@angular/core';
import { EditingDisabledService } from '../editing-disabled.service';
import { IAsmtStructuresDB, IItemSetStructuresDB, ISeItem, ISEItemSet, ISeItemSlugDefaults, ISESection } from '../widget-score-entry-settings/types';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { indexOf } from '../services/util';

@Component({
  selector: 'widget-rp-assessment-structures',
  templateUrl: './widget-rp-assessment-structures.component.html',
  styleUrls: ['./widget-rp-assessment-structures.component.scss']
})

export class WidgetAssessmentStructuresComponent implements OnInit {
  constructor(
    private editingDisabled: EditingDisabledService,
    private auth: AuthService,
    private routes: RoutesService,
    private login: LoginGuardService
  ) { }

  @Input() reportingProfileId: number;
  @Input() authoringGroupId: number;

  async ngOnInit() {
    await this.loadStructures();
  }

  isPageLoading = false;

  asmtStructCtx: {
    isError: boolean,
    hasChanges: boolean,
    isLoading: boolean,
    data: {asmtStructure: IAsmtStructuresDB} | null
  } = {
    isError: false,
    isLoading: false,
    hasChanges: false,
    data: null
  }

  itemSetStructCtx: {
    isError: boolean,
    hasChanges: boolean,
    isLoading: boolean,
    data: IItemSetStructuresDB[] | null
  } = {
    isError: false,
    isLoading: false,
    hasChanges: false,
    data: null
  }

  async loadStructures() {
    this.isPageLoading = true;

    try {
      await this.loadAsmtStruct();
      await this.loadItemStruct();
      this.loadBackUpMaps();
    } catch (err) {
      this.login.quickPopup(`Something wen't wrong while loading the item structure: ${err.message}`);
      console.error(err);
    } finally {
      this.isPageLoading = false;
    }
  }

  loadBackUpMaps() {
    if(!this.itemSetStructCtx.data) {
      return;
    }

    for (const itemSet of this.itemSetStructCtx.data) {
      this.slugBackUpMap.set(itemSet, itemSet.slug);
      for(const set of itemSet.config.sets) {
        this.slugBackUpMap.set(set, set.slug);
      }
    }
  }

  async loadAsmtStruct() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    this.asmtStructCtx.isLoading = true;
    this.asmtStructCtx.data = await this.auth.apiFind(this.routes.TEST_AUTH_RP_ASSESSMENT_STRUCTURES, {
      query: {
        reporting_profile_id: this.reportingProfileId,
        authoring_group_id: this.authoringGroupId
      }
    }).finally(() => {
      this.asmtStructCtx.isLoading = false;
    })
  }

  async loadItemStruct() {
    if(!this.reportingProfileId || !this.asmtStructCtx.data || !this.asmtStructCtx.data.asmtStructure) {
      return;
    }

    this.itemSetStructCtx.isLoading = true;
    this.itemSetStructCtx.data = await this.auth.apiFind(this.routes.TEST_AUTH_RP_ITEM_SET_STRUCTURES, {
      query: {
        item_set_structure_ids: this.asmtStructCtx.data!.asmtStructure.item_sets.item_set_structure_ids
      }
    }).finally(() => {
      this.itemSetStructCtx.isLoading = false;
    })
  }

  resetItemSetStructCtx() {
    this.itemSetStructCtx = {
      isError: false,
      isLoading: false,
      hasChanges: false,
      data: null
    }
  }

  resetAsmtStructCtx() {
    this.asmtStructCtx = {
      isError: false,
      isLoading: false,
      hasChanges: false,
      data: null
    }
  }


  slugBackUpMap = new Map<any, string>();
  clearBackUpMaps() {
    this.slugBackUpMap.clear();
  }

  onItemSetStructChange() {
    this.itemSetStructCtx.hasChanges = true;
  }

  onAsmtStructChange() {
    this.asmtStructCtx.hasChanges = true;
  }

  onItemSetSlugChange(itemSet: any) {
    if(!this.asmtStructCtx.data?.asmtStructure?.item_sets?.sets) {
      return;
    }

    const oldSlug = this.slugBackUpMap.get(itemSet);
    const newSlug = itemSet.slug;
  
    // Sync any matching set references
    for (const section of this.asmtStructCtx.data.asmtStructure.item_sets.sets) {
      if(section.item_set_structure_slug == oldSlug) {
        section.item_set_structure_slug = newSlug;
      }
    }
  
    // Update backup
    this.slugBackUpMap.set(itemSet, newSlug);
  
    this.onAsmtStructChange();
    this.onItemSetStructChange();
  }

  onSectionSlugChange(item: any) {
    if(!this.asmtStructCtx.data?.asmtStructure?.item_sets?.sets) {
      return;
    }

    const oldSlug = this.slugBackUpMap.get(item);
    const newSlug = item.slug;
  
    // Sync any matching set references
    for (const section of this.asmtStructCtx.data.asmtStructure.item_sets.sets) {
      if(section.set == oldSlug) {
        section.set = newSlug;
      }
    }
  
    // Update backup
    this.slugBackUpMap.set(item, newSlug);
  
    this.onAsmtStructChange();
    this.onItemSetStructChange();
  }
  

  getItemSetStructureSlugs() {
    if(!this.itemSetStructCtx.data) {
      return [];
    }
    return this.itemSetStructCtx.data.map((struc) => struc.slug);
  }

  getItemSetSectionSlugs(structureSlug: string) {
    if(!this.itemSetStructCtx.data) {
      return [];
    }
    const structure = this.itemSetStructCtx.data.find((itemSet) => itemSet.slug == structureSlug);
    if(!structure) {
      return [];
    }

    return structure.config.sets.map((set) => set.slug);
  }

  /*
  * Handles the drop event for rearranging items in a list.
  * Updates the order of items in the specified array and triggers a configuration change notification.
  */
  drop(arr:any, event: CdkDragDrop<string[]>) {
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
    this.onAsmtStructChange();
  }

  removeSection(content:any[], element:any){
    this.login.confirmationReqActivate({
      caption: 'Remove this section?',
      confirm: () => {
        let i = indexOf(content, element);
        if (i !== -1){
          content.splice(i, 1)
        }
      }
    })
    this.onAsmtStructChange();
  }

  removeItemContent(content:any[], element:any){
    this.login.confirmationReqActivate({
      caption: 'Would you like to remove this property?',
      confirm: () => {
        let i = indexOf(content, element);
        if (i !== -1){
          content.splice(i, 1)
        }
      }
    })
    this.onItemSetStructChange();
  }

  addParameter(obj: {[key: string]: any}, promptProperty = 'Parameter') {
    const newKey = prompt(`${promptProperty} name`);
    const newVal = prompt(`${promptProperty} value`);
    if (newVal === null || newKey === null) return;
      
    const numVal = Number(newVal);
    obj[newKey] = !isNaN(numVal) && newVal.trim() !== '' ? numVal : newVal;
    this.onItemSetStructChange();
  }

  removeObjKey(obj: {[key: string]: any}, key: string){
    this.login.confirmationReqActivate({
      caption: 'Would you like to remove this property?',
      confirm: () => {
        delete obj[key];
      }
    })
    this.onItemSetStructChange();
  }

  updateProperty(promptString: string, key: string, obj: { [key: string]: any }) {
    const newVal = prompt(promptString, obj[key]);
    if (newVal === null) return;
  
    const numVal = Number(newVal);
    obj[key] = !isNaN(numVal) && newVal.trim() !== '' ? numVal : newVal;
    this.onItemSetStructChange();
  }

  updateKey(promptString: string, oldKey: string, obj: { [key: string]: any }) {
    const newKey = prompt(promptString, oldKey);
    if (newKey === null || newKey.trim() === '' || newKey === oldKey) return;
    
    if (!(oldKey in obj)) {
      console.warn(`Key "${oldKey}" does not exist.`);
      return;
    }
  
    // Move the value to the new key and delete the old key
    obj[newKey] = obj[oldKey];
    delete obj[oldKey];
  
    this.onItemSetStructChange();
  }


  addItemSlugDefault(itemSlugDefaults: ISeItemSlugDefaults) {
    const newSlug = prompt('Enter item slug');
    if (newSlug === null) return;
    const newDefault = {
      caption: {en: "", fr: ""},
      item_params: {},
    }

    itemSlugDefaults[newSlug]=newDefault;
  }

  updateCaption(key: string, obj: { [key: string]: {en: string, fr: string} }) {
    const newEN = prompt('Enter an EN translation', obj[key]['en']);
    const newFR = prompt('Enter an FR translation', obj[key]['fr']);
    if (newEN === null || newFR === null) return;
  
    if(!obj) {
      obj = {};
    }

    obj[key]['en'] = newEN;
    obj[key]['fr'] = newFR;
    this.onItemSetStructChange();
  }

  addCaption(obj: IItemSetStructuresDB, key: string ) {
    const newKey = prompt(`Enter a new caption slug`);
    if (newKey === null) return;

    if(!obj[key]) { // Initialize item captions if it doesn't exist
      obj[key] = {};
    }

    obj[key][newKey] = {en: '', fr: ''};
    this.onItemSetStructChange();
  }

  addItem(arr: any[]) {
    const defaultItem: ISeItem = {
      slug: "",
      item_params: {},
      val_max: 0,
      caption: {en: '', fr: ''}
    }

    arr.push(defaultItem);
    this.onItemSetStructChange();
  }

  addSection(arr: any[]) {
    const defaultSection: ISEItemSet = {
      slug: "",
      items: [],
      caption: {en: "", fr: ""},
      item_params: {},
    }

    arr.push(defaultSection);
    this.onItemSetStructChange();
  }

  addPartition(arr: any[]) {
    const defaultSection: ISESection = {
      set: "",
      item_set_structure_slug: ""
    }

    arr.push(defaultSection);
    this.onAsmtStructChange();
  }

  isSaving = false;
  async saveChanges() {
    if(!this.itemSetStructCtx.data || !this.asmtStructCtx.data) {
      return this.login.quickPopup('No data to save');
    }

    this.isSaving = true;

    try {
      // Create new item set structure rows and store old and new IDs to update asmt struct
      const oldNewIdMapping = {};
      for(const itemSet of this.itemSetStructCtx.data) {
        const newItemSetStruct = await this.auth.apiCreate(this.routes.TEST_AUTH_RP_ITEM_SET_STRUCTURES, itemSet)
        oldNewIdMapping[itemSet.id] = newItemSetStruct.id;
      }

      // Update asmt struct IDs then patch
      const oldIds: number[] = [];
      while(this.asmtStructCtx.data.asmtStructure.item_sets.item_set_structure_ids.length > 0) {
        const oldId = this.asmtStructCtx.data.asmtStructure.item_sets.item_set_structure_ids.pop();
        if(oldId != undefined) {
          oldIds.push(oldId);
        }
      }
      oldIds.forEach((id) => {
        const newId = oldNewIdMapping[id] ?? id;
        this.asmtStructCtx.data!.asmtStructure.item_sets.item_set_structure_ids.push(newId)
      })

      const asmtStructure = this.asmtStructCtx.data.asmtStructure;
      await this.auth.apiPatch(this.routes.TEST_AUTH_RP_ASSESSMENT_STRUCTURES, asmtStructure.id, asmtStructure);
      await this.upgradeReportingProfile();

      await this.resetView();
    } catch (err) {
      this.login.quickPopup(`Unable to save changes: ${err.message}`)
      console.error(err);
    } finally {
      this.isSaving = false;
    }
  }

  async resetView() {
    this.resetAsmtStructCtx();
    this.resetItemSetStructCtx();
    this.clearBackUpMaps();
    await this.loadStructures();
  }

  /**
   * Helper function to upgrade the reporting profile and reload the data.
   * @returns 
   */
  async upgradeReportingProfileManual() {
    if(this.asmtStructCtx.data.asmtStructure.id == this.asmtStructCtx.data.asmtStructure.latest_id) {
      return;
    }

    await this.upgradeReportingProfile();
    await this.resetView();
  }

  /**
   * Used to upgrade the reporting profile to the latest assessment structure ID
   * @returns the newly updated reporting profile
   */
  async upgradeReportingProfile() {
    if(!this.reportingProfileId || !this.authoringGroupId) {
      return;
    }

    return await this.auth.apiUpdate(this.routes.TEST_AUTH_RP_ASSESSMENT_STRUCTURES, this.reportingProfileId, {})
  }

  /**
   * Used to upgrade the assessment structure to the latest item set ID and reload the data.
   * @param itemSet the item set that requires upgrading
   */
  async upgradeAsmtStructure(itemSet: IItemSetStructuresDB) {
    if(itemSet.id == itemSet.latest_id || !itemSet.latest_id) {
      return;
    }
    
    await this.auth.apiUpdate(this.routes.TEST_AUTH_RP_ITEM_SET_STRUCTURES, this.asmtStructCtx.data.asmtStructure.id, {
      current_id: itemSet.id,
      latest_id: itemSet.latest_id
    })

    await this.resetView();
  }

  isAsmtStructureOutdated() {
    return this.asmtStructCtx.data.asmtStructure.id != this.asmtStructCtx.data.asmtStructure.latest_id
  }

  isItemStructureOutdated(itemSet: IItemSetStructuresDB) {
    return itemSet.id != itemSet.latest_id
  }

  hasChanges() {
    return this.asmtStructCtx.hasChanges || this.itemSetStructCtx.hasChanges;
  }
  
}
