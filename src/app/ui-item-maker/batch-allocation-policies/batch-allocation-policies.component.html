<div style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
  
  <div *ngIf="!isEditing">
      <button class="button is-small" (click)="createBatchAllocationPolicyModal()">Create New Policy</button>
      <button class="button is-small" [disabled]="!selectedBatchAllocTable" (click)="startEditingPolicy()">Edit Score Options and Read Rules</button>
  </div>
  <div *ngIf="isEditing">
    <button class="button is-small" (click)="cancelEditingPolicy()">Return</button>
    <button class="button is-small" (click)="switchPolicy()">Previous Policy</button>
    <button class="button is-small" (click)="switchPolicy(true)">Next Policy</button>
  </div>
</div>
<div style="margin-bottom:1em;">
  <span>Authoring Group: </span>
  <select [(ngModel)]="selectedAgGroupId" (change)="updateSelectedBatchGroup()">
    <option
      [ngValue]="null"
    >All</option>
    <option
      *ngFor="let agGroup of agGroups"
      [ngValue]="agGroup.group_id"
    >{{agGroup.name}}</option>
  </select>
</div>

<ng-container *ngIf="!isEditing">
  <div class="space-between align-top">
    <ag-grid-angular
      style="flex-grow: 1;"
      class="ag-theme-alpine ag-grid-fullpage"
      [rowData]="filteredPolicies"
      [gridOptions]="batchAllocGridOption"
      [enableCellTextSelection]="true"
      (selectionChanged)="onEditingForScorer($event)">
      <!-- [columnDefs]="columnDefs" -->
    </ag-grid-angular>
    <div 
      *ngIf="selectedBatchAllocTable" 
      class = "table-side-panel" 
      style="display:flex; flex-direction:column; margin-left:1rem;">
      
        <div>
          <!-- ---Show edit button--- -->
          <ng-container *ngIf="!isEditingSelectedBatchAllocTable()">
            <button 
              style="margin-bottom: 1rem;"
              (click)="startEditing()"
              class="button is-small btn-download-report" 
            >
              Edit Batch Allocation Table
            </button>             
          </ng-container>
  
          <!-- ---Show Cancel and Save button if not editing--- -->
          <ng-container *ngIf="isEditingSelectedBatchAllocTable()">
              <button 
                (click)="cancelEdit()" 
                [disabled]="selectedBatchAllocTable.__isSaving" 
                class="button is-small cancel-btn">
                  Cancel
              </button>
              <button 
                (click)="saveEdit()"  
                [disabled]="selectedBatchAllocTable.__isSaving || !isEditFormValid()" 
                class="button is-small save-btn">
                  Save
              </button>
          </ng-container>
        </div>
        
        <table>
          <tr>
              <td>Batch Allocation ID</td>
              <td><code>{{ selectedBatchAllocTable.id }}</code></td>
          </tr>
          <tr>
            <td>Description</td>
            <td>
              <textarea
                [(ngModel)]="selectedBatchAllocTable.description"
                [disabled]="!selectedBatchAllocTable.__isEditing">
              </textarea>
            </td>
            
            
          </tr>
          <tr>
            <td>Max Batch Number Claim</td>
            <td>
              <code>
                {{selectedBatchAllocTable.max_batch_num_claim}}
              </code>
            </td>
          </tr>
          <tr>
            <td>Batch Size</td>
            <td>
              <input
                [(ngModel)]="selectedBatchAllocTable.batch_size"
                [disabled]="!selectedBatchAllocTable.__isEditing">
                <p 
                  class="error"
                  *ngIf="!isBatchSizeValid()"
                  >Input must be from 1 to {{maxBatchSizeAllowed}}
                </p>
            </td>
          </tr>
          <tr>
            <td>Claim Duration Hours</td>
            <td>
              <input
                [(ngModel)]="selectedBatchAllocTable.claim_dur_hours"
                [disabled]="!selectedBatchAllocTable.__isEditing">
                <p 
                  class="error"
                  *ngIf="!isClaimDurationHoursValid()"
                  >Input must be from 1 to 24
                </p>
            </td>
          </tr>
          <tr>
            <td>Same School Limit Rate</td>
            <td>
              <input
                [(ngModel)]="selectedBatchAllocTable.same_sch_lim_rate"
                [disabled]="!selectedBatchAllocTable.__isEditing">
                <p 
                  class="error"
                  *ngIf="!isSchoolLimitRateValid()"
                  >Input must be from 1 to 100
                </p>
            </td>
          </tr>
          <tr>
            <td>Standards Confirming Range Tags</td>
            <td style="display: flex; flex-direction: column; gap: 1em">
              <div *ngFor="let rangeCategory of rangeCategories" style="border-radius: 5px; padding: 0.5em" [ngStyle]="{ 'background': rangeCategory.bgColor }">
                <div *ngIf="rangeCategory.caption">
                  <b><tra [slug]="rangeCategory.caption"></tra></b>
                </div>
                <div *ngFor="let rangeTag of rangeCategory.ranges">
                  <input type="checkbox" [(ngModel)]="rangeTagSelections[rangeTag.id]" [disabled]="!selectedBatchAllocTable.__isEditing"/>
                  <label>
                    &nbsp;&nbsp;
                    <tra [slug]="rangeTag.caption_slug"></tra>
                  </label>
                </div>
              </div>
            </td>
          </tr>
          <tr>
            <td>Allow clearing a score</td>
            <td>
              <span style="padding-right: 0.5em">No</span>
              <mat-slide-toggle 
                [(ngModel)]="selectedBatchAllocTable.allow_score_clear"
                [disabled]="!selectedBatchAllocTable.__isEditing"
                color="primary"
              ></mat-slide-toggle>
              <span style="padding-left: 0.5em">Yes</span>
            </td>
          </tr>
          <tr>
            <td>Allow free navigation to Next Response in batch</td>
            <td>
              <span style="padding-right: 0.5em">No</span>
              <mat-slide-toggle 
                [(ngModel)]="selectedBatchAllocTable.allow_batch_next_navigate"
                [disabled]="!selectedBatchAllocTable.__isEditing"
                color="primary"
              ></mat-slide-toggle>
              <span style="padding-left: 0.5em">Yes</span>
            </td>
          </tr>
        </table>
  
    </div>
  </div>
</ng-container>
<ng-container *ngIf="isEditing">
  <batch-allocation-policies-editor
  [batchAllocationPolicyId]="selectedBatchAllocTable.id"
  [description]="selectedBatchAllocTable.description"
  >

  </batch-allocation-policies-editor>
</ng-container>
<div *ngIf="isCreatingPolicy" class="modal-container">
  <div class="modal-contents">
    <h1>Create New Scoring Policy</h1>
    <div *ngIf="hasAuthoringGroupSuprAccess()">
      <span>Select authoring group with supervisor access: </span>
      <br>
      <select [(ngModel)]="selectedAgGroupId" (change)="updateSelectedBatchGroup()">
        <option
          *ngFor="let agGroup of agGroupWithSuprAccess"
          [ngValue]="agGroup.group_id"
        >{{agGroup.name}}</option>
      </select>
    </div>
    <div *ngIf="!hasAuthoringGroupSuprAccess()">
      Create Custom Authoring Group
      <check-toggle 
          [isChecked]="createCustomAuthoringGroup"
          (toggle)="toggleCreateCustomAuthoringGroup()"
      ></check-toggle>
    </div>
    <div class="flex" style="display:flex; justify-content:flex-end;">
      <button mat-flat-button (click)="cancelCreatingPolicy()">Cancel</button>
      <button [disabled]="(!hasAuthoringGroupSuprAccess() && !createCustomAuthoringGroup) || (hasAuthoringGroupSuprAccess() && !selectedAgGroupId)" 
      mat-flat-button 
      (click)="createBatchAllocationPolicy()" color="primary">
        <tra slug="lbl_confirm"></tra>
    </button>
    </div>
  </div>
</div>