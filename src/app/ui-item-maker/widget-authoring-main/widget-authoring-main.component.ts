// app services
import { AdvancedSettings } from '../item-set-editor/models/advanced-settings';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { AuthService } from '../../api/auth.service';
import { CdkDrag } from '@angular/cdk/drag-drop';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { DropdownService } from '../dropdown.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { EditViewMode } from '../item-set-editor/models/types';
import { Subscription, Subject } from 'rxjs';
import { elementIconById } from '../item-set-editor/models';
import { elementTypes } from "../../ui-testrunner/models/ElementTypeDefs";
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { IContentElement, IElementTypeDef, IItemSetResponse, IQuestionConfig, ISequenceConfig } from '../../ui-testrunner/models/index';
import { LoginGuardService } from '../../api/login-guard.service';
import { IItemTag } from '../item-tag/item-tag.component';
import { IItemTypeDef, ItemType, ItemTypeDefs, itemTypes, CustomButtonPos } from '../models';
import { installPatch } from '../../patches/nested-drag-drop-patch';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { LangService } from '../../core/lang.service';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { PARAM_SPECIAL_FLAGS } from '../framework-dimension-editor/model';
import { StyleprofileService } from '../../core/styleprofile.service';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { TextToSpeechService } from '../../ui-testrunner/text-to-speech.service';
import { TwiddleState } from '../../ui-partial/twiddle/twiddle.component';
import { getElVoice, getVoiceChange } from 'src/app/io-audio/capture-voice/capture-voice.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { mtz } from '../../core/util/moment';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
import { WidgetAuthoringConnectionService } from '../widget-authoring-connection.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { ActivatedRoute } from '@angular/router';


export enum ModalType {
  CHANGE_LOG = "CHANGE_LOG"
}
import { ElementType } from "src/app/ui-testrunner/models/index";
import { IAuthoringGroup, ItemMakerService } from '../item-maker.service';
import { RoutesService } from 'src/app/api/routes.service';
import { IAuthRestrictions } from '../item-set-editor/item-set-editor.component';
import { VoiceoverStateService } from 'src/app/ui-testrunner/voiceover-state.service';
import { PARAMETER_LOCATIONS } from '../view-im-parameters/view-im-parameters.component';


@Component({
  selector: 'widget-authoring-main',
  templateUrl: './widget-authoring-main.component.html',
  styleUrls: ['./widget-authoring-main.component.scss']
})
export class WidgetAuthoringMainComponent implements OnInit, OnDestroy {

  @Input() assetLibraryCtrl: AssetLibraryCtrl
  @Input() frameworkCtrl: ItemSetFrameworkCtrl
  @Input() auditCtrl: ItemBankAuditor
  @Input() itemBankCtrl: ItemBankCtrl
  @Input() itemEditCtrl: ItemEditCtrl
  @Input() itemFilterCtrl: ItemFilterCtrl
  @Input() memberAssignmentCtrl: MemberAssignmentCtrl
  @Input() panelCtrl: PanelCtrl
  @Input() previewCtrl: ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl: ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Input() authRestrictions: IAuthRestrictions = {
    isCurrentUserRestricted: true,
    isCurrentUserTemplateManager: false
  };
  @ViewChild('secureDataUpload', { static: false }) secureDataUpload: ElementRef;
  @ViewChild('tagInput') tagInput: ElementRef;

  EditViewMode = EditViewMode;

  PARAMETER_LOCATIONS = PARAMETER_LOCATIONS;

  IS_TRACK_CHANGES_DISABLED = false; // temp
  
  ItemType = ItemType;
  ItemTypeDefs = ItemTypeDefs;
  getElVoice = getElVoice;
  getVoiceChange = getVoiceChange;
  ElementType = ElementType;
  isViewingArchived = false;
  showAdvancedSettings = false;
  isEditingSetName = false;
  itemPropsTwiddle = new TwiddleState(false);
  advancedItemPropsTwiddle = new TwiddleState(false);
  scoringItemPropsTwiddle = new TwiddleState(false);
  sequenceCommentsTwiddle = new TwiddleState(false);
  commentsTwiddle = new TwiddleState(true);
  pinnedCommentsTwiddle = new TwiddleState(false);
  rubricGrid: any[] = [];
  advancedSettings = AdvancedSettings;
  personalEditorSettings = {};
  rawResponseTableDataSource;
  dataSource;
  displayedColumns = ['response_ID', 'response'];
  isHighContrast: boolean = false;
  isWebsocketDisconnected: boolean = false;
  inactiveSubject = new Subject<any>();
  inactiveSub: Subscription;
  websocketDisconnectedSub: Subscription;
  inactiveTimeout;
  inactivityTimerDuration = 1800000; // 30 minutes
  createNewQuestionSub: Subscription;
  selectQuestionSub: Subscription;
  updatedItemSub: Subscription;
  userTimeoutSub: Subscription;
  autoSaveSub: Subscription;
  profiles: any[];
  pageModal: PageModalController;
  ModalType = ModalType;
  isItemTagsActive = false;
  isEditingWorkflowActive = false;
    
  public util = new ItemBankUtilCtrl();
  archivedQuestions: IQuestionConfig[] = [];
  cachedQuestions: IQuestionConfig[] = [];
  isLoadingArchived: boolean;
  constructor(
    private textToSpeech: TextToSpeechService,
    private onlineOrPaper: OnlineOrPaperService,
    private editingDisabled: EditingDisabledService,
    public profile: StyleprofileService,
    public dropdown: DropdownService,
    public lang: LangService,
    private auth: AuthService,
    private whiteLabel: WhitelabelService,
    private loginGuard: LoginGuardService,
    public itemComponentEdit: ItemComponentEditService,
    private authScopeSettings: AuthScopeSettingsService,
    public ws: WidgetAuthoringConnectionService,
    private pageModalService: PageModalService,
    public itemMakerService: ItemMakerService,
    public routes: RoutesService,
    private voiceoverState: VoiceoverStateService,
    private route: ActivatedRoute,
  ) { }

  queryParamsSub: Subscription;

  CustomButtonPos = CustomButtonPos;

  async ngOnInit(): Promise<void> {

    this.pageModal = this.pageModalService.defineNewPageModal();
    try { this.previewCtrl.updateDragZoomCorrection(); } catch(e){}
    this.profiles = await this.profile.getProfileOptions();

    //Required to fix cdk drag drop with nested drop lists (From https://github.com/angular/components/issues/16671)
    installPatch(); // todo: remove this once the issue is fixed

    this.ws.connect();
    this.initializeUserInactivityChecker();

    this.itemMakerService.isRolesLoaded.subscribe(() => this.loadElementTypes())

    this.updatedItemSub = this.ws.updatedItemSub.subscribe((item) => {
      if (!this.itemBankCtrl.currentQuestion) return;

      const selectedItems = this.ws.selectedItems[item["itemId"]];
      if (!selectedItems) return;

      const userHasItemSelected = this.ws.prevSelectedItemId === item["itemId"];
      const userSentSaveRequest = (this.ws.userConnectionId === item["connectionId"]);
      if (userHasItemSelected && !userSentSaveRequest) {
        this.showItemHasUpdatedAlert(item["connectionId"]);
        this.itemBankCtrl.reloadCurrentQuestion();
      }
    });
    this.inactiveSub = this.inactiveSubject.subscribe(_ => {
      this.ws.reconnectInactiveUser();
    });
    this.autoSaveSub = this.saveLoadCtrl.autoSaveSub.subscribe(_ => {
      if (!this.itemBankCtrl.isEditActivationPending()) {
        this.wsSaveItem();
      }
    });
    this.websocketDisconnectedSub = this.ws.websocketDisconnectedSub.subscribe((isDisconnected: boolean) => {
      this.isWebsocketDisconnected = isDisconnected;
    });
    this.selectQuestionSub = this.itemBankCtrl.selectQuestionSub.subscribe((question: IQuestionConfig) => {
      if (question && this.ws.prevSelectedItemId !== question.id) {
        this.ws.selectItem(question.id);
      }
    });
    this.userTimeoutSub = this.ws.userTimeoutSub.subscribe(_ => {
      if (!this.isEditActivationPending()) {
        this.saveLoadCtrl.autoSaveQuestionData();
        this.editingDisabled.isManualEditingEnabled = false;
      }
      this.loginGuard.quickPopup("You have been disconnected for inactivity. Start editing again to reconnect.");
      this.itemBankCtrl.deselectQuestions();
    });
    this.createNewQuestionSub = this.itemBankCtrl.createNewQuestionSub.subscribe(newQuestion => {
      this.selectQuestion(newQuestion);
    });
    if (this.itemBankCtrl.editModeItemId) {
      const questions = [];
      this.itemFilterCtrl.getSideItemList().forEach(i => {
        if (i.type === 'item') {
          questions.push(i)
        } else if (i.type === 'sequence') {
          (i as ISequenceConfig).children.forEach(q => questions.push(q))
        }
      })
      const question = questions.filter(q => q.id === this.itemBankCtrl.editModeItemId)[0];
      setTimeout(() => {
        this.itemBankCtrl.selectQuestion(question);
      }, 0)
    }
    this.queryParamsSub = this.route.queryParams.subscribe(params => {
      this.itemBankCtrl.testWindowAllocRuleId = params['twtar_id'] || null;
      const isStartOnStats = params['formStats'] || null;
      if (isStartOnStats && this.itemBankCtrl.testWindowAllocRuleId){
        this.itemBankCtrl.switchToFormStatisticsView(); // todo: we might get a race condition from trying to trigger this too soon
      }
    });
  }

  ngOnDestroy(): void {
    this.itemComponentEdit.reset();
    this.ws.cleanupOnDestroy();
    this.cleanupUserInactivityChecker();
    this.queryParamsSub.unsubscribe();
  }


  isRightPanelExpanded() {
    return this.authScopeSettings.isRightPanelExpanded
  }
  toggleRightPanelExpanded() {
    this.authScopeSettings.isRightPanelExpanded = !this.authScopeSettings.isRightPanelExpanded
  }

  getQuestionLastEdit() {
    this.itemBankCtrl
  }

  toggleTextToSpeech = () => this.textToSpeech.toggle();
  toggleOnlineOrPaper = () => this.onlineOrPaper.toggle();
  isTextSpeechActive = () => this.textToSpeech.isActive;
  isOnlineOrPaperActive = () => this.onlineOrPaper.isPaper;
  isSidePanelVisible = () => false;
  isSidePanelExpanded = () => false;
  createNewConversation() { }
  resolveComment(comment) { }
  isReadOnly = (ignoreCurrQLock: boolean = false, ignoreEditingDisabled: boolean = false) => {
    // if (this.isEditActivationPending()){
    //   return true;
    // }
    return this.editingDisabled.isReadOnly(ignoreCurrQLock, ignoreEditingDisabled);
  }
  elementTypes: IElementTypeDef[];
  itemTypes: IItemTypeDef[] = itemTypes;

  refreshPinnedTrigger: number = 0;

  ITEM_RERENDER_TIME_BUDGET = 600;

  /**
   * Load element types according to the current author's role access.
   */
  loadElementTypes() {
    try {
      if(!this.authRestrictions.isCurrentUserRestricted) { // If group is not restricted or user is an advanced author, load all blocks
        this.elementTypes = elementTypes;
        return;
      }
      // If user is a regular author, load unrestricted blocks.
      const unrestrictedElementTypes = elementTypes.filter((type) => !type.isAccessRestricted);
      this.elementTypes = unrestrictedElementTypes;
    } catch(err) {
      console.error(err);
      this.elementTypes = elementTypes;
    }
  }

  assignButtonHandler($event) {
    console.log($event);
    this.memberAssignmentCtrl.openAssignUser($event);
  }

  isEditActivationPending = () => this.itemBankCtrl.isEditActivationPending();
  activateManualEditing = async () => this.itemBankCtrl.activateManualEditing();
  finishManualEditing = async () => this.itemBankCtrl.finishManualEditing();
  isManualEditApplicable = () => this.itemBankCtrl.isManualEditApplicable();
  toggleManualEdit = async () => {
    if (this.isWebsocketDisconnected) {
      this.showWebsocketDisconnectedAlert();
    }
    if (this.isEditActivationPending() && this.isQuestionSelectedByOtherUser()) {
      this.showItemIsBeingEditedAlert(this.ws.prevSelectedItemId);
    }

    this.itemBankCtrl.toggleManualEdit();
    this.wsSaveItem();
  }
  isHeldForEditing = () => {
    return this.isManualEditApplicable() && !this.isEditActivationPending();
  }

  getIconByElementTypeId(elementTypeId: string) {
    return elementIconById.get(elementTypeId);
  }

  isNewItemDropdownActive() {
    return this.dropdown.isNewItemDropdownActive;
  }

  toggleNewItemDropdown(event) {
    return this.dropdown.toggleNewItemDropdown(event);
  }

  allDropListIds() {
    return this.itemBankCtrl.dropListIds;
  }

  allowedToDropIntoSeq = (drag: CdkDrag, drop) => {
    return drag.data.type !== ItemType.SEQUENCE
  }

  collapseSequence(seq: ISequenceConfig) {
    this.itemBankCtrl.setSequenceCollapsed(seq, !seq.isCollapsed);
  }

  currentQuestionIsSequence() {
    return this.itemBankCtrl.currentQuestionIsSequence();
  }

  getCurrentQuestionType() {
    return ItemTypeDefs[this.itemBankCtrl.currentQuestion.type].caption;
  }

  isViewable(param) {
    return this.authScopeSettings.isItemParamViewable(param)
  }

  getCurrentQuestionLabelSlug(): string {
    const id = ItemTypeDefs[this.itemBankCtrl.currentQuestion.type].id;
    let slug = '';
    if (id === ItemType.ITEM) {
      slug = 'auth_item_label';
    } else if (id === ItemType.SEQUENCE) {
      slug = 'auth_sequence_label';
    }
    return slug;
  }

  getCurrentQuestionUpdatedOn() {
    const question = this.itemBankCtrl.currentQuestion;
    if (question && question.updated_on) {
      return mtz(question.updated_on).fromNow();
    }
    return '---'
  }

  getCurrentQuestionIDSlug(): string {
    const id = ItemTypeDefs[this.itemBankCtrl.currentQuestion.type].id;
    let slug = '';
    if (id === ItemType.ITEM) {
      slug = 'ie_item_id';
    } else if (id === ItemType.SEQUENCE) {
      slug = 'auth_sequence_id';
    }
    return slug;
  }

  getRemoveItemLabelSlug(): string {
    const id = ItemTypeDefs[this.itemBankCtrl.currentQuestion.type].id;
    let slug = '';
    if (id === ItemType.ITEM) {
      slug = 'auth_remove_item';
    } else if (id === ItemType.SEQUENCE) {
      slug = 'auth_remove_sequence';
    }
    return slug;
  }

  deselectQuestions(event) {
    // if(event.target.classList.contains("allow-deselect-question")) {
    //   this.itemBankCtrl.deselectQuestions();
    // }
  }

  isNewItemDisabled() {
    const isLockedSequenceQuestion = (this.currentQuestionIsSequence() || this.itemBankCtrl.getParent(this.itemBankCtrl.currentQuestion)) && this.itemBankCtrl.isLocked(this.itemBankCtrl.currentQuestion)
    return (
      this.isReadOnly(true, true) || 
      this.saveLoadCtrl.isLoadingQuestion || 
      isLockedSequenceQuestion || 
      (this.isManualEditApplicable() && this.editingDisabled.isManualEditingEnabled)
    )
  }

  canDeleteReadSel(i: number) {
    return !this.itemBankCtrl.isContentEditingDisabled() && this.itemBankCtrl.isOwnReadSel(this.itemBankCtrl.getCurrentQuestionContent(), i)
  }

  closeEditMode() {
    this.updatePreviewCtrlData();

    this.itemBankCtrl.isPsychometricViewEnabled = true;
    this.itemBankCtrl.editModeItemId = null;

    // this.previewCtrl.previewLinearTestForm(); 
  }

  /**
   * Update the questionSrcDb with changes made in the edit item mode
   */
  updatePreviewCtrlData(){
    const questions = this.getSideItemConfigList();

    let editModeItemConfig = questions.filter(q => q.id === this.itemBankCtrl.editModeItemId)[0];
    if (editModeItemConfig){
      const configToOverwrite = this.getNewItemConfigToOverwrite(editModeItemConfig);
      this.previewCtrl.sampleTestForm.questionSrcDb.set(editModeItemConfig.id, configToOverwrite)
    }

    const associatedResourceConfigs = this.getEditModeItemReadingSelections();

    // If item has associated resource/read selection, update the item config in the questionSrcDb as well.
    if (associatedResourceConfigs && associatedResourceConfigs.length > 0){
      associatedResourceConfigs.forEach(config => {
        if (this.previewCtrl.sampleTestForm.questionSrcDb.has(config.id)){
          const configToOverwrite_associated = this.getNewItemConfigToOverwrite(config);
          this.previewCtrl.sampleTestForm.questionSrcDb.set(config.id, configToOverwrite_associated)
        }
      })
    }
  }

  /**
   * Helper method to get the correct item config (EN or FR) to update, to the questionSrcDb used in the 
   * test taker preview. If the current language is French the use the data inside the 'langlink'.
   * @param config The latest item config
   * @returns the correct config data to update
   */
  getNewItemConfigToOverwrite(config: IQuestionConfig){
    let newConfig = config;
    // If currently viewing in French, use the French item config instead, which is inside the 'langLink'
    // of the main (English) item config
    if (this.lang.c() == 'fr' && config.langLink){
      const itemId = config.id;     //Preserve the item id as the French item config does not contain the item id
      newConfig = config.langLink;
      newConfig.id = itemId
    }

    return newConfig;
  }

  refreshPinned() {
    this.refreshPinnedTrigger++;
  }

  isOwnRevision(revision: any) {
    const uid = this.auth.getUid();
    if (!uid || uid == -1) {
      return false;
    }
    return revision && uid == revision.created_by_uid;
  }

  writeRevisionMessage(revision: any) {
    const message = prompt("Message");
    this.saveLoadCtrl.saveRevisionMessage(revision, message);
  }

  getParamDisplay(param) {
    return param.isHidden || !this.isViewable(param) ? 'none' : ''
  }

  addTag(tag: IItemTag) {
    this.itemBankCtrl.addTag(tag);
    this.tagInput.nativeElement.blur();
  }

  gotoPrevQuestion() {
    if (this.isHeldForEditing()) return alert('Save your edits before moving on.');
    const questions = this.itemFilterCtrl.getSideItemList();
    let activeQuestionIndex = questions.findIndex(q => q.id === this.itemBankCtrl.currentQuestion.id);
    if (activeQuestionIndex < 0) {
      let previousChildQuestion;
      questions.forEach((question, index) => {
        if (question.children && question.children.length > 0) {
          let childIndex = question.children.findIndex(q => q.id === this.itemBankCtrl.currentQuestion.id)
          if (childIndex > -1 && childIndex - 1 >= 0) {  // If not the first child in sequence, proceed to the previous child
            previousChildQuestion = question.children[childIndex - 1]
          } else if (childIndex > -1) {
            activeQuestionIndex = index; // If first child in sequence, update activeQuestionIndex
          }
        }
      })
      if (previousChildQuestion) {
        this.itemBankCtrl.selectQuestion(previousChildQuestion);
        return;
      }
    }
    const prevQ = questions[activeQuestionIndex - 1];
    if (!prevQ) return;
    if (prevQ.children && prevQ.children.length > 0) {
      this.itemBankCtrl.selectQuestion(prevQ.children[prevQ.children.length - 1]);
      return;
    }
    this.itemBankCtrl.selectQuestion(prevQ);
  }

  gotoNextQuestion() {
    if (this.isHeldForEditing()) return alert('Save your edits before moving on.');
    const questions = this.itemFilterCtrl.getSideItemList();
    let activeQuestionIndex = questions.findIndex(q => q.id === this.itemBankCtrl.currentQuestion.id);
    // Find if question is inside of a sequence
    if (activeQuestionIndex < 0) {
      let nextChildQuestion;
      questions.forEach((question, index) => {
        if (question.children && question.children.length > 0) {
          let childIndex = question.children.findIndex(q => q.id === this.itemBankCtrl.currentQuestion.id)
          if (childIndex > -1 && question.children.length > childIndex + 1) {  // If not the last child in sequence, proceed to the next child
            nextChildQuestion = question.children[childIndex + 1]
          } else if (childIndex > -1) {
            activeQuestionIndex = index; // If last child in sequence, update activeQuestionIndex
          }
        }
      })
      if (nextChildQuestion) {
        this.itemBankCtrl.selectQuestion(nextChildQuestion);
        return;
      }
    }
    const nextQ = questions[activeQuestionIndex + 1];
    if (!nextQ) return;
    if (nextQ.children && nextQ.children.length > 0) {
      this.itemBankCtrl.selectQuestion(nextQ.children[0]);
      return;
    }
    this.itemBankCtrl.selectQuestion(nextQ);
  }

  toolbarEditingColumns = ["Format", "Code", "Example Code", "Example View"]
  twiddleTable = new TwiddleState(false);

  getToolbarCommands() {
    const rows = [
      ["Bold", "**", "**Example**", "<strong>Example<strong>"],
      ["Italic", "*", "*Example*", "<i>Example</i>"],
      ["Underline", "<u> </u>", "<u>Example</u>", "<u>Example</u>"],
      ["Strikethrough", "<strike> </strike>", "<strike>\nExample\n</strike>", "<strike>Example</strike>"],
      ["Superscript", "^", "Example^2", "Example<sup>2</sup>"],
      ["Subscript", "-", "Example_2", "Example<sub>2</sub>"],
      ["Invisible character", "&zwnj;", "url_&zwnj;website", "url_website"],
      ["Non-breakable space", "&nbsp;", "Example&nbsp;2", "Example&nbsp;2"],
      ["Em Dash", "&mdash;", "Example&mdash;2", "Example&mdash;2"],
      ["En Dash", "&ndash;", "Example&ndash;2", "Example&ndash;2"],
      ["Degree", "&deg;", "25&deg;C", "25&deg;C"],
      ["Triangle", "\\triangle (math block)", "\\triangle ABC", "△ ABC"],
      ["Angle", "\\angle (math block)", "\\angle ABC", "∠ ABC"]
    ]
    return rows
  }

  suggestionsEnabled() {
    return this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_SUGGESTIONS);
  }

  containsSpecial(param, spec: PARAM_SPECIAL_FLAGS) {
    if (param.config.special && param.config.special[spec]) {
      return true
    }
    return false
  }

  isWordCount(param) {
    return this.containsSpecial(param, PARAM_SPECIAL_FLAGS.WORD_COUNT)
  }

  getFilteredDims(dims: any[]) {
    const filtered = []
    dims.forEach((dim) => {
      if (this.itemBankCtrl.currentQuestion?.isReadingSelectionPage || !this.isWordCount(dim)) {
        filtered.push(dim)
      }
    })
    return filtered
  }

  isTrackedChangesBtnDisabled() {
    return this.authScopeSettings.getSetting(AuthScopeSetting.DISABLE_TRACKED_CHANGES_CTRL);
  }

  getSaveSlug() {
    if (this.itemBankCtrl.isSuggestionMode()) {
      return 'Save Suggestions';
    }
    return 'ie_auto_save';
  }


  
  setSlugEditForm:{val:string};
  async setSlugEditStart(){
    this.setSlugEditForm = {
      val: this.itemBankCtrl.currentSetSlug
    }
  }
  async setSlugEditSave(){
    const slug = this.setSlugEditForm.val
    if (slug && slug.trim()){
      const patchedRecord = await this.saveLoadCtrl.saveItemSetSlugOverride(this.setSlugEditForm.val)
      if (patchedRecord){
        this.setSlugEditForm = null;
      }
    }
  }
  setSlugEditCancel(){
    this.setSlugEditForm = null;
  }

  getSavingSlug() {
    return 'ie_saving';
  }

  getRightPanelSuggestionState() {
    if (this.itemComponentEdit.usingEditingMode()) {
      return this.itemComponentEdit.suggestion?.state; //in editing mode, we need to provide a direct link to the suggestion state's element for editing the value.
    }

    return this.itemComponentEdit.suggestionStateCopy; //in review mode, we provide a copy since we're modifying it by adding deleted elements.
  }

  signOffPrevious = false;
  setSignOffPrev(val: boolean) {
    this.signOffPrevious = val;
  }

  hasPrevious() {
    if(!this.itemBankCtrl.currentQuestion?.id) {
      return false;
    }
    const itemContent = this.itemBankCtrl.getItemDiffContent(this.itemBankCtrl.currentQuestion.id, this.lang.getCurrentLanguage(), this.signOffPrevious)
    if(itemContent) {
      return true;
    }

    return false;
  }
  
  getCurrQContent() {
    try {
      if(this.itemBankCtrl.isSignOffModeReady() && this.itemBankCtrl.currentQuestion.id) {
        const itemContent = this.itemBankCtrl.getItemDiffContent(this.itemBankCtrl.currentQuestion.id, this.lang.getCurrentLanguage(), this.signOffPrevious)
        if(itemContent) {
          return itemContent;
        }
      }
    } catch(err) {
      console.error(err);
    }
    return this.getCurrQStateContent();
  }

  getCurrQStateContent() {
    return this.itemBankCtrl.getCurrQStateContent();
  }

  isQuestionSelectedByCurrentUser(questionId: number) {
    if (this.ws.prevSelectedItemId === questionId) {
      return true;
    }

    return false;
  }

  isQuestionSelected(questionId: number) {
    const selectedItems = this.ws.selectedItems;
    if (!selectedItems[questionId]) return false;

    return selectedItems[questionId].length > 0;
  }

  isQuestionSelectedByOtherUser() {
    const selectedItems = this.ws.selectedItems;
    if (!selectedItems[this.ws.prevSelectedItemId]) return false;

    const otherUsersOnQuestion = selectedItems[this.ws.prevSelectedItemId].filter(connectionId => {
      return connectionId !== this.ws.userConnectionId
    });

    return otherUsersOnQuestion.length > 0;
  }

  getNumberOfUsersOnItem() {
    if (this.isWebsocketDisconnected && !this.ws.socketConnecting) return "WEBSOCKET DISCONNECTED";

    const selectedItems = this.ws.selectedItems;
    if (!selectedItems[this.ws.prevSelectedItemId]) return "User Tracking";

    const otherUsersOnQuestion = selectedItems[this.ws.prevSelectedItemId].filter(connectionId => {
      return connectionId !== this.ws.userConnectionId
    });

    if (otherUsersOnQuestion.length === 0) {
      return "User Tracking";
    }

    if (otherUsersOnQuestion.length === 1) {
      return otherUsersOnQuestion.length + " other user in item";
    }

    return otherUsersOnQuestion.length + " other users in item";
  }

  showItemSelectedAlert(questionId: number) {
    const selectedItems = this.ws.selectedItems;
    
    if(selectedItems[questionId].length > 0 && selectedItems[questionId][0] !== this.ws.userConnectionId) {
      const selectedAuthorName = this.ws.authorNames[this.ws.selectedItems[questionId][0]];
      this.loginGuard.quickPopup(selectedAuthorName + this.lang.tra('alert_item_selected'))
    }
  }

  showItemIsBeingEditedAlert(questionId: number) {
    const selectedAuthorName = this.ws.authorNames[this.ws.selectedItems[questionId][0]];
    this.loginGuard.quickPopup(selectedAuthorName + this.lang.tra('alert_item_being_edited'))
  }

  showItemHasUpdatedAlert(userConnectionId: string) {
    const selectedAuthorName = this.ws.authorNames[userConnectionId];
    this.loginGuard.quickPopup(selectedAuthorName + this.lang.tra('alert_item_has_updated'))
  }

  /**
   * Removed since it is happening too often, including when the WS are automatically reconnecting  
   * If to be enabled in the future, `this.ws.socketConnecting must me taken into account`
   */
  showWebsocketDisconnectedAlert() {
    return;
    this.loginGuard.quickPopup("Your websocket is not connected to the server. Until your websocket connects, you and"
      + " other people in this item bank will not be able to see each other's changes or selected items in real-time.");
  }

  async selectQuestion(question) {
    if (this.isHeldForEditing() || !question) return;
    if (this.isQuestionSelectedByCurrentUser(question.id)) return;
    if (this.isWebsocketDisconnected) {
      this.showWebsocketDisconnectedAlert();
    }

    // Clear voiceover state before loading new question
    this.voiceoverState.clearInteractions();

    await this.itemBankCtrl.selectQuestion(question);
  
    if (!this.itemBankCtrl.getQuestionContent(question).isTrackingChanges && !this.itemBankCtrl.editModeItemId){
      this.itemBankCtrl.switchToEditorView()
    }
    if (this.isQuestionSelected(question.id)) {
      this.showItemSelectedAlert(question.id);
    }

    this.ws.selectItem(question.id);
  }

  private wsSaveItem() {
    if (this.saveLoadCtrl.isSaving) {
      this.ws.updateItem(this.itemBankCtrl.currentQuestion.id);
    }
  }

  isSaving() {
    if (this.itemBankCtrl.isSuggestionMode()) {
      return this.saveLoadCtrl.isSuggesting;
    }
    return this.saveLoadCtrl.isSaving;
  }

  autoGenQuestionScript = () => {
    return this.itemEditCtrl.autoGenQuestionScript(this.itemBankCtrl.getCurrentQuestionContent())
  }

  autoGenElScriptFn = (element: IContentElement) => {
    return () => { this.itemEditCtrl.autoGenElScript(element) };
  }

  checkIsSplitData() {
    let content = this.itemBankCtrl.currentQuestion
    let isSplitData = false;
    const processContent = function (el: any) {
      if (Array.isArray(el.content)) {
        el.content.forEach(processContent);
      }
      if (Array.isArray(el.advancedList)) {
        el.advancedList.forEach(processContent);
      }
      if (el.elementType == 'select_table') {
        if (el.splitData) {
          isSplitData = true;
        }
      }
    }
    content.content.forEach(processContent)
    if (isSplitData) {
      this.createRawResponseTableDataSource();
    }
    return isSplitData;
  }

  responseInLetter = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  createRawResponseTableDataSource() {
    let tableData = []
    let formattedResponse = this.itemEditCtrl.simulateSubmissionRecord.formatted_response.split(';')
    let formattedResponseInLetter = []
    formattedResponse.forEach(response => {
      formattedResponseInLetter.push(this.getResponseInLetter(response))
    })

    this.itemEditCtrl.simulateSubmissionRecord.entries.forEach((entry, index) => {
      tableData.push({ response_id: entry.response_id, response: formattedResponseInLetter[index] })
    })
    if (JSON.stringify(this.dataSource) !== JSON.stringify(tableData)) {
      this.dataSource = tableData
    }
  }

  generateQuestionLink(item:IQuestionConfig){
    return [
      '/',
      this.lang.c(),
      'test-auth',
      'item-set-editor',
      item.question_set_id,
      encodeURIComponent(item.label)
    ]
  }

  isResetting = false
  restoreResponseState(response_raw:string){
    this.isResetting = true;
    this.itemBankCtrl.setQuestionState(response_raw)
    setTimeout(() => {
      this.isResetting = false;
    }, 500);
  }

  // In split data the count is for the appearance of each choice/response in one question,
  // e.g. how many time A is choosed for the first questionnaire question.
  getExpectedAnswersTableSplitData() {
    let expectedAnswers = this.itemEditCtrl.expectedAnswers;
    let expectedAnswersSplit = [];
    //A map that counts the number of time a choice of a question appears
    const responseCount = new Map();
    //Iterate through each choice (R#C#) in each formatted response (formatted_response: 'R1C2;R2C2;R3C3;R4C3')
    expectedAnswers.forEach((answer) => {
      let responses = answer.formatted_response.split(';')
      responses.forEach((response) => {
        let count = 0;
        responseCount.has(response) ? count = responseCount.get(response) + answer.total : count = answer.total;
        responseCount.set(response, count);
      })
    })
    const availableChoices = Array.from(responseCount.keys())
    const count = Array.from(responseCount.values())

    availableChoices.forEach((choice, index) => {
      if (choice == '') { return }

      const responseID = this.dataSource[parseInt(choice.charAt(1)) - 1].response_id;
      const formattedResponse = this.getResponseInLetter(choice)

      expectedAnswersSplit.push({
        response_id: responseID,
        formatted_response: formattedResponse,
        lang: expectedAnswers[0].lang,
        score: expectedAnswers[0].score,
        total: count[index],
        weight: expectedAnswers[0].weight,
      })
    })

    expectedAnswersSplit.sort(function (a, b) {
      if (a.response_id < b.response_id) return -1;
      if (a.response_id < b.response_id) return 1;
      if (a.response_id == b.response_id) {
        if (a.formatted_response < b.formatted_response) return -1
        if (a.formatted_response > b.formatted_response) return 1
      }
      return 0;
    });

    return expectedAnswersSplit;
  }

  getResponseInLetter(response) {
    var lastChar = response[response.length - 1];
    if (response == '') {
      return ''
    } else {
      return this.responseInLetter.charAt(parseInt(lastChar) - 1)
    }
  }

  toggleHiContrast() {
    this.isHighContrast = !this.isHighContrast;
    this.textToSpeech.hi_contrast_toggle();
  }

  initializeUserInactivityChecker() {
    window.onload = this.resetInactiveTimeout;
    window.onmousedown = this.resetInactiveTimeout;
    window.onkeydown = this.resetInactiveTimeout;
    window.onclick = this.resetInactiveTimeout;
  }

  resetInactiveTimeout = () => {
    if (this.inactiveTimeout) {
      clearTimeout(this.inactiveTimeout);
    }

    // disconnect and reconnect the user if they are inactive for 30 minutes or more
    this.inactiveTimeout = setTimeout(this.reconnectInactiveUser, this.inactivityTimerDuration);
  }

  reconnectInactiveUser = () => {
    this.inactiveSubject.next();
  }

  cleanupUserInactivityChecker() {
    window.onload = () => { };
    window.onmousedown = () => { };
    window.onkeydown = () => { };
    window.onclick = () => { };
    clearTimeout(this.inactiveTimeout);
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  startChangeLogModal(){
    const config = {};
    this.pageModal.newModal({
      type: ModalType.CHANGE_LOG,
      config,
      finish: () => {},
    })

  }

  /**
   * Determine if the question should be displayed on the left question list in auth view.
   * @param question 
   * @returns true if it should, false if not
   */
  showQuestionBlock(question: IQuestionConfig){
    return !this.itemBankCtrl.editModeItemId || question.id === this.itemBankCtrl.editModeItemId;
  }

  /**
   * Determine if a sequence should be displayed on the left question list in auth view.
   * @param question the question config for the sequence
   * @returns true if it should, false if not
   */
  showSequenceBlock(question: IQuestionConfig){
    const childQuestions = [];
    (question as ISequenceConfig).children.forEach(q => childQuestions.push(q)) 

    const isEditingOneOfTheChild = childQuestions.filter(q => q.id == this.itemBankCtrl.editModeItemId).length > 0

    return !this.itemBankCtrl.editModeItemId || isEditingOneOfTheChild;
  }

  /**
   * Get the question config of all side items
   * @returns List of question config for all side items
   */
  getSideItemConfigList(){
    const questions: IQuestionConfig[] = [];
    this.itemFilterCtrl.getSideItemList().forEach(i => {
      if (i.type === 'item') {
        questions.push(i)
      } else if (i.type === 'sequence') {
        (i as ISequenceConfig).children.forEach(q => questions.push(q))
      }
    })
    return questions;
  }

  /**
   * Returns the question config of all associated texts of the item in editing mode
   * @returns An array of question configs of the associated text items
   */
  getEditModeItemReadingSelections(): IQuestionConfig[]{
    if (this.itemBankCtrl.editModeItemId){

      const questions = this.getSideItemConfigList();
      const editModeItemConfig = questions.filter(q => q.id === this.itemBankCtrl.editModeItemId)[0]
      const editModeItemReadSelLabels = this.lang.c() === 'fr' && editModeItemConfig.langLink !== null ? editModeItemConfig?.langLink?.readSelections : editModeItemConfig?.readSelections;

      if (editModeItemReadSelLabels && editModeItemReadSelLabels.length > 0){
        const readSelConfigs = questions.filter(q => editModeItemReadSelLabels.includes(q.label));
  
        return readSelConfigs;
      } else {
        return [];
      }
    }
    return [];
  }

  isViewModeSelectionDragging: boolean = false;
  onViewModeSelectorDragEnd(){
    this.isViewModeSelectionDragging = true;
  }

  setEditViewMode(viewMode: EditViewMode){
    console.log('clicked!')
    if (!this.isViewModeSelectionDragging){
      this.itemBankCtrl.setEditViewMode(viewMode);
    } else {
      this.isViewModeSelectionDragging = false;
    }
  }

  isABED(){
    return this.whiteLabel.getSiteFlag('IS_ABED');
  }

  getTextToSpeechSlug(){
    if (this.isABED()){
      return 'ie_text_to_speech_ABED';
    }
    return 'ie_text_to_speech';
  }

  getResultPrintModeSlug(){
    if (this.printViewCtrl.isResultsPrint){
      if (this.isABED()){
        return 'auth_leave_results_print_mode_ABED';
      }
      return 'auth_leave_results_print_mode';
    } 

    if (this.isABED()){
      return 'auth_results_print_mode_ABED';
    }
    
    return 'auth_results_print_mode';
  }

  getOnlinePaperSlug(){
    if (this.isABED()){
      return 'ie_online_or_paper_ABED';
    }
    return 'ie_online_or_paper';
  }

  getRestoreSlug(){
    if (this.isABED()){
      return 'ie_restore_ABED';
    }
    return 'ie_restore';
  }
  async retrieveArchivedItems() {
    this.isLoadingArchived = true;
    this.isViewingArchived = !this.isViewingArchived;
    if (this.isViewingArchived) {
      this.cachedQuestions = this.itemFilterCtrl.getSideItemList()
      this.itemBankCtrl.questions = (await this.saveLoadCtrl.getArchivedQuestions())?.filter(q => !!q)??[];
      this.itemBankCtrl.refreshQuestionsView();
    } else {
      this.itemBankCtrl.questions = this.cachedQuestions;
      this.itemBankCtrl.refreshQuestionsView();
    }
    if (this.itemFilterCtrl.getSideItemList()[0]) {
      this.itemBankCtrl.selectQuestion(this.itemFilterCtrl.getSideItemList()[0]);
    }
    this.isLoadingArchived = false;
  }
  async recoverQuestion() {
    try{
      const currentQuestion = JSON.parse(JSON.stringify(this.itemBankCtrl.currentQuestion));
      await this.itemBankCtrl.recoverQuestion()
      this.cachedQuestions.push(currentQuestion);
      this.itemBankCtrl.questions = await this.saveLoadCtrl.getArchivedQuestions();
      this.itemBankCtrl.refreshQuestionsView();
    } catch(e){
      console.error(e);
    }
  }

  async approveItemSignOff(questionId: number, isApproved: 0 | 1) {
    const itemSignOffRecord = this.itemBankCtrl.getItemSignOffData(questionId);
    if(!itemSignOffRecord) {
      this.loginGuard.quickPopup('Sign off record not found.')
      return;
    }
    return await this.itemBankCtrl.approveItemSignOff(itemSignOffRecord.id, isApproved);
  }

  async approveTestDesignSignOff() {
    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure you want to sign off on this test design?',
      confirm: () => {
        const signOffId = this.itemBankCtrl.signOffDiffData?.signOffRecord?.id;
        if(signOffId == undefined) {
          this.loginGuard.quickPopup('Sign off record not found.')
          return;
        }
        this.itemBankCtrl.approveTestDesignSignOff(signOffId).catch((err) => {
          this.loginGuard.quickPopup(`Error signing off test design: ${err.message}`)
        }).finally(() => {
          this.publishingCtrl.loadTestDesignReleaseHistory();
        });
      }
    })

  }
}
