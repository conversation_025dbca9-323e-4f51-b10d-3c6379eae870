<div >
    <div *ngIf="!isLoaded">
      Loading...
    </div>
    <div *ngIf="isLoaded" style="display: flex; flex-direction: column; gap: 1em;">
      <div style="display:flex; flex-direction:row; gap: 2em;">
        <div style="max-height:90vh; overflow:auto">
            <h2>General</h2>
            <table class="table is-bordered" style="width:auto; margin-bottom:2em;" cdkDropListGroup>
                <tr>
                    <th style="width: 18em; max-width:20em"><tra slug="ie_section"></tra></th>
                </tr>
                <tr *ngFor="let partition of frameworkCtrl.asmtFmrk.partitions; let index = index">
                    <td>
                        <div class="space-between">
                            <div>
                            <a [class.is-disabled]="isReadOnly()">{{getPartitionPropValue(partition, 'description') || '(Description)'}}</a>
                            <span *ngIf="getPartitionPropValue(partition, 'isConditional')">Path {{ getConditionOnOptionVal(partition) }}</span>
                            </div>
                            <div style="display: flex; flex-direction: row; justify-content: flex-end">
                            <code>
                                {{getPartitionPropValue(partition, 'id')}}
                            </code>
                            </div>
                        </div>
                        <!-- <button class="button is-small" (click)="util.moveArrElUp(partition, frameworkCtrl.asmtFmrk.partitions)"><tra slug="auth_move_up"></tra></button>
                        <button class="button is-small" (click)="util.moveArrElDown(partition, frameworkCtrl.asmtFmrk.partitions)"><tra slug="auth_move_down"></tra></button> -->
                        <div  class="section-settings-container">
                            <div class="section-setting"><span class="first-el">Section Slug</span> <a [class.is-disabled]="isReadOnly()"> {{getPartitionPropValue(partition, 'sectionSlug') || '(No Section Slug)'}} </a></div>
                            <!-- <button [disabled]="isReadOnly()" class="button is-small is-danger" (click)="removeSection(partition)"><tra slug="auth_discard_section"></tra></button> -->
                        </div>
                        <div style="display: flex; flex-direction: column; padding: 0em 0.5em;">
                          <div *ngFor="let question of getSectionQuestions(partition); let i = index" class="section-settings-container" style="display: flex; flex-direction: column; border-top: none; margin: 0em; padding: 0.25em 0.5em;">
                            <div class="section-setting"><span class="first-el" style="font-weight: bold;">Score Slug</span> <a [class.is-disabled]="isReadOnly()"> {{question[ENTRY_PARAM_DOMAIN_SLUG]}} </a></div>
                            <div class="section-setting"><span class="first-el">Score Weight</span> <a [class.is-disabled]="isReadOnly()"> {{getScoreEntryWeightValue(question.id) || '(No Score Weight)'}} </a></div>
                          </div>
                          <!-- <div style="margin-top: 0.5em;">
                            <button class="button is-small" (click)="createScoreEntryQuestion(partition)">Add Score Entry</button>
                          </div> -->
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div>
            <h2>Cut Scores</h2>
            <div *ngIf="frameworkCtrl.isTestFormScoreEntry()">
                <h3>Choose Twtar for Cut Score Editing:</h3>
                <div *ngIf="publishingCtrl.twtars.length > 0; else noTwtars">
                    <select [(ngModel)]="publishingCtrl.selectedTwtar" (change)="onTwtarChange()">
                    <option *ngFor="let twtar of publishingCtrl.twtars" [ngValue]="twtar">
                        {{ twtar.twtar_name }} - {{ twtar.twtar_date_start | date: 'MMM yyyy' }}
                    </option>
                    </select>
                </div>
                <ng-template #noTwtars>
                    Test Design is not yet published to a test window. Please update/assign TD to a twtar.
                </ng-template>
                <ng-container *ngIf="cutScoreConfig ; else noCutScoreConfig" >
                    <div style="margin-top:1em">
                        <button class="button is-small is-info is-outlined" (click)="editCutConfig()">Edit Cut Score Config</button>
                        <button class="button is-small is-info is-outlined" (click)="openIsrOptions()">Print Sample ISR</button>
                    </div>
                    <div style="margin: 2em 0" *ngFor="let lang of cutScoreConfig | keyvalue">
                        <h2>{{ lang.key | uppercase }} Cut Score Config</h2>
                        <table>
                          <thead>
                            <tr>
                              <th>Section Slug</th>
                              <ng-container *ngFor="let section of lang.value | keyvalue; let firstSection = first">
                                <ng-container *ngIf="firstSection">
                                  <th *ngFor="let scoreType of section.value">
                                    {{ scoreType.long }}
                                  </th>
                                </ng-container>
                              </ng-container>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let section of lang.value | keyvalue">
                              <td>{{ section.key }}</td>
                              <td *ngFor="let score of section.value">
                                {{ score.cut_score !== null ? score.cut_score : 'N/A' }}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                </ng-container>
                <ng-template #noCutScoreConfig>
                    <div>No Cut Scores</div>
                    <button *ngIf="publishingCtrl.twtars.length > 0" (click)="toggleCutConfigModal()" >Create Cut Score Config</button>
                </ng-template>
            </div>
        </div>
        <div>
          <h2>ISR Text</h2>
          <div *ngFor="let key of isrTextKeys">
            <label>
              {{ key | titlecase }} Title:
              <input
                style="display: block;"
                [(ngModel)]="frameworkCtrl.asmtFmrk.isrText[lang.c()][key].title"
                (ngModelChange)="updateTitle($event, key)"
              />
            </label>
            <label style="display: block; width: 100%;">
              {{ key | titlecase }} Content:
              <textarea
                class="text-area"
                [(ngModel)]="frameworkCtrl.asmtFmrk.isrText[lang.c()][key].content"
                (ngModelChange)="updateContent($event, key)"
                style="width: 100%; box-sizing: border-box;">
              </textarea>
            </label>
          </div>
        </div>
        <div>
            <h2>Category Descriptors</h2>
            <button 
                [disabled]="!categoryDescriptorUrl" 
                class="button" 
                (click)="openCategoryDescriptors()">
                View
            </button>
            <button 
                [disabled]="!categoryDescriptorUrl" 
                class="button" 
                (click)="deleteCategoryDescriptors()">
                Delete
            </button>
            <div *ngIf="!categoryDescriptorUrl" class="edit-mode-value">
                <div>
                  <div class="file" >
                    <label class="file-label">
                      <input class="file-input" type="file" (change)="startUpload($event.target.files)">
                      <span class="file-cta">
                      <span class="file-icon">
                        <i class="fa fa-upload"></i>
                      </span>
                      <span class="file-label">
                        <tra slug="auth_select_file_to_upload"></tra>
                      </span>
                    </span>
                    </label>
                  </div>
                </div>
              </div>
        </div>
        <button class="button" (click)="forceLoadAsmtStructure()">Force Sync</button>
      </div>
    </div>
    <div class="custom-modal" *ngIf="cutConfigModalIsOpen">
        <div class="modal-contents" style="max-width:80vw;">
            <h1>Create Cut Score Config</h1>
            <form [formGroup]="cutConfigForm">
                <div *ngFor="let lang of cutConfigForm.controls | keyvalue">
                    <h2>{{ lang.key | titlecase }} Sections</h2>
                    <button class="button is-small is-info is-outlined" type="button" (click)="addSection(lang.key)">Add Section</button>
                    <div formArrayName="{{ lang.key }}">
                        <div class="section" *ngFor="let section of getSections(lang.key).controls; let i = index" [formGroupName]="i">
                            <label>
                                Section Slug:
                                <input formControlName="section_slug" />
                            </label>
                            <div>
                                <strong>Scores</strong>
                            </div>
                            <div formArrayName="scores">
                                <div class="scores" *ngFor="let score of getScores(section).controls; let j = index" [formGroupName]="j">
                                <label>
                                    Short Name:
                                    <input formControlName="short" />
                                </label>
                                <label>
                                    Long Name:
                                    <input formControlName="long" />
                                </label>
                                <label>
                                    Cut Score:
                                    <input formControlName="cut_score" type="number" />
                                </label>
                                <label>
                                    Hex Color:
                                    <input formControlName="color" type="string" />
                                </label>
                                <label>
                                    Order:
                                    <input formControlName="order" type="number" />
                                </label>
                                <button class="button is-small" type="button" (click)="removeScore(section, j)">Remove Score</button>
                                </div>
                            </div>
                            <button class="button is-small" type="button" (click)="addScore(section)">Add Score</button>
                            <button class="button is-small" type="button" (click)="removeSectionForCutConfig(lang.key, i)">Remove Section</button>
                            <hr/>
                        </div>
                    </div>
                </div>
            </form>
            <button class="button is-small is-info is-outlined" (click)="confirmCutConfig()">Confirm</button>
            <button class="button is-small" (click)="toggleCutConfigModal()">Cancel</button>
        </div>
    </div>
    <!-- <div style="margin-top:1em;">
      <button [disabled]="isReadOnly()" (click)="createNewScoreEntrySection()" class="button is-success"><tra slug="ie_create_new_section"></tra></button>
    </div> -->

</div>
<div class="custom-modal" *ngIf="dateSelectorModalIsOpen">
    <div class="modal-contents" style="max-width:80vw; min-width: 40em;">
      <div style="display: flex; flex-direction: column; margin-bottom: 1em;">
        <h1>Select Dates</h1>
        <mat-form-field>
          <input matInput 
            [matDatepicker]="picker"
            [formControl]="dateControl"
            (dateChange)="addDate($event, picker)">
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
        <div style="display: flex; justify-content: flex-end;">
          <button mat-button (click)="clearDates()">Clear Dates</button>
        </div>
      </div>
      <div style="margin-bottom: 1em; display: flex; flex-direction: column; gap: 1.25em;">
        <div *ngFor="let date of customScoreMap | keyvalue">
          <b style="font-size: 1.5em;">{{renderDate(date.key)}}</b>
          <div>
            <score-entry [isSample]="false" [test_designs]="date.value"></score-entry>
          </div>
          <hr style="margin-bottom: 0em;"/>
        </div>
      </div>
      <div class="buttons" style="justify-content: flex-end;">
        <button mat-button (click)="closeDateModal()">Close</button>
        <button [disabled]="sampleDates.length == 0 || hasInvalidScore()" mat-button (click)="printSampleIsr()">Generate ISR</button>
      </div>
    </div>
  </div>