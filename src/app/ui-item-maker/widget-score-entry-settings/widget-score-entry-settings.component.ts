import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { EditingDisabledService } from '../editing-disabled.service';
import { LangService } from 'src/app/core/lang.service';
import { AuthService } from 'src/app/api/auth.service';
import { indexOf } from '../services/util';
import { IAssessmentPartition, IIsrText, ISectionQuestion } from '../item-set-editor/models/assessment-framework';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { ElementType, IContentElementScore, IQuestionConfig } from 'src/app/ui-testrunner/models';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { RoutesService } from 'src/app/api/routes.service';
import { MatDatepickerInputEvent, MatDatepicker } from '@angular/material/datepicker';
import { sampleStudentData } from './data';
import { DefaultCategories } from 'src/app/ui-teacher/view-teacher-student-reports/view-teacher-student-reports.component';
import { mtz } from 'src/app/core/util/moment';
import { extractSectionParams } from '../item-set-editor/controllers/testform-gen';
import { IDiscontRuleMap } from 'src/app/ui-teacher/score-entry/types';
import { ISEItemSet, IAsmtStructuresDB, TQuestionScoreSlugMap, TItemSetSlugMapping } from './types';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { ItemType } from '../models';

export const ENTRY_PARAM_DOMAIN_SLUG = 'entry_domain';
export const ENTRY_PARAM_CAPTION_SLUG = 'entry_caption';
export const ENTRY_PARAM_SECTION_SLUG = 'section_slug';

@Component({
  selector: 'widget-score-entry-settings',
  templateUrl: './widget-score-entry-settings.component.html',
  styleUrls: ['./widget-score-entry-settings.component.scss'],
})
export class WidgetScoreEntrySettingsComponent implements OnInit {

  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl

  public util = new ItemBankUtilCtrl();
  cutScoreConfig;
  csdId: number;
  cutConfigForm: FormGroup;
  cutConfigModalIsOpen = false;
  isLoaded = false;

  categoryDescriptorUrl: string;
  dateSelectorModalIsOpen = false;
  sampleDates: string[] = [];
  dateControl = new FormControl();
  sampleIsrReports = [];
  isrTextKeys: string[] = [];
  discontinuationRules: IDiscontRuleMap = {};
  ENTRY_PARAM_DOMAIN_SLUG = ENTRY_PARAM_DOMAIN_SLUG;

  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    public profile: StyleprofileService,
    private auth: AuthService,
    private fb: FormBuilder,
    private routes: RoutesService,
    private changeDetector: ChangeDetectorRef,
    private login: LoginGuardService
  ) {
    this.cutConfigForm = this.fb.group({
      en: this.fb.array([]),
      fr: this.fb.array([])
    });
   }

  async ngOnInit() {
    if (!this.frameworkCtrl.asmtFmrk.isrText) {
      this.frameworkCtrl.asmtFmrk.isrText = this.initializeIsrText();
    }
    this.isrTextKeys = Object.keys(this.frameworkCtrl.asmtFmrk.isrText[this.lang.c()]);

    await this.getTwtarsFromMostRecentDesign();
    await this.getCategoryDescriptors();
    this.checkIdLabelMapping();
    try {
      await this.autoLoadAsmtStructure();
    } catch (err) {
      console.error(err);
    }
    await this.loadDiscontinuationRules();
    this.isLoaded = true;
  }

  validateAsmtStructure(asmtStructure: IAsmtStructuresDB, itemSetSlugMapping: TItemSetSlugMapping) {
    const sections = asmtStructure.item_sets.sets
    sections.forEach((section, index) => {
      if(!section.item_set_structure_slug) {
        throw new Error(`MISSING_ITEM_SET_STRUCTURE_SLUG_INDEX_${index}`);
      }
      if(!section.set) {
        throw new Error(`MISSING_SET_SLUG_INDEX_${index}`);
      }
      if(!itemSetSlugMapping[section.item_set_structure_slug].sets ||
        !itemSetSlugMapping[section.item_set_structure_slug].sets[section.set]
      ) {
        throw new Error(`MISALIGNED_SECTIONS_${section.item_set_structure_slug}_${section.set}`);
      }
      if(Array.isArray(itemSetSlugMapping[section.item_set_structure_slug].sets[section.set].item_params)) {
        throw new Error(`ITEM_PARAMS_IS_ARRAY_${section.set}`);
      }
      for(const item of itemSetSlugMapping[section.item_set_structure_slug].sets[section.set].items) {
        if(Array.isArray(item.item_params)) {
          throw new Error(`ITEM_PARAMS_IS_ARRAY_${item.slug}`);
        }
      }
    })
  }

  isAsmtStructureChanged(asmtStructure: IAsmtStructuresDB) {
    return asmtStructure.id != this.frameworkCtrl.asmtFmrk.asmtStructId
  }

  async autoLoadAsmtStructure() {
    const {asmtStructure, itemSetSlugMapping} = await this.frameworkCtrl.getLatestAsmtStructure();

    // Check if assessment structure from API is valid.
    try {
      this.validateAsmtStructure(asmtStructure, itemSetSlugMapping)
    } catch (err) {
      return this.login.quickPopup(`Invalid assessment structure detected: ${err.message}`);
    }

    // Check if changes exist.
    const isAsmtStructureChanged = this.isAsmtStructureChanged(asmtStructure);
    if(!isAsmtStructureChanged) {
      return false;
    }

    this.frameworkCtrl.isAsmtStructureOutdated = true;
    
    this.login.confirmationReqActivate({
      caption: this.lang.tra('A new version of the current assessment structure has been found, click ok to upgrade.'),
      confirm: async () => {
        await this.loadAsmtStructure(asmtStructure, itemSetSlugMapping);
      },
      close: () => {
      }
    })
  }

  async forceLoadAsmtStructure() {
    const {asmtStructure, itemSetSlugMapping} = await this.frameworkCtrl.getLatestAsmtStructure();

    // Check if assessment structure from API is valid.
    try {
      this.validateAsmtStructure(asmtStructure, itemSetSlugMapping)
    } catch (err) {
      return this.login.quickPopup(`Invalid assessment structure detected: ${err.message}`);
    }
    
    this.login.confirmationReqActivate({
      caption: this.lang.tra('Are you sure you would like to load the latest assessment structure?'),
      confirm: async () => {
        await this.loadAsmtStructure(asmtStructure, itemSetSlugMapping);
      },
      close: () => {
      }
    })
  }

  async loadAsmtStructure(asmtStructure: IAsmtStructuresDB, itemSetSlugMapping: TItemSetSlugMapping) {

    this.isLoaded = false;
    try {
      // Create an array of all curr questions to reuse.
      const items = this.itemBankCtrl.getItems();
      const asmtFmrk = this.frameworkCtrl.asmtFmrk;
      if(!asmtFmrk.partitions) {
        asmtFmrk.partitions = [];
      }
      if(!asmtFmrk.sectionItems) {
        asmtFmrk.sectionItems = {};
      }

      const partitions = asmtFmrk.partitions;
      const sectionItems = asmtFmrk.sectionItems;

      // Wipe all sections
      this.clearArray(partitions ?? []);
      this.clearObject(sectionItems);

      try {
        asmtFmrk.asmtDescription = asmtStructure.assessment_description;
      } catch (err) {
        console.error(err);
      }
      // Create new sections/questions.
      const sections = asmtStructure.item_sets.sets
      for(const section of sections) {
        const itemSet = itemSetSlugMapping[section.item_set_structure_slug];
        const itemSetSection = itemSet.sets[section.set]
        const itemSlugDefaults = itemSet.item_slug_defaults;
        const itemSetParams = itemSet.item_params;
        await this.frameworkCtrl.createNewSection(true);
        const newPartition = partitions[partitions.length-1];
        newPartition.description = itemSetSection.caption.en;
        newPartition.sectionSlug = itemSetSection.item_params[ENTRY_PARAM_SECTION_SLUG] ?? itemSetSection.slug;
        if(newPartition.langLink) {
          newPartition.langLink.description = itemSetSection.caption.fr;
          newPartition.langLink.sectionSlug = itemSetSection.item_params[ENTRY_PARAM_SECTION_SLUG] ?? itemSetSection.slug;
        }

        if(!sectionItems[newPartition.id]) {
          sectionItems[newPartition.id] = {questions: []};
        }

        const questions = sectionItems[newPartition.id].questions
        const sectionItemParams = itemSetSection.item_params;
        for(const item of itemSetSection.items) {
          const itemDefaults = itemSlugDefaults[item.slug];
          const itemParams = item.item_params;
          const reusedQuestion = items.pop();
          // Use item caption, fallback to itemSlugDefaults if empty.
          const itemCaption = {
            en: (item.caption?.en && item.caption?.en.length ? item.caption?.en : itemDefaults?.caption?.en) ?? item.slug,
            fr: (item.caption?.fr && item.caption?.fr.length ? item.caption?.fr : itemDefaults?.caption?.fr) ?? item.slug,
          }
          let questionCommonInfo = {
            [ENTRY_PARAM_DOMAIN_SLUG]: item.slug,
            [ENTRY_PARAM_CAPTION_SLUG]: itemCaption,
            ...itemSetParams,
            ...sectionItemParams,
            ...itemDefaults?.item_params,
            ...itemParams,
          }
          if (!reusedQuestion) {
            // If no items left, need to create new item
            const question = await this.itemBankCtrl.createNewQuestion(ItemType.ITEM);
            questions.push({
              label: question.label,
              id: question.id,
              ...questionCommonInfo
            })
          }
          else {
            questions.push({
              label: reusedQuestion.label,
              id: reusedQuestion.id,
              ...questionCommonInfo
            })
          }

          // Set new item score weight, use item max and fallback to item defaults
          const latestQuestion = questions[questions.length-1]
          if(item.val_max || itemDefaults?.val_max) {
            await this.updateQuestionContentProp(latestQuestion, 'scoreWeight', item.val_max ?? itemDefaults.val_max)
          }
        }
      };

      this.frameworkCtrl.isAsmtStructureOutdated = false;
      this.frameworkCtrl.isAsmtStructureSaveRequired = true;
      
      this.frameworkCtrl.asmtFmrk.asmtStructId = asmtStructure.id;

      // await this.saveLoadCtrl.saveChanges(); // this causes issues because question saves are not awaited properly.
    } catch (err) {
      this.login.quickPopup(`Something went wrong while loading the assessment structure: ${err.message}`);
      console.error(err);
    } finally {
      this.isLoaded = true;
    }
  }

  private clearArray(arr: any[]) {
    arr.splice(0, arr.length);
  }

  private clearObject(obj) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        delete obj[key];
      }
    }
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  async getTwtarsFromMostRecentDesign() {
    await this.publishingCtrl.loadTestDesignReleaseHistory()
    if (this.publishingCtrl?.testDesignReleaseHistory?.length > 0) {
      await this.onTwtarChange();
    }
  }

  initializeIsrText(): Record<string, IIsrText> {
    return this.lang.getSupportedLanguages().reduce((acc, lang) => {
      acc[lang] = {
        definition: {
          title: '',
          content: '',
        },
        interpretation: {
          title: '',
          content: '',
        },
        footer: {
          title: '',
          content: '',
        },
        overall: {
          title: '',
          content: '',
        }
      };
      return acc;
    }, {} as Record<string, IIsrText>);
  }

  async getCategoryDescriptors() {
    this.categoryDescriptorUrl = null;
    const res = await this.auth.apiGet('public/test-auth/category-descriptors', this.itemBankCtrl.customTaskSetId, {})

    if (res.link) {
      this.categoryDescriptorUrl = res.link;
    }
  }

  async deleteCategoryDescriptors() {
    const res = await this.auth.apiRemove('public/test-auth/category-descriptors', this.itemBankCtrl.customTaskSetId, {})
    await this.getCategoryDescriptors();
  }

  checkIdLabelMapping(){
    const changesMade = []
    const asmtFmrk = this.frameworkCtrl.asmtFmrk;
    for (let partition of asmtFmrk.partitions){
      const {questions} = this.frameworkCtrl.getSectionQuestionsLinear(asmtFmrk, partition);
      for (let question of questions){
        if (question.id){
          const questionRef = this.itemBankCtrl.getQuestionById(question.id)
          if (questionRef && question.label !== questionRef.label){
            changesMade.push({from:question.label, to: questionRef.label})
            question.label = questionRef.label
          }
        }
      }
    }
    if (changesMade.length > 0){
      console.log('labelChangesToSave', changesMade)
      alert('It looks like some of the item labels for items that were assigned to the form have changed. These have been updated in the form and reflected in the framework the next time you save. List is available in the console');
    }
  }

  removeElement(content:any[], element:any){
    if (window.confirm('Remove this option?')){
      let i = indexOf(content, element);
      if (i !== -1){
        content.splice(i, 1)
      }
    }
  }

  getConditionOnOptionVal = (partition: IAssessmentPartition) => +(this.getPartitionPropValue(partition, 'conditionOnOption')) + 1 

  updatePartitionProp = (partition: IAssessmentPartition, prop: keyof IAssessmentPartition, forceReplace?: boolean, onlyForLang?: string) => {
    this.util.replaceProp(this.getFormPartitionObj(partition), prop, forceReplace, onlyForLang)
  }

  getPartitionPropValue = <K extends keyof IAssessmentPartition>(
    partition: IAssessmentPartition,
    prop: K
  ): IAssessmentPartition[K] => this.frameworkCtrl.getPartitionPropValue(partition, prop);

  getFormPartitionObj = (partition: IAssessmentPartition) => this.frameworkCtrl.getFormPartitionObj(partition);

  //(click)="updateQuestionContentProp(frameworkCtrl.asmtFmrk.sectionItems[partition.id].questions[0], 'scoreWeight', true)"
  async updateQuestionContentProp(question: ISectionQuestion, prop, newValue: any) {
    const questionConfig = this.itemBankCtrl.getQuestionById(question.id)
    await this.itemBankCtrl.selectQuestion(questionConfig).then(()=>{
      const questionData = this.itemBankCtrl.currentQuestion
      if (!questionData.content[0]) {
        questionData.content.push({
          [prop]: null,
          elementType: ElementType.SCORE_ENTRY
        })
      }
      if (!questionData.langLink.content[0]) {
        questionData.langLink.content.push({
          [prop]: null,
          elementType: ElementType.SCORE_ENTRY
        })
      }
      this.util.replaceProp(questionData.content[0], prop, true, undefined, newValue)
      this.util.replaceProp(questionData.langLink.content[0], prop, true, undefined, newValue)
    })
  }

  updateQuestionMetaProp(questionId: number, prop, forceReplace?:boolean) {
    const questionConfig = this.itemBankCtrl.getQuestionById(questionId)
    this.itemBankCtrl.selectQuestion(questionConfig).then(()=>{
      const questionData = this.itemBankCtrl.currentQuestion
      if (!questionData.meta) {
        questionData.meta = {};
      }
      this.util.replaceProp(questionData.meta, prop, forceReplace)
    })
  }

  removeSection(partition: IAssessmentPartition) {
    this.util.removeArrEl(this.frameworkCtrl.asmtFmrk.partitions, partition); // remove from partitions
    delete this.frameworkCtrl.asmtFmrk.sectionItems[partition.id]; // remove from section items
  }

  getScoreEntryWeightValue(questionId: number) {
    const questionData = this.itemBankCtrl.getQuestionById(questionId)
    const content = (this.lang.getCurrentLanguage() == 'en' ? questionData?.content[0] : questionData?.langLink?.content[0]) as IContentElementScore
    if (!content) {
      return null;
    }
    return content.scoreWeight
  }

  getSectionQuestionPropValue(question: ISectionQuestion, prop: string) {
    return question[prop];
  }

  async onTwtarChange() {
    this.cutScoreConfig = null;
    this.csdId = null;
    this.clearForm();
    if (this.publishingCtrl.selectedTwtar?.cut_score_def_id) {
      const result = await this.auth.apiFind(this.routes.CUT_SCORES, {
        query: {
          cut_score_def_id: this.publishingCtrl.selectedTwtar.cut_score_def_id
        }
      })
      if (result.cut_config && result.id) {
        this.cutScoreConfig = JSON.parse(result.cut_config);
        this.csdId = result.id
      }

    }
  }

  toggleCutConfigModal() {
    this.cutConfigModalIsOpen = !this.cutConfigModalIsOpen;
  }

  async confirmCutConfig() {
    if (this.cutConfigForm.valid) {
      const partitions = this.frameworkCtrl.asmtFmrk.partitions
      const sectionSlugs = partitions.map(section => section.sectionSlug);
      const form = this.cutConfigForm.value
      for (let i = 0; i < form.en.length; i++) {
        for (const lang in form) {
          if (!sectionSlugs.includes(form[lang]![i]!.section_slug)) {
            alert(`section slug: ${form[lang]![i]!.section_slug} in ${lang} section does not exist`)
            return;
          }
        }
      }
      const cutConfig = this.transformCutConfig(this.cutConfigForm.value);
      try {
        const createResult = await this.auth.apiCreate(this.routes.CUT_SCORES, {
          config: cutConfig,
          allowed_twtar_types: [this.publishingCtrl.selectedTwtar.type_slug],
          csdId: this.csdId,
        }, {
          query: {
            twtarId: this.publishingCtrl.selectedTwtar.twtar_id
          }
        })
        this.csdId = createResult.insertId
        this.cutScoreConfig = cutConfig;
        this.toggleCutConfigModal();
      }
      catch (e) {
        console.log(e);
        alert('Issue with creating config')
      }
    } else {
      console.error('Form is invalid');
      alert('Missing Fields')
    }

  }
  
  private transformCutConfig(formValue: any): any {
    const transformedConfig = {};
  
    Object.keys(formValue).forEach(language => {
      const sections = formValue[language];
      transformedConfig[language] = {};
  
      sections.forEach(section => {
        const sectionSlug = section.section_slug;
        transformedConfig[language][sectionSlug] = section.scores
      });
    });
  
    return transformedConfig;
  }

  getSections(language: string): FormArray {
    return this.cutConfigForm.get(language) as FormArray;
  }

  addSection(language: string): void {
    const sections = this.getSections(language);
    sections.push(this.fb.group({
      section_slug: ['', Validators.required],
      scores: this.fb.array([])
    }));
  }

  removeSectionForCutConfig(language: string, index: number): void {
    const sections = this.getSections(language);
    sections.removeAt(index);
  }

  getScores(section: FormGroup): FormArray {
    return section.get('scores') as FormArray;
  }

  addScore(section: FormGroup): void {
    const scores = this.getScores(section);
    scores.push(this.fb.group({
      short: ['', Validators.required],
      long: ['', Validators.required],
      cut_score: [null],
      order: [null, Validators.required],
      color: ['', Validators.required],
    }));
    scores?.setValidators([this.uniqueOrderValidator(scores)]);
    scores?.updateValueAndValidity();
  }

  uniqueOrderValidator(scores: FormArray): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      // Filter out null or undefined orders
      const orders = scores.controls
        .map(group => group.get('order')?.value)
        .filter(order => order !== null && order !== undefined);
      
      const hasDuplicates = new Set(orders).size !== orders.length;
  
      return hasDuplicates ? { nonUniqueOrder: true } : null;
    };
  };

  removeScore(section: FormGroup, index: number): void {
    const scores = this.getScores(section);
    scores.removeAt(index);
  }

  clearForm(): void {
    const formControls = this.cutConfigForm.controls;
    Object.keys(formControls).forEach(lang => {
      const sectionsArray = formControls[lang] as FormArray;
      sectionsArray.clear();
    });
  }

  fillFormWithConfig(): void {
    Object.keys(this.cutScoreConfig).forEach(lang => {
      const sectionsArray = this.cutConfigForm.get(lang) as FormArray;
      sectionsArray.clear();

      if (this.cutScoreConfig[lang]) {
        Object.keys(this.cutScoreConfig[lang]).forEach(sectionSlug => {
          const sectionGroup = this.fb.group({
            section_slug: [sectionSlug],
            scores: this.fb.array([])
          });

          const scoresArray = sectionGroup.get('scores') as FormArray;
          this.cutScoreConfig[lang][sectionSlug].forEach(score => {
            scoresArray.push(this.fb.group({
              short: [score.short],
              long: [score.long],
              cut_score: [score.cut_score],
              order: [score.order],
              color: [score.color],
            }));
          });

          sectionsArray.push(sectionGroup);
        });
      }
    });
  }

  editCutConfig(): void {
    this.fillFormWithConfig();
    this.cutConfigModalIsOpen = true;
  }

  updateTitle(value: string, key: keyof IIsrText) {
    this.frameworkCtrl.asmtFmrk.isrText[this.lang.c()][key].title = value;
  }
  
  updateContent(value: string, key: keyof IIsrText) {
    this.frameworkCtrl.asmtFmrk.isrText[this.lang.c()][key].content = value;
  }

  async createNewScoreEntrySection() {
    await this.frameworkCtrl.createNewSection()
  }

  getSectionQuestions(partition: IAssessmentPartition) {
    const asmtFmrk = this.frameworkCtrl.asmtFmrk;
    return this.frameworkCtrl.getSectionQuestionsLinear(asmtFmrk, partition).questions;
  }

  async startUpload(files: FileList) {
        const uploadPromises = [];
        const file = files.item(0);

        const fileExt = file.name.split('.').pop();
        if (fileExt != 'pdf') {
          alert('Unsupported File Extension ' + fileExt);
            return;
        }
        uploadPromises.push( this.auth
          .uploadFile(file, file.name, 'authoring', true));
  
      const results = await Promise.all(uploadPromises);
      for (let i = 0; i < results.length; i++) {
        await this.auth.apiCreate('public/test-auth/category-descriptors', {
          url: results[i].url,
          source_item_set_id: this.itemBankCtrl.customTaskSetId,
        } ) 
      }
      await this.getCategoryDescriptors()
    }

    openCategoryDescriptors(): void {
      const url = this.categoryDescriptorUrl;
      if (url) {
          window.open(url, '_blank');
      }
  }

  openIsrOptions() {
    this.dateSelectorModalIsOpen = true;
  }

  customScoreMap: {[key: string]: any} = null;
  loadCustomScores() {
    const currLang = this.lang.getCurrentLanguage();
    const framework = this.frameworkCtrl.asmtFmrk
    const sections = framework.partitions

    const sectionScoreMap = this.customScoreMap || {}
    for (const sampleDate of this.sampleDates) {
      if(sectionScoreMap[sampleDate]) {
        continue;
      }

      sectionScoreMap[sampleDate] = this.getSampleScoreEntry();
    }

    this.customScoreMap = sectionScoreMap;
  }

  renderDate(dateStr){
    return mtz(dateStr).format(this.lang.tra('datefmt_dashboard_long_pj'))
  }

  hasInvalidScore() {
    // This means the map hasnt been loaded yet
    if(!this.customScoreMap) {
      return false;
    }

    for(const date in this.customScoreMap) {
      for(const section in this.customScoreMap[date]) {
        if(this.customScoreMap[date][section].isInvalid) {
          return true;
        }
      }
    }

    return false;
  }

  updateCustomScoreValid(scoreInfo: any) {
    try {
      if(scoreInfo.score == null || scoreInfo.score == undefined || Number.isNaN(+scoreInfo.score) || +scoreInfo.score > +scoreInfo.weight || +scoreInfo.score < 0) {
        scoreInfo.isInvalid = true;
        return;
      }
    } catch (err) {
      scoreInfo.isInvalid = true;
      return;
    }
    scoreInfo.isInvalid = false;
  }

  closeDateModal() {
    this.dateSelectorModalIsOpen = false;
    this.sampleDates = [];
    this.dateControl.reset();
    this.customScoreMap = null;
  }

  addDate(event:MatDatepickerInputEvent<Date>, picker: MatDatepicker<Date>) {
    if (event.value) {
      this.sampleDates = [...this.sampleDates, event.value.toISOString()];
      this.dateControl.reset();
      picker.close();
      this.changeDetector.detectChanges();
      this.loadCustomScores();
    }
  }

  clearDates() {
    this.sampleDates = [];
    this.dateControl.reset();
    this.customScoreMap = null;
  }

  getRandomInt(max) {
    return Math.floor(Math.random() * max);
  }

  async printSampleIsr() {
    this.sampleIsrReports = [];
    const res = await this.auth.apiFind('public/test-auth/cut-scores-definitions', {
      query: {
        cut_score_def_id: this.csdId
      }
    })
    const currLang = this.lang.getCurrentLanguage();
    const cutScores = JSON.parse(res.cut_config)[this.lang.c()]
    const framework = this.frameworkCtrl.asmtFmrk
    const sections = framework.partitions
    for (let i = 0; i < this.sampleDates.length; i++) {
      const sampleIsrData = {}

      const overallData = {
        details: {
          cutOff: [],
          weight: 0,
          students: {
            [sampleStudentData.uid]: 0,
          },
        }
      }
      sampleIsrData[DefaultCategories.OVERALL] = overallData
      for (const section in sections) {
        let sectionName = sections[section].description
        if (currLang === 'fr' && sections[section].langLink) {
          sectionName = sections[section].langLink.description
        }
        const testQuestionId = framework.sectionItems[sections[section].id].questions[0].id
        const questionData = this.itemBankCtrl.getQuestionById(testQuestionId)
        const content = questionData.content[0] as IContentElementScore
        const studentScore = this.customScoreMap[this.sampleDates[i]][0].studentAttempts[sampleStudentData.uid].questions[testQuestionId].score;
        if (!cutScores[sections[section]?.sectionSlug]) {
          alert(`Missing Cut Scores for ${sectionName}`)
          return;
        }
        sampleIsrData[sectionName] = {
          details: {
            cutOff: cutScores[sections[section].sectionSlug],
            weight: +content.scoreWeight,
            students: {
                [sampleStudentData.uid]: studentScore,
            },
          }
        }
        sampleIsrData[DefaultCategories.OVERALL].details.weight += +content.scoreWeight
        sampleIsrData[DefaultCategories.OVERALL].details.students[sampleStudentData.uid] += studentScore
      }

        // Determine data for overallData
      const totalCutOff = {} as any
      for (const section in cutScores) {
        for (let i = 0; i < cutScores[section].length; i++) {
          if (!totalCutOff[cutScores[section][i].long]) {
            totalCutOff[cutScores[section][i].long] = {
              color: cutScores[section][i].color,
              cut_score: 0
            }
          }
          // For null cut score 
          if (cutScores[section][i].cut_score == null) {
            totalCutOff[cutScores[section][i].long].cut_score = null;
            continue;
          }
          totalCutOff[cutScores[section][i].long].cut_score += cutScores[section][i].cut_score
        }
      }

      for (const outcome in totalCutOff) {
        sampleIsrData[DefaultCategories.OVERALL].details.cutOff.push({
          long: outcome,
          cut_score: totalCutOff[outcome].cut_score,
          color: totalCutOff[outcome].color
        })
      }
      
      const report = {
        data: sampleIsrData,
        date: this.sampleDates[i],
        students: [sampleStudentData],
        td_id: this.publishingCtrl.td_id,
        testDesign: {
          long_name: this.itemBankCtrl.currentSetName.value,
          framework: JSON.stringify(framework),
        },
      }
      this.sampleIsrReports.push(report);
    }

    try { 
      const data = await this.auth.apiCreate(this.routes.EDUCATOR_INDIVIDUAL_REPORT, {
        students: [sampleStudentData],
        summary_reports: this.sampleIsrReports,
        twtar_id: this.publishingCtrl.selectedTwtar.twtar_id
      }, {
        query: {
          selectedCategory: DefaultCategories.OVERALL,
          lang: currLang,
        }
      })

      if (data.pdfBase64) {
        const byteCharacters = atob(data.pdfBase64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
  
        // Create a link element and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = 'file.pdf';
        link.click();
  
        // Cleanup
        URL.revokeObjectURL(url);
      } else {
        console.error('PDF data not found');
      }
    // }
  }
  catch (error) {
    console.error('Error fetching PDFs:', error);
  }
  }

  getSampleScoreEntry() {
    const questionDb = {};
    // Initialize sample sections
    const sections = this.frameworkCtrl.asmtFmrk.partitions.map((partition) => {
      const sectionItems = this.frameworkCtrl.getSectionQuestionsLinear(this.frameworkCtrl.asmtFmrk, partition);
      const questions = [];

      sectionItems.questions.forEach(questionRef => {
        const questionId = questionRef.id;
        const question = this.itemBankCtrl.getQuestionById(questionId);
        if (question) {
          questions.push(questionId);
          questionDb[questionId] = question;
        } 
        else {
          console.error('An item has been removed from the associated item banks since this test form was created'), questionRef;
        }
      });

      return {
        ...extractSectionParams(partition, this.frameworkCtrl),
        sectionSlug: partition.sectionSlug, // REMINDER: We need to start storing sectionSlug in the sections array in test forms
        questions
      }
    });

    const studentAttempts = {
      [sampleStudentData.uid]: {
        questions: {},
        studentGovId: sampleStudentData.student_gov_id,
        firstName: sampleStudentData.first_name,
        lastName: sampleStudentData.last_name
      }
    }

    const discontinuationRules = this.discontinuationRules;

    const sampleTD = {
      assessment_description: this.frameworkCtrl.asmtFmrk.asmtDescription ?? {},
      studentAttempts,
      testForm: {
        sections,
        questionDb
      },
      discontinuationRules,
      questionScoreSlugMap: this.getQuestionScoreSlugMapping()
    }

    return [sampleTD];
  }

  getQuestionScoreSlugMapping() {
    const questionScoreSlugMapping: TQuestionScoreSlugMap = {};
    this.frameworkCtrl.asmtFmrk.partitions.forEach((partition) => {
      const sectionItems = this.frameworkCtrl.getSectionQuestionsLinear(this.frameworkCtrl.asmtFmrk, partition);

      sectionItems.questions.forEach(questionRef => {
        const questionId = questionRef.id;
        questionScoreSlugMapping[questionId] = {
          entry_domain: questionRef[ENTRY_PARAM_DOMAIN_SLUG],
          entry_caption: questionRef[ENTRY_PARAM_CAPTION_SLUG]
        };
      });
    });

    return questionScoreSlugMapping;
  }


  async loadDiscontinuationRules() {
    if(this.publishingCtrl.selectedTwtar) {
      try {
        this.discontinuationRules = await this.fetchDiscontinuationRules();
      } catch (err) {
        console.error(err);
      }
    }
  }

  async fetchDiscontinuationRules() {
    const dbRule = await this.auth.apiGet(this.routes.TEST_AUTH_RP_DISCONTINUATION_PROFILES, this.itemBankCtrl.customTaskSetId)

    if(!dbRule) {
      return undefined;
    }

    try {
      return JSON.parse(dbRule.config);
    } catch (err) {
      alert(`An error occurred, unable to load discontinuation rules.`)
    }
  }

  async createScoreEntryQuestion(partition: IAssessmentPartition) {
    const questions = this.frameworkCtrl.asmtFmrk.sectionItems[partition.id].questions;

    const question = await this.itemBankCtrl.createNewQuestion(ItemType.ITEM);
    questions.push({
      label: question.label,
      id: question.id
    });
  }
}
