type params = { [key: string | number]: any }
type caption = { en: string, fr: string }

export interface IAsmtStructuresDB {
    id: number,
    slug: string,
    caption: string,
    assessment_description: {en: string, fr: string}
    item_sets: ISEItemSets,
    reporting_profile_id: number,
    is_revoked: number,
    revoked_by_uid: number,
    revoked_on: string,
    created_by_uid: number,
    created_on: string,
    last_updated_on: string,
    updated_by_uid: number,
    latest_id?: number
}

export interface ISeItem {
    slug: string,
    item_params: params,
    val_max?: number,
    caption?: caption
}

export interface ISeItemSlugDefaults {
    [key: string]: { // item slug
        caption: caption,
        item_params: params,
        val_max?: number
    } 
}

export interface ISEItemSet {
    slug: string,
    caption: caption,
    item_params: params,
    items: ISeItem[],
}

export interface IItemSetStructuresDB {
    id: number,
    slug: string,
    config: {
        sets: ISEItemSet[],
        item_params: params,
        item_slug_defaults: ISeItemSlugDefaults
    }
    created_by_uid: number,
    created_on: string,
    latest_id?: number
}

export type TItemSetSlugMapping = { 
    [key: string]: { 
        item_slug_defaults: ISeItemSlugDefaults,
        item_params: params,
        sets: { 
            [key: string]: ISEItemSet 
        } 
    }
}

export interface ISESection {
    set: string;
    item_set_structure_slug: string;
}

export interface ISEItemSets {
    sets: ISESection[],
    item_set_structure_ids: number[]
}

export type TQuestionScoreSlugMap = {
    [key: number]: {
        entry_domain?: string, 
        entry_caption?: {en: string, fr: string}
    }
}