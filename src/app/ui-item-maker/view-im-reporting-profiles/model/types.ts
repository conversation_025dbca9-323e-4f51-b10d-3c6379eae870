
// todo: fill out all of the missing properties

import { LangService } from "src/app/core/lang.service";

export interface IReportingProfileNew {
    authoring_group_id: number,
    slug: string,
  }
  
export interface IReportingProfile {
  id?: number;
  authoring_group_id?: number;
  slug?: string;

  created_on?: string; // datetime
  created_by_uid?: number;
  is_revoked?: boolean; // stored as TINYINT(1) in DB, but treated as boolean in TS
  revoked_on?: string | null; // datetime
  revoked_by_uid?: number | null;

  cut_score_schema_id?: number | null;
  cut_score_profile_id?: number | null;
  domain_score_scaling_factor_profile_id?: number | null;
  category_schema_id?: number | null;
  layout_profile_id?: number | null;
  default_cut_score_profile_id?: number | null;
  assessment_structure_id?: number | null;

  config?: any; // stored as JSON in DB
  // ... other fields
}
export interface IReportingProfileView extends IReportingProfile {
  ag_name: string
}


export interface IDomainSchema {
  id?: number;
  authoring_group_id?: number;
  config?: any;
}

export interface IScalingFactorProfile {
  id?: number;
  authoring_group_id?: number;
  config?: any;
  // ...
}

export type TFontStyle = 'bold' | 'regular'

export type TAlign = 'left' | 'right' | 'center'

export enum BLOCK_TYPES {
  TEXT = 'TEXT',
  OVERALL = 'OVERALL',
  HEADER = 'HEADER',
  GRAPHS = 'GRAPHS',
  DIVIDER = 'DIVIDER',
  FOOTER = 'FOOTER'
}

export interface ILayoutBlock {
  blockType: BLOCK_TYPES,
  columns?: ILayoutNode[][],
  data?: ILayoutNode
}

export interface ILayoutNode {
  marginTop?: number
}
export interface ILayoutText extends ILayoutNode{
  slug: string,
  align: TAlign
  fontStyle: TFontStyle,
  fontSize: number
}

export interface ILayoutGraphs extends ILayoutNode{
  showUnits?: boolean
}

export interface ILayoutOverall extends ILayoutNode {
  titleSlug: string,
  contentSlug: string,
  titleFontSize: number,
  contentFontSize: number
}

export interface ILayoutHeader extends ILayoutNode {
  // slug: string,
  // align: TAlign
  // fontStyle: TFontStyle,
  // fontSize: number
}

export interface ILayoutConfig {
  normalFont: string,
  boldFont: string,
  layout: ILayoutBlock[],
}

export interface INodeRefConfig {
  [key: string]: {en: string, fr: string}
}

export interface IRepProfileDB {
  id: number,
  slug: string,
  layout_config: string,
  text_node_ref_config: string,
  layout_profile_id: number,
  layout_config_id: number,
  text_node_refs_id: number
}

export interface ICutScoreSubprofile {
  id: number, 
  slug: string, 
  created_on: string, 
  authoring_group_id: number,
  is_selected?: number,
  latest_id?: number
}

export interface ICutScoreProfile extends ICutScoreSubprofile{
  config: ICutScoreProfileConfig
}
export interface ICategorySchema extends ICutScoreSubprofile{
  config: ICategorySchemaConfig[]
}

export interface ICutScoreSchema extends ICutScoreSubprofile{
  config: ICutScoreSchemaConfig
}

export interface IScalingProfile extends ICutScoreSubprofile {
  config: IScalingFactorConfig
}

export interface ICutScoreProfileConfig {
  en: TLanguageProfile,
  fi: TLanguageProfile,
  fr: TLanguageProfile
}

export type TLanguageProfile = {
  entry_domain: {[domain: string]: IDomainCutScore[]}
}

export interface IDomainCutScore {
  slug: string,
  cut_score: number,
  comparison_type: COMPARISON_TYPES
}

export interface ICutScoreSchemaConfig {
  lang: string[],
  scopeTypes: string[]
}

export interface ICategorySchemaConfig {
  caption: {en: string, fr: string},
  color: string,
  order: number,
  slug: string
}

export interface IScalingFactorConfig {
  grade: string,
  lang: {
    [key: string]: IScalingFactor
  }
}

export interface IScalingFactor {
  total_weight: number,
  total_max_score: number,
  scalings: IScalingFactorScalings[]
}
export interface IScalingFactorScalings {
  entry_domain: string,
  weight: number,
  max_score: number
}

export enum COMPARISON_TYPES {
  LTE = 'lte',
  GTE = 'gte',
  EQ = 'eq',
  LT = 'lt',
  GT = 'gt'
}

export const ComparisonTypes = [
  {caption: 'None', comparison_type: null},
  {caption: 'Less than / Equal to',comparison_type: COMPARISON_TYPES.LTE},
  {caption: 'Greater than / Equal to',comparison_type: COMPARISON_TYPES.GTE},
  {caption: 'Equal to',comparison_type: COMPARISON_TYPES.EQ},
  {caption: 'Less than',comparison_type: COMPARISON_TYPES.LT},
  {caption: 'Greater than',comparison_type: COMPARISON_TYPES.GT},
]


export const getLanguageCaption = (langSlug: 'en' | 'fi' | 'fr', lang: LangService) => {
  switch(langSlug) {
    case 'en':
      return lang.tra('lbl_en')
    case 'fi':
      return lang.tra('sa_lbl_french_immi')
    case 'fr':
      return lang.tra('lbl_fr')
    default:
      return 'N/A'
  }
}

export enum ReportingSubprofileColumns {
  rp_cut_score_profile = 'cut_score_profile_id',
  rp_domain_score_scaling_factor_profile = "domain_score_scaling_factor_profile_id",
  rp_cut_score_schema = "cut_score_schema_id",
  rp_category_schema = "category_schema_id"
}