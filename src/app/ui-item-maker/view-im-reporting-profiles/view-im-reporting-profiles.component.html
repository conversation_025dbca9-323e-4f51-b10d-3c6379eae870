<div class="box">
    <h2 class="title is-4">Reporting Profiles</h2>
  
    <!-- Loading State -->
    <div *ngIf="!isLoaded && isLoading" class="notification is-info">
      <p>Loading... Please wait.</p>
    </div>
  
    <!-- No Data, Idle, or Error State -->
    <div *ngIf="!isLoaded && !isLoading && isLoadingError" class="notification is-danger">
      <p>An error occurred while loading data. Please try again or contact support.</p>
    </div>
    <div *ngIf="!isLoaded && !isLoading && !isLoadingError" class="notification is-warning">
      <p>Please select an authoring group above.</p>
    </div>
  
    <!-- Loaded State: Show Profile Table -->
    <ng-container *ngIf="isLoaded && profiles?.length">
      <table class="table is-fullwidth is-striped">
        <thead>
          <tr>
            <th *ngIf="isMultiAuthGroupScope()">Auth Group</th>
            <th>ID</th>
            <th>Slug</th>
            <th>Cut Score Profile</th>
            <th>Cut Score Schema</th>
            <th>Category Schema</th>
            <th>Scaling Factor</th>
            <th>Layout Profile</th>
            <th>Assessment Structure</th>
            <th>Select</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let profile of profiles">
            <td *ngIf="isMultiAuthGroupScope()"> <span class="tag">{{profile.ag_name}}</span> </td>
            <td><code>{{ profile.id }}</code></td>
            <td><strong>{{ profile.slug }}</strong></td>
            <td>{{ profile.cut_score_profile_id || 'TBD' }}</td>
            <td>{{ profile.cut_score_schema_id || 'TBD' }}</td>
            <td>{{ profile.category_schema_id || 'TBD' }}</td>
            <td>{{ profile.domain_score_scaling_factor_profile_id || 'TBD' }}</td>
            <td>{{ profile.layout_profile_id || 'TBD' }}</td>
            <td>{{ profile.assessment_structure_id || 'TBD' }}</td>
            <td>
              <button 
                class="button is-small" 
                [class.is-info]="selectedProfileId == profile.id"
                (click)="selectProfile(profile)"
              >
                Select
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </ng-container>
    <div *ngIf="isLoaded && profiles?.length === 0" class="notification is-light">
      No profiles found for this authoring group.
    </div>
  </div>
  
  
  <!-- Sub-Profile Management -->
  <div class="box" *ngIf="selectedProfileId">
    <span class="title is-5">
      <code>{{selectedProfileId}}</code>
      <span> | </span>
      {{getSelectedProfileSlug()}}
    </span>
    <div class="buttons" style="margin-top: 1em;">
      <button 
        class="button"
        [class.is-info]="subProfileFocus === SubProfileFocus.CUT_SCORE_PROFILE"
        (click)="setSubProfileFocus(SubProfileFocus.CUT_SCORE_PROFILE)">
        Cut Score Profile
      </button>
      <button 
        class="button"
        [class.is-info]="subProfileFocus === SubProfileFocus.CUT_SCORE_SCHEMA"
        (click)="setSubProfileFocus(SubProfileFocus.CUT_SCORE_SCHEMA)">
        Cut Score Schema
      </button>
      <button 
        class="button" 
        [class.is-info]="subProfileFocus === SubProfileFocus.CATEGORY_SCHEMA"
        (click)="setSubProfileFocus(SubProfileFocus.CATEGORY_SCHEMA)">
        Category Schema
      </button>
      <button 
        class="button" 
        [class.is-info]="subProfileFocus === SubProfileFocus.SCALING_FACTOR"
        (click)="setSubProfileFocus(SubProfileFocus.SCALING_FACTOR)">
        Scaling Factor
      </button>
      <button 
        class="button"
        [class.is-info]="subProfileFocus === SubProfileFocus.REPORTING_LAYOUT"
        (click)="setSubProfileFocus(SubProfileFocus.REPORTING_LAYOUT)">
        ISR Layout
      </button>
      <button 
        class="button"
        [class.is-info]="subProfileFocus === SubProfileFocus.ASSESSMENT_STRUCTURE"
        (click)="setSubProfileFocus(SubProfileFocus.ASSESSMENT_STRUCTURE)">
        Assessment Structure
      </button>
    </div>
  
    <!-- Dynamically show the sub-profile components based on focus -->
    <div *ngIf="subProfileFocus === SubProfileFocus.SCALING_FACTOR">
      <widget-rp-scaling-factor-profiles [reportingProfileId]="selectedProfileId" [authoringGroupId]="selectedAuthoringGroupId"></widget-rp-scaling-factor-profiles>
    </div>
    <div *ngIf="subProfileFocus === SubProfileFocus.CATEGORY_SCHEMA">
      <widget-rp-category-schemas [reportingProfileId]="selectedProfileId" [authoringGroupId]="selectedAuthoringGroupId"></widget-rp-category-schemas>
    </div>
    <div *ngIf="subProfileFocus === SubProfileFocus.CUT_SCORE_PROFILE">
      <widget-rp-cut-score-profiles [reportingProfileId]="selectedProfileId" [authoringGroupId]="selectedAuthoringGroupId"></widget-rp-cut-score-profiles>
    </div>
    <div *ngIf="subProfileFocus === SubProfileFocus.CUT_SCORE_SCHEMA">
      <widget-rp-domain-schemas [reportingProfileId]="selectedProfileId" [authoringGroupId]="selectedAuthoringGroupId"></widget-rp-domain-schemas>
    </div>
    <div *ngIf="subProfileFocus === SubProfileFocus.ASSESSMENT_STRUCTURE">
      <widget-rp-assessment-structures [reportingProfileId]="selectedProfileId" [authoringGroupId]="selectedAuthoringGroupId"></widget-rp-assessment-structures>
    </div>
    <div *ngIf="subProfileFocus === SubProfileFocus.REPORTING_LAYOUT">
      <div *ngIf="layoutProfileCtx.isLoading" class="notification is-info">
        <p>Loading...</p>
      </div>
      <div *ngIf="layoutProfileCtx.isError" class="notification is-danger">
        <p>An error occurred while loading data. Please try again or contact support.</p>
      </div>
      <div *ngIf="!layoutProfileCtx.isLoading" style="display: flex; flex-direction: row; justify-content: space-between; gap: 1em;">
        <!-- txt node refs -->        
        <div class="flex-column" style="flex: 1;">
          <span class="title is-5">Text Nodes</span>
          <div style="display: flex; flex-flow: row wrap; gap: 1em;">
            <div *ngFor="let item of layoutProfileCtx.txtNodeRefs | keyvalue" class="column has-border" style="min-width: 30em; max-width: 30em;">
              <span class="title is-5"> {{ item.key }}</span>
              <div class="flex-row">
                <div class="flex-column">
                  <span>EN</span>
                  <textarea class="textarea" [(ngModel)]="item.value.en" (change)="onTextNodeRefChange()"></textarea>
                </div>
                <div class="flex-column">
                  <span>FR</span>
                  <textarea class="textarea" [(ngModel)]="item.value.fr" (change)="onTextNodeRefChange()"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- layout config -->
        <div class="flex-column" style="flex: 1;">
          <span class="title is-5">Layout Config</span>
          <textarea class="textarea" style="height: 100%;" [(ngModel)]="layoutProfileCtx.layoutConfig" (change)="onLayoutConfChange()"></textarea>
          <!-- <div class="flex-column" style="max-width: 30em;">
            <strong>Regular Font</strong>
            <input class="input" [(ngModel)]="layoutProfileCtx.layoutConfig.normalFont" (change)="onLayoutConfChange()" />
          </div>
          <div class="flex-column" style="max-width: 30em;">
            <strong>Bold Font</strong>
            <input class="input" [(ngModel)]="layoutProfileCtx.layoutConfig.boldFont" (change)="onLayoutConfChange()" />
          </div>
          <div class="flex-column">
            <strong>Layout</strong>
            <div class="flex-column default-gap">
              <div class="flex-row default-gap" *ngFor="let section of layoutProfileCtx.layoutConfig.layout">
                <div class="flex-column default-gap" *ngFor="let column of section">
                  <div class="flex-column has-border" *ngFor="let row of column">
                    <div style="width: 100%;" class="select">
                      <select style="width: 100%;" [(ngModel)]="row.slug" (ngModelChange)="onLayoutConfChange()">
                        <option *ngFor="let slug of getTextNodeRefs()" [value]="slug">{{slug}}</option>
                      </select>
                    </div>
                    <div style="width: 100%;" class="select">
                      <select style="width: 100%;" [(ngModel)]="row.fontStyle" (ngModelChange)="onLayoutConfChange()">
                        <option *ngFor="let fontStyle of getFontStyles()" [value]="fontStyle">{{fontStyle}}</option>
                      </select>
                    </div>
                    <div style="width: 100%;" class="select">
                      <select style="width: 100%;" [(ngModel)]="row.align" (ngModelChange)="onLayoutConfChange()">
                        <option *ngFor="let alignment of getAlignments()" [value]="alignment">{{alignment}}</option>
                      </select>
                    </div>
                    <input class="input" type="number" [(ngModel)]="row.fontSize"/>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <div style="margin-top: 1em;">
        <button 
          [disabled]="isSavingDisabled()"
          class="button has-icon"
          [class.is-success]="isNotSaving()" 
          (click)="saveChanges()"
        >
          <span><tra slug="ie_save_changes"></tra></span>
        </button>
      </div>
    </div>
  
  </div>
  
  <!-- Create New Profile Fold -->
  <div class="box" style="margin-top: 1rem;" *ngIf="isLoaded">
    <details>
      <summary class="title is-5">Create a New Profile</summary>
      <div class="field is-grouped" style="margin-top:1rem;">
        <p class="control">
          <input class="input" placeholder="Profile Slug" [(ngModel)]="newProfile.slug" />
        </p>
        <div class="control">
          <div class="select is-fullwidth">
            <select [(ngModel)]="selectedAuthoringGroupId">
              <option>(Select Authoring Group)</option>
              <option *ngFor="let authGroup of authGroupOptions" [value]="authGroup.group_id">
                {{ authGroup.description }}
              </option>
            </select>
          </div>
        </div>
        <p class="control">
          <button class="button is-success" (click)="createProfile()">Create Profile</button>
        </p>
      </div>
    </details>
  </div>