import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IQuestionConfig } from '../models';

@Component({
  selector: 'tr-progress-bar',
  templateUrl: './tr-progress-bar.component.html',
  styleUrls: ['./tr-progress-bar.component.scss']
})
export class TrProgressBarComponent implements OnInit {

  constructor() { }

  @Input() currProgressLoc;
  @Input() currProgressLocAsNum;
  @Input() currProgressBySession;
  @Input() isHidden;
  @Input() useProgressBySession;
  @Input() testRunnerSections;
  @Input() useLessPadding; 
  @Input() isPj;
  @Input() isG6;
  @Input() isReadingSelection : Function;
  @Input() isQuestionFilled: Function;
  @Input() getQuestionTitle: Function; //not used anymore, might be used in future. Keeping for now.
  @Input() currentQuestions;
  @Input() currentQuestionIndex;
  @Input() isQuestionnaire?;

  @Output() selectQuestion = new EventEmitter();
  
  pjQIndex = 0;

  ngOnInit(): void {
    console.log(navigator.platform)
  }

  resetPjQIndex(){
    this.pjQIndex = 0;
  }

  incPjQIndex() {
    return ++this.pjQIndex;
  }

  getProgressBarProgress(){
    if(this.currProgressLocAsNum * 2 > 100)
      return 100
    return this.currProgressLocAsNum * 2
  }

  getProgressBarProgress_secondRow(){
    if(this.currProgressLocAsNum * 2 < 100)
      return 0
    return this.currProgressLocAsNum * 2 - 100
  }

  getProgressBarProgressiPad(){
    if(this.currentQuestions.length <= 6){  // one row
      return this.currProgressLocAsNum;
    }
    if(this.currentQuestions.length <= 12){  // two rows
      return this.processProgressNum(this.currProgressLocAsNum * 2 + 1);
    }
    if(this.currProgressLocAsNum * 3 >= 100){  
        return 100
    } 
    return this.processProgressNum(this.currProgressLocAsNum * 3 + 4)
    
  }

  getProgressBarProgressiPad_second(){

    if(this.currentQuestions.length > 12){   // if three rows
      if(this.currProgressLocAsNum * 3 >= 200)
        return 100
      if(this.currProgressLocAsNum * 3 < 200 && this.currProgressLocAsNum * 3 > 100)
        return this.processProgressNum(this.currProgressLocAsNum * 3 - 100);
      if(this.currProgressLocAsNum * 3 < 100)
        return 0;
    } 
    else if(this.currentQuestions.length > 6){   // if two rows
      if(this.currProgressLocAsNum * 2 < 100)
        return 0
      return this.processProgressNum(this.currProgressLocAsNum * 2 - 100);
    }
  }

  getProgressBarProgressiPad_third(){
    if(this.currProgressLocAsNum * 3 < 200)
      return 0
    else if(this.currProgressLocAsNum == 100){
      return 100
    }
    return this.processProgressNum(this.currProgressLocAsNum * 3 - 210);
  }

  processProgressNum(progressAsNumber: number){
    let numArray = [0, 20, 40, 60, 80, 100]
    let closestIndex = -1;
    let closestDist = 1000000;
    for (const element of numArray){
      if (Math.abs(element - progressAsNumber) < closestDist){
        closestDist = Math.abs(element - progressAsNumber);
        closestIndex = numArray.indexOf(element)
      }
    }

    return numArray[closestIndex];
  }


  isDeviceiPad(){
    // console.log('is iPad? ', (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 0) || navigator.platform === 'iPad')
    return (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 0) || navigator.platform === 'iPad';
  }

  isIOS() {
    if (/iPad|iPhone|iPod/.test(navigator.platform)) {
      return true;
    } else {
      return navigator.maxTouchPoints &&
        navigator.maxTouchPoints > 2 &&
        /MacIntel/.test(navigator.platform);
    }
  }
}
