@import '../../../styles/partials/_media.scss';

.progress-bar-container {
    max-width: 26em;
    margin: auto;
    padding: 2em 3em;

    &.is-pj {
        max-width: 97%;
    }
    &.less-padding {
        padding: 0em;
        padding-top: 1em;

        &.is-pj {
            padding-top: 1.7em
        }
    }
    @include viewport-md {
        margin-bottom: 0em;
    }
}

.question-loc-sm {
    display: none;
    font-size: 0.8em;
    font-weight: 600;
    margin-bottom: 0.5em;
    color: #9a9a9a;
}

.is-hidden {
    display: none;
}

.pj-navbar-container{
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    &.is-questionnaire{
        display: grid;
        grid-template-columns: repeat(auto-fill,minmax(3.2em, 1fr));
    }

    &.is-ipad{
        display: grid;
        grid-template-columns: repeat(auto-fill,minmax(5em, 1fr));
    }

    .pj-header-block{
        background-color: white;
        width: 3em;
        height: 2em;
        border: solid 1px black;
        border-radius: 5em;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:hover {
            background-color: rgb(223, 223, 223);
            color: black;
        }
    }

    .pj-question-navbar-block{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2em;
        height: 2em;
        border: solid 1px black;
        border-radius: 5em;
        cursor: pointer;
        background-color: white;
        color: black;

        &.is-filled {
            background-color: transparent;
            border: none;
        }

        .filled-div{
            width:90%;
            height:90%;
            display: flex;
            justify-content: center;
            align-items: center;
            &.is-filled {
                color: black;
                background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/star/1638218271593/star.png");
                background-repeat: no-repeat;
                background-position: 50% 42%;
                background-size: contain;
                border: none;
                background-color: transparent;
            }
        }


        &:hover {
            background-color: rgb(223, 223, 223);
            color: black;
        }
    }

    .pj-is-active{
        border: none;
        &.is-primary {
            background-color: #7dd5f5;
        }
        &.is-junior{
            color: white !important;
            background-color:#12365A;
        }
    }
}

.primary-progress-active{
    background-color:#7dd5f5;
}

.junior-progress-active{
    background-color:#12365A;
}

.pj-progress-inactive{
    width: 100%;
    height: .5em;
    background-color:rgb(223, 223, 223);
    margin-bottom: -1.3em;

    &.is-questionnaire{
        margin-bottom: 1.5em;
    }
}

.pj-progress-inactive-second{
    width: 100%;
    height: .5em;
    background-color:rgb(223, 223, 223);
    margin-bottom: -3.3em;
}

.pj-progress-inactive-second-ipad{
    width: 100%;
    height: .5em;
    background-color:rgb(223, 223, 223);
    margin-bottom: -3.3em;
    margin-top: 2.8em;
}

.pj-progress-inactive-third-ipad{
    width: 100%;
    height: .5em;
    background-color:rgb(223, 223, 223);
    margin-bottom: -5.3em;
    margin-top: 4.8em;
}

.nav-bar-button{
    background-color: transparent;
    border: none;
    font-size: 1em;
}