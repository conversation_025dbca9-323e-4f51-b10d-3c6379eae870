import { Component, OnInit, ViewChild, ElementRef, Input, OnChanges, OnDestroy, SimpleChanges } from '@angular/core';
import katex from 'katex';
import 'katex/contrib/mhchem/mhchem.js';
import { LangService } from '../../core/lang.service';
import { StyleprofileService, processText, transformThousandsSeparator, transformFrenchDecimal } from 'src/app/core/styleprofile.service';
import { HighlighterService } from '../highlighter.service';
import { IEquationBlockMode } from '../element-render-math/model';

@Component({
  selector: 'render-math',
  templateUrl: './render-math.component.html',
  styleUrls: ['./render-math.component.scss']
})
export class RenderMathComponent implements OnInit, OnDestroy, OnChanges {

  @Input() obj:any;
  @Input() prop:string;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() raw:string;
  @Input() fontStyle?: any;
  @ViewChild('mathField', { static: true }) mathFieldRef: ElementRef;

  mathField:HTMLSpanElement;
  currentLatex;
  intervalTracker;
  isRendered:boolean;
  styleProfileChangeSub;

  constructor(
    private lang:LangService,
    private profile:StyleprofileService,
    public highlighter: HighlighterService
  ) { }

  ngOnInit() {
    this.styleProfileChangeSub = this.profile.getStyleProfileChanges().subscribe( hasStyleProfile => {
      if(hasStyleProfile) {
        this.updateLatex();
      }
    });

  }

  ngOnChanges(changes: SimpleChanges): void {
    this.updateLatex();
  }

  ngOnDestroy() {
    this.clearListener();  

    if(this.styleProfileChangeSub) {
      this.styleProfileChangeSub.unsubscribe();
    }
  }

  clearListener(){
    if (this.intervalTracker){
      clearInterval(this.intervalTracker);
    }
  }

  ngAfterViewInit() {
    // console.log('ngAfterViewInit')
    this.mathField = this.mathFieldRef.nativeElement;
    this.updateLatex();
    // if (!this.raw){
    this.intervalTracker = setInterval(this.updateLatex.bind(this), 2000);
    // }
  }

  sanitizeLatex(latex:string){
    if(this.obj?.elementMode === IEquationBlockMode.CHEMISTRY) return latex;
    return processText(latex, this.profile.getStyleProfile()[this.lang.c()].renderStyling.math.transforms);
  }

  renderLatex(latex){
    latex = this.sanitizeLatex(latex);
    katex.render(latex, this.mathField, {
      throwOnError: false,
      trust: ({command}) => 
        command === "\\htmlId" ||
        command === "\\htmlClass"
    });
  }

  updateLatex(isForcedChange:boolean=false){
    // if (this.isRendered){
    //   this.clearListener(); 
    //   return 
    // }
    // console.log('updateLatex', this.raw, this.mathField)
    if (this.raw && this.mathField){
      this.renderLatex(this.raw);
      this.isRendered = true;
      return;
    }
    else if (this.mathField && this.obj && this.obj[this.prop] !== undefined){
      if ( isForcedChange || (this.obj[this.prop] !== this.currentLatex) ){
        this.currentLatex = this.obj[this.prop];
        // this.mathField.innerHTML = '$$'+this.currentLatex+'$$';
        // this.MathLive.renderMathInElement(this.mathField);
        this.renderLatex(this.currentLatex);
        this.isRendered = true;
      }
  
    }

    if (this.mathField && this.mathField.getElementsByClassName('katex').length > 0 && this.fontStyle){
      RenderMathComponent.updateFontStyle(this.mathField, this.fontStyle);
    }
  }
  
  static updateFontStyle(mathElement: HTMLElement, fontStyle: {[key: string]: string}) {
    const katexEl = mathElement.getElementsByClassName('katex')[0];
    if (katexEl == undefined) return;
    const mrelElements = mathElement.getElementsByClassName('mrel');
    const mathnormalEl = mathElement.getElementsByClassName('mathnormal');
    
    let styles = [];
    for (const [key, value] of Object.entries(fontStyle)) {
      styles.push(`${key}: ${value}`) 
    }
    katexEl.setAttribute("style", styles.join('; '));
    // Relational symbols like !=, >= etc. have font size Katex_main
    Array.from(mrelElements).forEach(mrel => mrel.setAttribute("style", "font-family: Katex_main"))
    
    if (fontStyle['font-family']){
      Array.from(mathnormalEl).forEach(el => (el as HTMLElement).style.fontFamily = fontStyle['font-family'])
      this.shiftAccentPosition(mathElement, fontStyle['font-family']);
    }
    
  }
  
  static shiftAccentPosition(mathElement: HTMLElement, fontFamily: string) {
    // accent/diacritics is shifted a little bit if the font is changed
    if (fontFamily == 'Times New Roman') {
      // -0.16em for Times New Roman
      const accentsEl = mathElement.getElementsByClassName('accent-body');
      Array.from(accentsEl).forEach(el => (el as HTMLElement).style.left = '-0.16em')
    }
  }

}

