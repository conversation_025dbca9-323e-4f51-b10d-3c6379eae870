import { ElementType } from "../models";

export const indexOf = (arr:any[], t:any) => {
    let i = -1;
    arr.forEach((_t, _i) => {
      if (_t === t){
        i = _i;
      }
    });
    return i;
  }
  

export const mapToJson = (map) =>  {
    return JSON.stringify([...map]);
}

export const isOldFirefoxBrowser = () => {
  const agent = navigator.userAgent
  const index = agent.indexOf("Firefox/")
  if (index !=-1) {
    let substr = agent.substr(index+8)
    const version = Number(substr)
    if (version && version<60) {
      return true
    }
  }
  return false
}


export const storeDataInLocalStorage = (key: string, value: string) => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.error('failed to save in local storage', key, value);
  }
}

export const retriveDataFromLocalStorage = (key: string): string | null => {
  return localStorage.getItem(key);
}

export const removeDataFromLocalStorage = (key: string) => {
  localStorage.removeItem(key);
} 

export const getPrintBlocks = (elements: any[], results: any[]) => {
  elements.forEach(element => {
    // Check if the current element has elementType 'results_print'
    if (element.elementType === ElementType.RESULTS_PRINT) {
      results.push(element);
    }

    // If the element has nested content, recursively search it
    if (element.content && Array.isArray(element.content)) {
      getPrintBlocks(element.content, results);
    }
  });
}

export const hasPrintBlocks = (elements: any[]): boolean => {
  for (const element of elements) {
    // Check if the current element has elementType 'results_print'
    if (element.elementType === ElementType.RESULTS_PRINT) {
      return true;  // Return true immediately if a 'results_print' is found
    }

    // If the element has nested content, recursively search it
    if (element.content && Array.isArray(element.content)) {
      const found = hasPrintBlocks(element.content);
      if (found) {
        return true;  // Return true if a 'results_print' is found in nested content
      }
    }
  }

  // Return false if no 'results_print' elements are found
  return false;
}

export const ElementsWithUserInteraction = [
  ElementType.MCQ,
  ElementType.MCQ_OPTION,
  ElementType.INPUT,
  ElementType.MOVEABLE_DND,
  ElementType.DND,
  ElementType.GRAPHING,
  ElementType.INSERTION,
  ElementType.SELECT_TEXT,
  ElementType.GROUPING,
  ElementType.SELECT_TABLE,
  ElementType.CUSTOM_INTERACTION,
  ElementType.INTERACTIVE_DIAGRAM,
  ElementType.NUMBERLINE,
  ElementType.SCIENTIFIC_NOTATION,
  ElementType.CUSTOM_MCQ,
  ElementType.CUSTOM_MCQ_OPTION,
  ElementType.ORDER,
  ElementType.MIC,
  ElementType.MATCHING,
  ElementType.ANNOTATION,
  ElementType.DND_TARGET,
  ElementType.VIRTUAL_TOOLS
];

const includesResponseTemplate = (elements): boolean => {

  for (const element of elements) {
    // Check if the current element requires response from user
    if (element.elementType && ElementsWithUserInteraction.includes(element.elementType)) {
      return true;
    }

    // If the element has nested content, recursively search it
    if (Array.isArray(element)) {
      const found = includesResponseTemplate(element);
      if (found) {
        return true;
      }
    } else if (element.content && Array.isArray(element.content)) {
      const found = includesResponseTemplate(element.content);
      if (found) {
        return true;
      }
    } else if (element.pages && Array.isArray(element.pages)) {
      const found = includesResponseTemplate(element.pages);
      if (found) {
        return true;
      }
    } else if (element.displayList && Array.isArray(element.displayList)) {
      const found = includesResponseTemplate(element.displayList);
      if (found) {
        return true;
      }
    } else if (element.grid && Array.isArray(element.grid)) {
      const found = includesResponseTemplate(element.grid);
      if (found) {
        return true;
      }
    } else if (element.advancedList && Array.isArray(element.advancedList)) {
      const found = includesResponseTemplate(element.advancedList);
      if (found) {
        return true;
      }
    }
  }

  // Return false if no elements are found that require response from user
  return false;
}

/*
  Takes an item and checks if it has more than one response templates
*/
export const isContainsMoreThanOneResponseTemplate = (elements: any[]): boolean => {
  let numOfElementsThatNeedResponse = 0;
  for (const element of elements) {
    // Check if the current element requires response from user
    if (ElementsWithUserInteraction.includes(element.elementType)) {
      numOfElementsThatNeedResponse += 1;
      continue;
    }

    if (element.content && includesResponseTemplate(element.content)) {
      numOfElementsThatNeedResponse += 1;
    }
  }

  return numOfElementsThatNeedResponse > 1;
}