audio.has-controls {
    min-width:14em;
    width:20em;
    height:2em;
}

audio.is-safari-override {
    max-width: 350px !important;
    height: auto !important;
}

img.prelaoders {
    width:0px;
    visibility: hidden;
}
.one-button {
    cursor:pointer; 
    width: 3em;
    %active-img {
        width:unset;
        visibility: visible;
    }
    img {
        width:0px;
        visibility: hidden;
    }
    &.is-default { 
        img.on-default{ @extend %active-img; }
    }
    &.is-playing { 
        img.on-playing{ @extend %active-img; }
        img.on-hover{ display:none; }
    }
    &:hover { 
        img.on-hover{ @extend %active-img; }
        img.on-default{ display:none; }
    }
}
.default-audio, .default-audio::before {
    content:url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6276/authoring/audio_button-default/1612797879947/audio_button-default.svg");
    &:hover {
        content:url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6276/authoring/audio_button-hover/1612798136994/audio_button-hover.svg");
    }
}

.playing-audio {
    content:url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6276/authoring/audio_button-playing/1612798150063/audio_button-playing.svg");
}


