    .simulated-question-text {
        // background-color: #9ed7ff;
        margin: 1em;
        display: flex;
        justify-content: space-evenly;
        // height: 150%;
        align-items: flex-start;
        flex-direction: column;
        // color:#fff;
        p {
            margin-bottom: 1em;
            max-width: 36em;
        }
        .temp-stim-table {
            margin-bottom: 1em;
            width: auto;
            tr td,
            tr th {
                border: 1px solid #000;
            }
            .cell-content {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
            .image-container {
                width: 5em;
                margin-right: 1em;
                text-align: center;
                flex-shrink: 0;
                img {
                    height: 4em;
                }
            }
        }
    }
    
    .solution-block {
        display: flex;
        flex-direction: row;
        margin-bottom: 0.5em;
        .is-handle {
            flex-grow: 0;
            height: 2em;
            transition: 200ms;
            font-size: 1em;
            width: 3.2em;
            opacity: 0;
            &:focus {
                opacity: 0.4;
                box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
            }
        }
        &:hover .is-handle {
            opacity: 0.4;
        }
        .solution-block-content {
            flex-grow: 1;
        }
        .table-input {
            td {
                padding: 0em;
                &.edit-cell {
                    background-color: #f1f1f1;
                    text-align: center;
                    vertical-align: middle;
                }
            }
            textarea {
                font-size: 1em;
                border-style: none;
                border-color: transparent;
                padding: 0.3em;
                padding-bottom: 0.8em;
                resize: none;
                height: 100%;
                width: 100%;
                overflow: hidden;
            }
        }
        textarea.long-input {
            font-size: 1em;
            border-style: none;
            border-color: transparent;
            padding: 0.3em;
            padding-bottom: 0.8em;
            resize: none;
            width: 100%;
            overflow: hidden;
        }
    }
    
    .as-horizontal>.as-split-area {
        height: auto;
        //overflow: auto
    }
    
    .block-inserters {
        padding-left: 4.5em;
    }
    
    .blocks {
        padding: 1em;
    }
    
    .cdk-drag-placeholder {
        opacity: 0;
    }
    
    //modal styling
    .instr-popup {
        position: fixed;
        display: none;
        background: rgba(17, 17, 17, 0.801);
        height: 100vh;
        width: 100vw;
        top: 0;
        &__box {
            position: absolute;
            top: 5%;
            left: 50%;
            transform: translateX(-50%);
            width: 60vw;
            height: 70vh;
            background: white;
            box-sizing: border-box;
            z-index: 10;
            &--header {
                height: 15%;
                width: 100%;
                background: black;
                opacity: 0.7;
                display: flex;
                flex-direction: column;
                justify-content: center;
                &-contents {}
                padding: 0 1rem;
                //position: fixed;
            }
            &--body {
                height: 83%;
                width: 100%;
                //margin-top: 15%;
                padding: 0 1rem;
                overflow-y: scroll;
                &::-webkit-scrollbar {
                    display: block;
                    width: 5px;
                    height: 10px;
                    &-track {
                        background: transparent;
                    }
                }
            }
        }
    }
    
    .drawing {
        position: relative;
        width: 100%;
        height: 44em;
    }
    
    .cancel {
        transition: all 0.3s;
        &:hover {
            color: rgb(121, 101, 101) !important;
        }
    }
    
    .btn {
        border-color: #949494;
        -webkit-transition: border-color 1.5s;
        &:hover {
            border-color: #131212;
            -webkit-transition: border-color 0.1s;
        }
    }
    
    .undo_btn {
        position: absolute;
        top: 45%;
        right: 1%;
        width: 140px;
        height: 45px;
        font-family: inherit;
        font-size: 11px;
        text-transform: uppercase;
        letter-spacing: 2.5px;
        font-weight: 500;
        color: #000;
        background-color: #fff;
        border: none;
        border-radius: 45px;
        box-shadow: 0px 12px 18px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease 0s;
        cursor: pointer;
        outline: none;
        &:hover {
            background-color: rgba(37, 155, 76, 0.787);
            box-shadow: 0px 20px 25px rgba(65, 68, 66, 0.4);
            color: #fff;
            transform: translateY(-2px);
        }
    }
    
    .review {
        width: 100%;
        height: 100%;
        &__content {
            //background: red;
            width: 100%;
            height: 100%;
        }
    }
    
    .overflowVisible {
        overflow: visible !important;
    }
    
    .ck-editor__top {
        display: none;
    }
    
    .screen {
        //width: 100%;
        //background: powderblue;
        min-width: 700px;
    }