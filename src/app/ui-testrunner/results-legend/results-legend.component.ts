import { Component, Input, OnInit } from '@angular/core';
import { StyleprofileService } from 'src/app/core/styleprofile.service';

@Component({
  selector: 'results-legend',
  templateUrl: './results-legend.component.html',
  styleUrls: ['./results-legend.component.scss']
})
export class ResultsLegendComponent implements OnInit {
  
  @Input() initExpand: boolean;
  @Input() numCRQuestions: boolean
  
  constructor(
    private styleProfile: StyleprofileService,
  ) { }

  public isExpanded : boolean = false;
  ngOnInit(): void {
    this.isExpanded = this.initExpand;
  }

  toggleLegendExpand() {
    this.isExpanded = !this.isExpanded;
  }

  getResultsLegendKeyboardSlug(){
    const textFromSP = this.styleProfile.getResultsPageText('tr_results_legend_keyboard');

    if(textFromSP)
      return textFromSP;

    return 'tr_results_legend_keyboard'
  }
}
