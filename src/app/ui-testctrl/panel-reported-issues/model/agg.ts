export enum SortByType {
    CREATION_DATE = 'CREATION_DATE',
    RESOLUTION_DATE = 'RESOLUTION_DATE',
    RESPONSE_DATE = 'RESPONSE_DATE',
    LAST_TOUCH_DATE = 'LAST_TOUCH_DATE',
}
export enum SortByDirType {
    OLDEST = 'ASC',
    NEWEST = 'DESC',
}
export enum FilterGroupJoinType {
    AND = 'AND',
    OR = 'OR',
}
export enum GroupByType {
    NONE = 'NONE',
    DATE = 'DATE',
    HOUR = 'HOUR',
    KEYWORD = 'KEYWORD',
    ASSIGNEE = 'ASSIGNEE',
    REPORTER = 'REPORTER',
    STATUS = 'STATUS',
    COMMON_CAUSE = 'COMMON_CAUSE',
    ASMT_CODE = 'ASMT_CODE',
    TW_ID = 'TW_ID',
}

export const groupByTypes = [
    {id: GroupByType.NONE, caption: 'None'},
    {id: GroupByType.DATE, caption: 'Date'},
    {id: GroupByType.HOUR, caption: 'Hour'},
    {id: GroupByType.KEYWORD, caption: 'Keyword'},
    {id: GroupByType.ASSIGNEE, caption: 'Assignee'},
    {id: GroupByType.REPORTER, caption: 'Reporter'},
    {id: GroupByType.STATUS, caption: 'Status'},
    {id: GroupByType.ASMT_CODE, caption: 'Assessment Code'},
    {id: GroupByType.TW_ID, caption: 'Test Window'},
    {id: GroupByType.COMMON_CAUSE, caption: 'Common Cause'},
]

export enum FieldType {
    DATE = 'DATE',
    NUMBER = 'NUMBER',
    STRING = 'STRING',
}
export enum CondiitionsTag {
    CONTAINS_YES = 'CONTAINS_YES',
    CONTAINS_NO = 'CONTAINS_NO',
    EMPTY_YES = 'EMPTY_YES',
    EMPTY_NO = 'EMPTY_NO',
}
export enum CondiitionsNumber {
    EQUALS = 'EQUALS',
    NOT_EQUALS = 'NOT_EQUALS',
    GREATER_THAN = 'GREATER_THAN',
    LESS_THAN = 'LESS_THAN',
    GREATER_THAN_OR_EQUALS = 'GREATER_THAN_OR_EQUALS',
    LESS_THAN_OR_EQUALS = 'LESS_THAN_OR_EQUALS',
}
export enum ConditionsDate {
    IS = 'IS',
    IS_BEFORE = 'IS_BEFORE',
    IS_AFTER = 'IS_AFTER',
    IS_ON_OR_BEFORE = 'IS_ON_OR_BEFORE',
    IS_ON_OR_AFTER = 'IS_ON_OR_AFTER',
    IS_BETWEEN_YES = 'IS_BETWEEN_YES', // is between 
    IS_BETWEEN_NO = 'IS_BETWEEN_NO',  // is not between 
    IS_RELATIVE_TO_TODAY = 'IS_RELATIVE_TO_TODAY', // is relative to today
    IS_EMPTY_YES = 'IS_EMPTY_YES', // is empty
    IS_EMPTY_NO = 'IS_EMPTY_NO', // is not empty
}
export enum DateConditionSelectionType {
    ABSOLUTE = 'ABSOLUTE',
    RELATIVE = 'RELATIVE',
}
export enum DateConditionRelativeUnit {
    DAYS = 'DAYS',
    WEEKS = 'WEEKS',
    MONTHS = 'MONTHS',
}
export enum DateConditionRelativeDirection {
    AGO = 'AGO',
    FROM_NOW = 'FROM_NOW',
}

interface IDateFilterOption {
    caption: string,
    isRange: boolean,
    isTimeExcluded?: boolean,
    condition: ConditionsDate,
    subOptions?: IDateFilterOptionSub[],
}
interface IRelativeDateDef {
    selectionType: DateConditionSelectionType,
    relativeUnit: DateConditionRelativeUnit,
    relativeDirection: DateConditionRelativeDirection,
    relativeDistance: number,

}
interface IDateFilterOptionSub {
    caption: string,
    isRange?: boolean,
    relativeDate?: IRelativeDateDef,
    relativeDateEnd?: IRelativeDateDef,
}
const dateRelativeOptions:IDateFilterOptionSub[] = [
    {
        caption: 'Today',
        relativeDate: {
            selectionType: DateConditionSelectionType.RELATIVE,
            relativeUnit: DateConditionRelativeUnit.DAYS,
            relativeDirection: DateConditionRelativeDirection.AGO,
            relativeDistance: 0,
        }
    },
    {
        caption: 'Yesterday',
        relativeDate: {
            selectionType: DateConditionSelectionType.RELATIVE,
            relativeUnit: DateConditionRelativeUnit.DAYS,
            relativeDirection: DateConditionRelativeDirection.AGO,
            relativeDistance: 1,
        }
    },
    {
        caption: 'This week',
        relativeDate: {
            selectionType: DateConditionSelectionType.RELATIVE,
            relativeUnit: DateConditionRelativeUnit.DAYS,
            relativeDirection: DateConditionRelativeDirection.AGO,
            relativeDistance: 7,
        },
        relativeDateEnd: {
            selectionType: DateConditionSelectionType.RELATIVE,
            relativeUnit: DateConditionRelativeUnit.DAYS,
            relativeDirection: DateConditionRelativeDirection.AGO,
            relativeDistance: 0,
        }
    },
]
export const DateFilterOptions:IDateFilterOption[] = [ 
    {
        caption: 'Is',
        condition: ConditionsDate.IS,
        isTimeExcluded: true,
        isRange: false,
        subOptions: dateRelativeOptions
    },
    {
        caption: 'Is before',
        condition: ConditionsDate.IS_BEFORE,
        isTimeExcluded: true,
        isRange: false,
        subOptions: dateRelativeOptions
    }
]

export interface IAggSettings {
    groupingBy: GroupByType;
    sortBy: SortByType,
    sortByDir: SortByDirType,
    filters: IFilter[],
}
export interface IFilter {
    caption: string,
    filters: IFilterConfigGroup[]
}
export interface IFilterConfigGroup {
    filters: IFilterConfig[],
    joinType: FilterGroupJoinType,
}
export interface IFilterConfig {
    field: string,
    fieldType: FieldType,
    config: IFilterConfigTags | IFilterConfigNumber | IFilterConfigDate,
}
export interface IFilterConfigTags {
    condition: CondiitionsTag,
    tags: string[],
}
export interface IFilterConfigNumber {
    condition: CondiitionsNumber,
    value: number,
}
export interface IFilterConfigDate {
    condition: ConditionsDate,
    value: string,
}

export interface IReportedIssueGrouping {
    key: string;
    caption: string;
    n?: number,
    groupingValue: {
        [key:string]: string | number | boolean;
    };
    records:any[];
}