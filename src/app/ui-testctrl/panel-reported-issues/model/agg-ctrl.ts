import { mtz } from "src/app/core/util/moment";
import { GroupByType, IAggSettings, IReportedIssueGrouping } from "./agg";
import * as _ from 'lodash';

interface IGroupingKey {
    key: string,
    label?: string,
    detail?: {
        [key:string]: string | number | boolean;
    }
}

export class ReportedIssueAggCtrl {
    
    constructor(
        public settings: Partial<IAggSettings>,
    ) {}

    records:any[] = [];
    groupings:IReportedIssueGrouping[] = [];

    loadRecords(records:any[]){
        this.records = records;
        this.updateGroupings();
    }

    identifyGroupingKeys(record:any){
        const {groupingBy} = this.settings;
        const dateProp = 'created_on' //  todo: should be picking between this and,'assigned_on', updated_on, etc.
        if ((groupingBy === GroupByType.NONE) || !groupingBy){
            return [{key: 'ALL', caption: 'All Issues', detail: {}}];
        }
        if (groupingBy === GroupByType.ASSIGNEE){
            return [{key: record['assigned_email'], detail: {}}]
        }
        if (groupingBy === GroupByType.REPORTER){
            return [{key: record['invigilator_email'], detail: {}}]
        }
        if (groupingBy === GroupByType.ASMT_CODE){
            return [{key: record['ts_slug'], detail: {}}]
        }
        if (groupingBy === GroupByType.TW_ID){
            return [{key: record['tw_id'], detail: {}}]
        }
        if (groupingBy === GroupByType.STATUS){
            return [{key: record['resolution_slug'], detail: {}}]
        }
        if (groupingBy === GroupByType.KEYWORD){
            const keywordsStr = record['keywords'];
            if (keywordsStr){
                return keywordsStr.split(',').map(k => k.trim()).map(k => {
                    return {key: k, detail: {}}
                });
            }else {
                return [{key: '(UNTAGGED)', detail: {}}]
            }
        }
        if (groupingBy === GroupByType.DATE){
            const date = mtz(record[dateProp]);
            const key = date.format('YYYY-MM-DD');
            const caption = date.format('ddd D MMM');
            return [{key, caption, detail: {}}];
        }
        if (groupingBy === GroupByType.HOUR){
            const date = mtz(record[dateProp]);
            const key = date.format('YYYY-MM-DD-HH');
            const caption = date.format('ha ddd D MMM');
            return [{key, caption, detail: {}}];
        }
        return ['*']
    }

    measureGroupings(groupings:IReportedIssueGrouping[] ){
        for (let grouping of groupings){
            grouping.n = grouping.records.length;
        }
    }
    sortGroupings(groupings:IReportedIssueGrouping[] ){
        const {groupingBy} = this.settings;

        switch (groupingBy){
            case GroupByType.KEYWORD:
                return _.orderBy(groupings, 'n', 'desc')
            case GroupByType.DATE:
            case GroupByType.HOUR:
                return _.orderBy(groupings, 'key', 'desc')
        }

        return groupings
    }

    updateGroupings(){
        const groupings:IReportedIssueGrouping[] = [];
        const groupingsRef:Map<string, IReportedIssueGrouping> = new Map();
        for (let record of this.records){
            const groupingKeys = this.identifyGroupingKeys(record);
            for (let groupingKey of groupingKeys){
                if (!groupingsRef.has(groupingKey.key)){
                    const grouping:IReportedIssueGrouping = {
                        key: groupingKey.key,
                        caption: groupingKey.caption || groupingKey.key,
                        groupingValue: {
                            ... groupingKey.details
                        },
                        records: []
                    }
                    groupingsRef.set(groupingKey.key, grouping);
                    groupings.push(grouping);
                }
                groupingsRef.get(groupingKey.key).records.push(record);
            }
        }
        this.measureGroupings(groupings)
        this.groupings = this.sortGroupings(groupings)
        console.log('updateGroupings', this.groupings)
    }
}