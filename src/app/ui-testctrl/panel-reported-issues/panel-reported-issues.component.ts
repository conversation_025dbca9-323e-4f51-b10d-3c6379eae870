import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';
import { mtz } from '../../core/util/moment';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { STU_EX_OVERRIDES } from '../panel-reported-issues/model/override-categories';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { RoutesService } from 'src/app/api/routes.service';
import { observePresenceRecords, updatePresenceRecord, removePresenceRecord, FBC_RI_PresenceRecord } from './model/firebase-reported-issues-presence';
import { ReportedIssueAggCtrl } from './model/agg-ctrl';
import { GroupByType, groupByTypes } from './model/agg';
import { FormControl } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

enum PatchType {
  ASSIGNMENT = 'ASSIGNMENT',
  RESOLUTION = 'RESOLUTION',
  KEYWORDS = 'KEYWORDS',
}
enum ResolutionStatus {
  DISMISSED = 'DISMISSED',
  PENDED = 'PENDED',
  WITHHELD = 'WITHHELD',
  PRORATED = 'PRORATED',
  OVERRIDDEN = 'OVERRIDDEN',
  RESOLVED = 'RESOLVED',
}
interface INewIssue {
  msg?:string,
  isSaving?:boolean,
}

interface IRouteParam {
  [key: string]: any
}
@Component({
  selector: 'panel-reported-issues',
  templateUrl: './panel-reported-issues.component.html',
  styleUrls: ['./panel-reported-issues.component.scss']
})
export class PanelReportedIssuesComponent implements OnInit, OnDestroy {

  @Input() roleContext:any;
  @Input() isAutoLoad:boolean;

  STU_EX_OVERRIDES = STU_EX_OVERRIDES

  recordLimit:number = 0;

  selectedIssue:any; // {ts_id}
  isShowUid:boolean;
  isDetailedMode:boolean;

  isIniting:boolean = true;
  isLoading:boolean;
  isCreating:boolean;
  isStructuredView:boolean;
  records:any[] = [];
  recordsDetailed:any[] = [];
  
  agg = new ReportedIssueAggCtrl({
    groupingBy: GroupByType.NONE,
  });
  groupingsCollapsed:{[key:string]:boolean} = {};
  groupByTypesList = groupByTypes

  // Initialize tabSessionHash once at class level
  private tabSessionHash = Math.random().toString(36).substring(2, 15);

  newIssue?:INewIssue = null
  isStudentListingExpanded:boolean

  gridOptionsDetailed:any = {
    columnDefs: [
      { headerName:'ID', field:'ri_id' },
      { headerName:'Report ID', field:'ric_id' },
      { headerName:'Date', field:'created_on', valueGetter: (params) => {
        return mtz(params.data.created_on).format('MMM D [at] h:mm A')
      }},
      { headerName:'Category', field:'categorySelection' },
      // { headerName:'Sub. Cat.', field:'subCategorySelection' },
      { headerName:'Issue', field:'msg' },
      { headerName:'Board Mident', field:'sd_code' },
      { headerName:'Board Name', field:'sd_name' },
      { headerName:'School Mident', field:'s_code' },
      { headerName:'School Name', field:'s_name' },
      { headerName:'Class', field:'sc_name' },
      { headerName:'Session Type', field:'ts_slug' },
      { headerName:'Phone Number', field:'phone_number' },
      { headerName:'Invig.Email', field:'invigilator_email' },
      { headerName:'OEN', field:'StudentOEN' },
      { headerName:'First Name', field:'first_name' },
      { headerName:'Last Name', field:'last_name' },
      { headerName:'tw_id', field:'tw_id' },
      // { headerName:'Old Log ID', field:'old_log_id' },
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };
  gridOptions:any = {
    columnDefs: [
      { headerName:'Report ID', field:'ric_id', checkboxSelection:true, width:120 },
      { headerName:'Contact Email', field:'invigilator_email' },
      { headerName:'Assigned To', field:'assigned_email' },
      { headerName:'Status', field:'resolution_slug', width:100},
      { headerName:'Date', field:'created_on', valueGetter: (params) => {
        return mtz(params.data.created_on).format('MMM D [at] h:mm A')
      }},
      { headerName:'Category', field:'categorySelection' },
      { headerName:'Phone Number', field:'phone_number' },
      { headerName:'Keywords', field:'keywords' },
      // { headerName:'Sub. Cat.', field:'subCategorySelection' },
      { headerName:'Issue', field:'msg' },
      { headerName:'Board Mident', field:'sd_code' },
      { headerName:'Board Name', field:'sd_name' },
      { headerName:'School Mident', field:'s_code' },
      { headerName:'School Name', field:'s_name' },
      { headerName:'Language', field:'s_lang' },
      { headerName:'Class', field:'sc_name' },
      { headerName:'Session Type', field:'ts_slug' },
      { headerName:'Num. Students', field:'num_students' },
      { headerName:'tw_id', field:'tw_id' },
      { headerName:'Session', field:'ts_id' },
      { headerName:'Assigned On', field:'assigned_on' },
      { headerName:'Processed?', field:'is_resolved' },
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };
  
  currentComment:string;
  isSendingComment: boolean;
  selectedStudent;
  isPracticeSessionsIncluded:boolean;
  public routeQuerySub:Subscription;

  newIssueCheckInterval: any;
  wsPresenceSub: Subscription;
  wsPresenceHeartbeatInterval: any;
  ricIdPresence: Map<number, FBC_RI_PresenceRecord> = new Map();

  assigneeControl = new FormControl('');
  filteredAssignees: Observable<any[]>;

  displayAssigneeFn = (assignee: any): string => {
    if (!assignee) return '';
    return `${assignee.assigned_name} (${assignee.assigned_email})`;
  }

  // Keyword management
  availableKeywords: Set<string> = new Set();
  keywordControl = new FormControl('');
  filteredKeywords: Observable<string[]>;

  constructor(
    private auth: AuthService,
    private route:ActivatedRoute,
    private router:Router,
    public whitelabel: WhitelabelService,
    private routes: RoutesService,
  ) { }

  ngOnInit(): void {
    // console.log('Component initializing');
    this.loadAssignees();
    this.loadReportingCategories();
    this.autoLoad();
    this.initCurrentPresence();
    // Set up heartbeat for presence updates
    this.newIssueCheckInterval = setInterval(() => {
      this.refresh({queryNewer: true})
    }, 60*2*1000);
    this.wsPresenceHeartbeatInterval = setInterval(() => {
      if (this.selectedIssue) {
        // console.log('Heartbeat: updating presence for issue', this.selectedIssue.ric_id);
        this.updateCurrentPresence(this.selectedIssue.ric_id);
      }
    }, 30000); // Update every 30 seconds
    this.setupAssigneeFilter();
    this.setupKeywordFilter();
  }

  ngOnDestroy(): void {
    // console.log('Component destroying');
    if (this.routeQuerySub) {
      this.routeQuerySub.unsubscribe();
    }
    // Only remove presence when component is actually being destroyed
    if (this.selectedIssue) {
      const userInfo = this.auth.user().getValue();
      if (userInfo) {
        removePresenceRecord(this.whitelabel.context, this.selectedIssue.ric_id, userInfo.uid);
      }
    }
    this.destroyWsPresence();
  }

  async autoLoad(){
    if (this.isAutoLoad){
      await this.refresh();
      this.routeQuerySub = this.route.queryParams.subscribe( (routeParams:IRouteParam) => {
        if (routeParams['ric_id']){
          this.selectReportedIssueByRicId(+routeParams['ric_id'])
        }
      });
    }
  }

  selectReportedIssueByRicId(ric_id:number){
    for (let record of this.records){
      if (+record.ric_id === +ric_id){
        this.selectIssue(record);
        break;
      }
    }
  }

  reportingCategories:any[] = [];
  async loadReportingCategories(){
    this.reportingCategories = await this.auth.apiFind('public/test-ctrl/schools/reported-issues-categories')
  }



  async refresh(options?:{queryOlder?:boolean, queryNewer?:boolean}){
    this.isLoading = true;
    let isAdditive = false;

    const queryParams = { 
      ... this.roleContext, 
      // skipMultiplier: this.recordLimit 
    }

    const ric_ids = (this.records || []).map(r => r.ric_id)
    if (options?.queryOlder){
      isAdditive = true;
      queryParams.max_ric_id = Math.min(...ric_ids) 
    }
    if (options?.queryNewer){
      isAdditive = true;
      queryParams.min_ric_id = Math.max(...ric_ids) 
    }

    // ingest records
    let allowGridRefresh = false;
    const newRecords = await this.auth.apiFind('public/test-ctrl/schools/reported-issues', {query: queryParams});
    if (isAdditive){
      if (options?.queryNewer){
        this.records = [...newRecords, ...this.records]
      }
      else {
        this.records = [...this.records, ...newRecords]
      }
    }
    else {
      this.records = newRecords;
      allowGridRefresh = true;
    }
    // initialize detailed records
    this.recordsDetailed = []
    this.records.forEach(record =>{
      record.students.forEach(student => {
        this.recordsDetailed.push({
          ... record,
          ... student,
        })
      })
      // Initialize available keywords from existing records
      if (record.keywords) {
        this.getKeywordsArray(record.keywords).forEach(keyword => this.availableKeywords.add(keyword));
      }
    });
    this.agg.loadRecords(this.records);

    
    // apply default filters
    if (allowGridRefresh){
      setTimeout(() => {
        try {
          this.gridOptions.api.refreshCells({})
        } catch(e) {}
        this.applyDefaultResolvedFilter()
      }, 500)
    }
    this.isIniting = false;
    this.isLoading = false;
  }

  exportCsv(){
    this.gridOptions.api.exportDataAsCsv();
  }

  isResolvedShown:boolean;
  applyDefaultResolvedFilter(){
    const filterSessionType = this.gridOptions.api.getFilterInstance('ts_slug'); 
    const filterIsResolved = this.gridOptions.api.getFilterInstance('is_resolved'); 
    
    if (this.isResolvedShown){
      filterIsResolved.setModel({ });
    }
    else {
      filterIsResolved.setModel({
        filter: "1",
        filterType: "text",
        type: "notContains",
      });
    }

    if (this.isPracticeSessionsIncluded){
      filterSessionType.setModel({ });
    }
    else {
      filterSessionType.setModel({
        filter: "sample",
        filterType: "text",
        type: "notContains",
      });
    }

    this.gridOptions.api.onFilterChanged();
  }

  availableAssignees:any [];
  selectedAssignedUid;
  async loadAssignees(){
    // console.log('Loading assignees...');
    this.availableAssignees = await this.auth.apiFind('public/test-ctrl/schools/reported-issues-assignees');
    // console.log('Loaded assignees:', this.availableAssignees);
    // Initialize the filter after loading assignees
    this.setupAssigneeFilter();
  }

  isAssigning:boolean;
  async assignIssue(){
    const issue = this.selectedIssue;
    if (issue.assigned_uid != this.selectedAssignedUid){
      this.isAssigning = true;
      const assigned_uid = this.selectedAssignedUid;
      let assigneeName = ''; 
      for (let assignee of this.availableAssignees){
        if (assignee.uid == assigned_uid){
          assigneeName = assignee.assigned_name
        }
      }
      await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
        patchType: PatchType.ASSIGNMENT,
        assigned_uid,
        assigneeName,
      });
      issue.assigned_uid = this.selectedAssignedUid
      this.isAssigning = false;
    }
  }
  async dismissIssue(){
    const issue = this.selectedIssue;
    await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
      patchType: PatchType.RESOLUTION,
      isResolved: true,
      resolution_slug: ResolutionStatus.DISMISSED
    });
    issue.is_resolved = 1
  }
  async unresolveIssue(){
    const issue = this.selectedIssue;
    await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
      patchType: PatchType.RESOLUTION,
      isResolved: false,
    });
    issue.is_resolved = 0
  }
  async resolveIssue(){
    if (confirm('Are you sure that all necessary actions have been taken on this issue?')){
      const issue = this.selectedIssue;
      await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
        patchType: PatchType.RESOLUTION,
        isResolved: true,
        resolution_slug: ResolutionStatus.RESOLVED
      });
    }
  }
  isAnyStudentSelected(){
    let isAnySelected = false;
    if (this.selectedIssue){
      for (let student of this.selectedIssue.students){
        if (student._isSelected){
          isAnySelected = true;
        }
      }
    }
    return isAnySelected
  }
  
  
  keywordEditStart(){
    const issue = this.selectedIssue;
    issue._isEditingKeywords = true;
    issue._isSavingKeywords = false
    issue._newKeywords = issue.keywords;
  }
  async keywordEditSave(){
    const issue = this.selectedIssue;
    issue._isSavingKeywords = true
    const {keywords} = await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
      patchType: PatchType.KEYWORDS,
      newKeywords: issue._newKeywords, 
      oldKeyWords: issue.keywords,
    });
    issue.keywords = keywords;
    issue._isSavingKeywords = false
    issue._isEditingKeywords = false;
    alert('Updated keywords: '+issue._newKeywords)
  }
  keywordEditCancel(){
    const issue = this.selectedIssue;
    issue._isEditingKeywords = false;
  }


  statusEditStart(){
    const issue = this.selectedIssue;
    issue._isEditingStatus = true;
    issue._isSavingStatus = false
    issue._newStatus = issue.resolution_slug;
    issue._newResolved = issue.is_resolved==1;
  }
  async statusEditSave(){
    const issue = this.selectedIssue;
    issue._isSavingStatus = true
    const resolution_slug = issue._newStatus
    const isResolved = issue._newResolved
    await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
      patchType: PatchType.RESOLUTION,
      isResolved,
      resolution_slug,
    });
    issue.resolution_slug = resolution_slug;
    issue._isSavingStatus = false
    issue._isEditingStatus = false;
    alert('Updated status: '+resolution_slug)
  }
  statusEditCancel(){
    const issue = this.selectedIssue;
    issue._isEditingStatus = false;
  }


  async loadClassStudents(){
    const issue = this.selectedIssue;
    const students = await this.getClassStudents(issue);
    const studentRef = new Map();
    for (let studentMeta of students){
      studentRef.set(+studentMeta.uid, studentMeta);
    }
    for (let student of issue.students){
      const studentMeta = studentRef.get(+student.uid);
      if (studentMeta){
        student.num_stu_ex = studentMeta.num_stu_ex;
        student.single_stu_ex_cat = studentMeta.single_stu_ex_cat;
        student.num_stu_item_ex = studentMeta.num_stu_item_ex;
      }
    }
  }

  configureWhitelabelParams() {
    return {query: {whitelabel: this.whitelabel.getWhitelabelFlag()}}
  }

  async getClassStudents(issue){
    // const includeUids = issue.students.map(s => s.uid);
    if (issue.ts_id){
      return this.auth.apiGet('public/test-ctrl/schools/reported-issue-students', issue.ts_id, this.configureWhitelabelParams());
    }
    else {
      return []
    }
  }

  
  isClassRefinementActive:boolean;
  async classRefinementStart(){
    const issue = this.selectedIssue;
    if (this.isClassRefinementActive){
      this.isClassRefinementActive = false;
    }
    else {
      issue._allStudents = []
      this.selectedStudent = null;
      this.isClassRefinementActive = true
      issue._allStudents = await this.getClassStudents(issue)
      for (let student of issue._allStudents){
        for (let _student of this.selectedIssue.students){
          if (student.uid == _student.uid){
            student.isIncluded = true;
          }
        }
      }
    }
  }
  async classRefinementSave(){
    const issue = this.selectedIssue;
    const updatedRic = await this.auth
      .apiPatch('public/test-ctrl/schools/reported-issue-students', 
      this.selectedIssue.ric_id, 
      {
        ric_id: this.selectedIssue.ric_id,
        students: issue._allStudents,
      },
      this.configureWhitelabelParams()
    );
    this.selectedIssue.students = updatedRic.students;
    this.isClassRefinementActive = false
  }
  classRefinementCancel(){
    this.isClassRefinementActive = false
  }

  notYetAvailable(){
    alert('Processing of student exceptions is currently disabled on this test window.')
  }

  exportDetailedCsv(){
    this.gridOptionsDetailed.api.exportDataAsCsv();
  }

  async postComment(){
    const issue = this.selectedIssue;
    if (this.currentComment && issue){
      this.isSendingComment = true
      const comment = await this.auth.apiCreate('public/test-ctrl/schools/reported-issue-comments', {
        ric_id: this.selectedIssue.ric_id,
        comment: this.currentComment
      })
      if (!issue.comments){
        issue.comments = [];
      }
      issue.comments.push(comment)
      this.isSendingComment = false
    }
  }

  onSelected($event: RowNodeEvent){
    const selectedRows = this.gridOptions.api.getSelectedRows();
    this.selectedStudent = null;
    if (selectedRows.length > 0){
      this.selectIssue(selectedRows[0])
    }
    else {
      this.selectedIssue = null;
    }
  }

  async withSelStuPend(){
    const {createdRecords, retrievedRecords} = await this.processStudentExceptionsOnSelectedStudents({
      is_pended: 1,
      category: STU_EX_OVERRIDES.PENDED,
    })
    alert(`Created ${createdRecords.length} new pendings.`)
  }
  async withSelStuOverride(){
    const {createdRecords, retrievedRecords} = await this.processStudentExceptionsOnSelectedStudents({
      is_pended: 0,
      category: STU_EX_OVERRIDES.INDIVIDUAL,
    })
    alert(`Created ${createdRecords.length} new outcome override.`)
  }
  async withSelStuWithhold(){
    const {createdRecords, retrievedRecords} = await this.processStudentExceptionsOnSelectedStudents({
      is_pended: 0,
      category: STU_EX_OVERRIDES.WITHHOLD,
    })
    alert(`Created ${createdRecords.length} new withholdings.`)
  }
  
  async processStudentExceptionsOnSelectedStudents(payload: {is_pended:number, category?:string}){
    const issue = this.selectedIssue
    // capture notes
    const notes = prompt('Notes (optional)')
    // identify students
    let studentUids = [];
    for (let student of issue.students){
      if (this.isAnyStudentSelected()){
        if (student._isSelected){
          studentUids.push(student.uid);
        }
      }
      else {
        studentUids.push(student.uid);
      }
    }
    const queryParams = {query: {... this.roleContext}}
    const {createdRecords, retrievedRecords} = await this.auth
      .apiCreate(this.routes.TEST_CTRL_STUDENT_EXCEPTIONS, {
        ... payload, // is_pended, category
        test_window_id: issue.tw_id,
        uids: studentUids,
        ric_id: issue.ric_id,
        notes,
      }, queryParams);
    // console.log('Bulk student exceptions', {createdRecords, retrievedRecords})
    await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
      patchType: PatchType.RESOLUTION,
      isResolved: true,
      resolution_slug: payload.category
    });
    await this.loadClassStudents();
    return  {createdRecords, retrievedRecords}
    // for (let record of createdRecords){
    //   this.selectedStudent.studentExceptions.push(record)
    // }
  }

  onSubExceptionsChange(){
    // console.log('onSubExceptionsChange')
    this.loadClassStudents()
  }

  private async handlePresenceTransition(newRicId: number): Promise<void> {
    const userInfo = this.auth.user().getValue();
    if (!userInfo) return;

    const ctx = this.whitelabel.context;
    
    // If we're already on this issue, do nothing
    if (this.selectedIssue?.ric_id === newRicId) {
      return;
    }

    // If we're moving from an existing issue, remove its presence
    if (this.selectedIssue) {
      await removePresenceRecord(ctx, this.selectedIssue.ric_id, userInfo.uid);
    }

    // Add presence for the new issue
    await updatePresenceRecord(ctx, newRicId, userInfo.uid, this.auth.getDisplayName(), this.tabSessionHash);
  }

  async selectIssue(selectedIssue: any) {
    // Handle presence transition first
    await this.handlePresenceTransition(selectedIssue.ric_id);

    this.selectedIssue = selectedIssue;
    this.isClassRefinementActive = false;
    this.selectedAssignedUid = this.selectedIssue.assigned_uid;
    
    // Update the assignee control with the current assignee
    const currentAssignee = this.getCurrentAssignee();
    this.assigneeControl.setValue(currentAssignee);
    
    this.defineEmailSubject(selectedIssue);
    this.loadComments();
    this.loadClassStudents()
    this.router.navigate( [], {
      relativeTo: this.route,
      queryParams: {ric_id: selectedIssue.ric_id }, 
      queryParamsHandling: 'merge', 
    });
  }

  getCurrentAssignee() {
    if (!this.selectedIssue?.assigned_uid) return null;
    return this.availableAssignees?.find(a => a.uid === this.selectedIssue.assigned_uid) || null;
  }

  defineEmailSubject(selectedIssue:any){
    const STANDARD_SUBJECT_PREFIX = '# Subject: '
    try {
      if (selectedIssue.msg.startsWith(STANDARD_SUBJECT_PREFIX)){
        const firstLine = selectedIssue.msg.split('\n')[0];
        const subject = firstLine.split(STANDARD_SUBJECT_PREFIX)[0];
        if (subject){
          selectedIssue.email_subject = subject
        }
      }
    }
    catch(e){
      // selectedIssue.email_subject = 'e-Assessment Support'
    }
  }

  async loadComments(){
    // console.log('loadComments')
    const issue = this.selectedIssue;
    if (issue){
      const comments = await this.auth.apiFind('public/test-ctrl/schools/reported-issue-comments', {
        query: {
          ric_id: issue.ric_id,
        }
      })
      issue.comments = comments; 
    }
  }

  selectStudent(student){
    // const issue = this.selectedIssue;
    if (this.selectedStudent === student){
      this.selectedStudent = null;
    }
    else {
      this.selectedStudent = student;
    }
  }

  getSelectedStudentIndex(){
    return this.selectedIssue.students.indexOf(this.selectedStudent)
  }

  isSelectedStudent(student){
    return this.selectedStudent === student
  }
  
  selectAdjStudent(dir){
    const targetStudent = this.selectedIssue.students[this.getSelectedStudentIndex()+dir];
    if (targetStudent){
      this.selectStudent(targetStudent)
    }
  }
  isAddingComment:boolean;
  async addComment(){
    const issue = this.selectedIssue;
    if (issue){
      this.isAddingComment = true;
      const comment = this.currentComment;
      const newComment = await this.auth.apiCreate('public/test-ctrl/schools/reported-issue-comments', {
        ric_id: issue.ric_id,
        comment,
        shouldSendEmail: issue.shouldSendEmail && issue.invigilator_email,
        contact_email: issue.invigilator_email,
        email_subject: issue.email_subject,
      })
      issue.comments.push(newComment); 
      this.currentComment = '';
      this.isAddingComment = false;
    }
  }
  setDefaultEmailSubject(){
    this.selectedIssue.email_subject = 'e-Assessment Support'
  }
  
  renderTime(rawTime){
    return rawTime
  }
  
  newIssueStart(){
    this.newIssue = {}
  }
  newIssueCancel(){
    this.newIssue = null
  }
  async newIssueConfirm(){
    const {msg, isSaving} = this.newIssue;
    if (msg && !isSaving){
      this.newIssue.isSaving = true
      const newIssue = await this.auth.apiCreate('public/user-authenticated/report-issue', {msg})
      // todo: incremental refresh
      this.newIssue = null
    }
  }

  async initCurrentPresence() {
    // console.log('Initializing presence tracking');
    const ctx = this.whitelabel.context;
    // console.log('Using context:', ctx);
    const presenceObservable = observePresenceRecords(ctx);
    this.wsPresenceSub = presenceObservable.subscribe({
      next: (records) => {
        // console.log('Received presence records:', records);
        this.cachePresenceMappings(records);
        // console.log('Updated presence records:', records);
      },
      error: (error) => {
        console.error('Error observing presence records:', error);
      }
    });
  }

  cachePresenceMappings(records: FBC_RI_PresenceRecord[]) {
    // console.log('Caching presence mappings for', records.length, 'records');
    this.ricIdPresence = new Map();
    records.forEach(record => {
      if (record.ric_id) {
        // console.log('Caching record for ricId:', record.ric_id);
        this.ricIdPresence.set(record.ric_id, record);
      }
    });
    // console.log('Current ricIdPresence map size:', this.ricIdPresence.size);
  }

  destroyWsPresence() {
    // console.log('Destroying presence tracking');
    if (this.wsPresenceSub) {
      this.wsPresenceSub.unsubscribe();
      this.wsPresenceSub = null;
    }
    if (this.wsPresenceHeartbeatInterval) {
      clearInterval(this.wsPresenceHeartbeatInterval);
      this.wsPresenceHeartbeatInterval = null;
    }
    if (this.newIssueCheckInterval) {
      clearInterval(this.newIssueCheckInterval);
      this.newIssueCheckInterval = null;
    }
  }

  getPresenceInfo(ricId: number): string {
    const record = this.ricIdPresence.get(ricId);
    if (!record || !record.users || record.users.length === 0) return '';
    
    // Filter out the current user from the list
    const currentUser = this.auth.user().getValue();
    const otherUsers = record.users.filter(user => user.uid !== currentUser?.uid);
    
    if (otherUsers.length === 0) return '';
    return otherUsers.map(user => user.display_name).join(', ');
  }

  getPresenceIndicators(ricId: number): { initials: string, isSelf: boolean }[] {
    // console.log('getPresenceIndicators called for ricId:', ricId);
    // console.log('Current ricIdPresence map:', this.ricIdPresence);
    const record = this.ricIdPresence.get(ricId);
    // console.log('Found record for ricId:', record);
    if (!record || !record.users || record.users.length === 0) {
      // console.log('No record or users found, returning empty array');
      return [];
    }
    
    const currentUser = this.auth.user().getValue();
    // console.log('Current user:', currentUser);
    const result = record.users.map(user => ({
      initials: user.initials || user.display_name.split(' ').map(n => n[0]).join('').toUpperCase(),
      display_name: user.display_name,
      isSelf: user.uid === currentUser?.uid
    }));
    // console.log('Returning presence indicators:', result);
    return result;
  }

  isIssueInUse(ricId: number): boolean {
    const record = this.ricIdPresence.get(ricId);
    if (!record || !record.users || record.users.length === 0) return false;
    
    const currentUser = this.auth.user().getValue();
    return record.users.some(user => user.uid !== currentUser?.uid);
  }

  // testIssueEmailExtend(){
  //   this.auth.apiCreate('public/user-authenticated/report-issue', {
  //     msg: 'side test',
  //     email_subject: 'Re: [VT#4252] e-Assessment Support',
  //     contact_email: '<EMAIL>',
  //     categorySelection: 'EMAIL',
  //     reportedIssueId: "4252",
  //     thread_hash: "<EMAIL>:197125c676527d5f",
  //   })
  // }

  async updateCurrentPresence(ricId: number) {
    // console.log('Updating presence for ricId:', ricId);
    const userInfo = this.auth.user().getValue();
    if (!userInfo) {
      // console.log('No user info available');
      return;
    }
    const ctx = this.whitelabel.context;
    try {
      await updatePresenceRecord(ctx, ricId, userInfo.uid, this.auth.getDisplayName(), this.tabSessionHash);
      // console.log('Successfully updated presence record');
    } catch (error) {
      console.error('Error updating presence record:', error);
    }
  }

  setupAssigneeFilter() {
    // console.log('Setting up assignee filter with assignees:', this.availableAssignees);
    this.filteredAssignees = this.assigneeControl.valueChanges.pipe(
      startWith(''),
      map(value => {
        const filtered = this._filterAssignees(value);
        // console.log('Filtered assignees:', filtered);
        return filtered;
      })
    );
  }

  private _filterAssignees(value: string | any): any[] {
    // Handle both string input and object selection
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    const searchStr = filterValue || '';
    
    if (!this.availableAssignees) {
      // console.log('No available assignees found');
      return [];
    }

    const filtered = this.availableAssignees.filter(assignee => 
      assignee.assigned_name?.toLowerCase().includes(searchStr) ||
      assignee.assigned_email?.toLowerCase().includes(searchStr)
    );
    
    // console.log('Filtering assignees:', {
    //   searchStr,
    //   totalAssignees: this.availableAssignees.length,
    //   filteredCount: filtered.length
    // });
    
    return filtered;
  }

  onAssigneeSelected(assignee: any) {
    // console.log('Assignee selected:', assignee);
    this.selectedAssignedUid = assignee?.uid || null;
    this.assignIssue();
  }

  setupKeywordFilter() {
    this.filteredKeywords = this.keywordControl.valueChanges.pipe(
      startWith(''),
      map(value => {
        if (typeof value === 'string') {
          return this._filterKeywords(value);
        }
        return [];
      })
    );
  }

  private _filterKeywords(value: string): string[] {
    const filterValue = value.toLowerCase();
    return Array.from(this.availableKeywords).filter(keyword => 
      keyword.toLowerCase().includes(filterValue)
    );
  }

  getKeywordsArray(keywords: string): string[] {
    return keywords ? keywords.split(',').map(k => k.trim()).filter(k => k) : [];
  }

  async deleteKeyword(keyword: string) {
    const keywords = this.getKeywordsArray(this.selectedIssue.keywords);
    const newKeywords = keywords.filter(k => k !== keyword).join(', ');
    await this.updateKeywords(newKeywords);
  }

  async addKeyword(keyword: string) {
    if (!keyword) return;
    
    const trimmedKeyword = keyword.trim();
    if (!trimmedKeyword) return;

    const keywords = this.getKeywordsArray(this.selectedIssue.keywords);
    if (keywords.includes(trimmedKeyword)) return;

    if (!this.availableKeywords.has(trimmedKeyword)) {
      const confirmed = confirm(`"${trimmedKeyword}" is not in the list of known keywords. Add it anyway?`);
      if (!confirmed) return;
    }

    keywords.push(trimmedKeyword);
    await this.updateKeywords(keywords.join(', '));
    this.keywordControl.setValue('');
  }

  private async updateKeywords(newKeywords: string) {
    const issue = this.selectedIssue;
    issue._isSavingKeywords = true;
    try {
      const {keywords} = await this.auth.apiPatch('public/test-ctrl/schools/reported-issues', issue.ric_id, {
        patchType: PatchType.KEYWORDS,
        newKeywords,
        oldKeyWords: issue.keywords,
      });
      issue.keywords = keywords;
      // Update available keywords set
      this.getKeywordsArray(keywords).forEach(keyword => this.availableKeywords.add(keyword));
    } finally {
      issue._isSavingKeywords = false;
    }
  }

  onKeywordInput(event: any) {
    if (event.key === 'Enter') {
      const value = this.keywordControl.value;
      if (value) {
        this.addKeyword(value);
      }
    }
  }

}
