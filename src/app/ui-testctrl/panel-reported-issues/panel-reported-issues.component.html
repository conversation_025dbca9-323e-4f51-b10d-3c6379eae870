<div class="space-between">
    <div>
        <div *ngIf="!newIssue">
            <button class="button" (click)="newIssueStart()">
                Report New Issue
            </button>
        </div>
        <div *ngIf="newIssue">
            <p>You can post quick messages here (not associated to a window or student, but is tracked in the global tracker)</p>
            <textarea class="textarea" [(ngModel)]="newIssue.msg"></textarea>
            <div>
                <button (click)="newIssueConfirm()" class="button">Save</button>
                <button (click)="newIssueCancel()" class="button">Cancel</button>
            </div>
        </div>

        <!-- temp -->
        <!-- <button (click)="testIssueEmailExtend()" class="button is-small">test</button> -->

    </div>
    <div>
        <mat-slide-toggle [(ngModel)]="isStructuredView">
            Structured View
        </mat-slide-toggle>
    </div>
    <div>
        <!-- <span *ngIf="records && records.length">
            <mat-slide-toggle [(ngModel)]="isDetailedMode">
                Activate Detailed View (one row per student)
            </mat-slide-toggle>
        </span> -->
        <button
            (click)="refresh({queryNewer:true})"
            class="button is-small"
            [disabled]="isLoading"
        >
            Refresh
        </button>
    </div>
</div>

<hr>

<div  *ngIf="isIniting" class="notification">
Loading...
</div>

<ng-container *ngIf="isCreating">

</ng-container>

<ng-container *ngIf="!isCreating">
    <div *ngIf="records && records.length">
        <div class="space-between align-top">

            <ng-container *ngIf="!isStructuredView" id="grid-view">
                <div class="grid-container" [class.is-hidden]="!isDetailedMode">
                    <ag-grid-angular
                        class="ag-theme-alpine ag-grid-fullpage"
                        [rowData]="recordsDetailed"
                        [gridOptions]="gridOptionsDetailed"
                        [enableCellTextSelection]="true"
                    ></ag-grid-angular>
                    <div style="margin: 0.5em 0em;">
                        <button class="button is-light" (click)="exportDetailedCsv()">
                            Export Student Detail
                        </button>
                    </div>
                </div>
                <div class="grid-container" [class.is-hidden]="isDetailedMode">
                    <ag-grid-angular
                        class="ag-theme-alpine ag-grid-fullpage"
                        style="flex-grow: 1;"
                        [rowData]="records"
                        [gridOptions]="gridOptions"
                        (selectionChanged)="onSelected($event)"
                        [enableCellTextSelection]="true"
                    ></ag-grid-angular>
                    <div style="margin: 0.5em 0em;" class="space-between">
                        <div>
                            <div>
                                <mat-slide-toggle 
                                    [(ngModel)]="isResolvedShown"
                                    (change)="applyDefaultResolvedFilter()"
                                > Include Issues that have been Actioned? </mat-slide-toggle>
                            </div>
                            <div>
                                <mat-slide-toggle 
                                    [(ngModel)]="isPracticeSessionsIncluded"
                                    (change)="applyDefaultResolvedFilter()"
                                > Include Practice/Sample Assessments? </mat-slide-toggle>
                            </div>
                        </div>
                        <button class="button is-light" (click)="exportCsv()">
                            Export Reported Issues
                        </button>
                    </div>
                </div>
            </ng-container>
            <ng-container *ngIf="isStructuredView" id="structured-view">
                <div class="structured-view">
                    <div class="reported-issue-agg">

                        <div>
                            <strong>
                                Grouping By
                            </strong>
                            <mat-radio-group [(ngModel)]="agg.settings.groupingBy" (change)="agg.updateGroupings()" class="radio-group">
                                <div class="radio-options">
                                    <mat-radio-button *ngFor="let groupBy of groupByTypesList" [value]="groupBy.id" class="radio-option">
                                        {{groupBy.caption}}
                                    </mat-radio-button>
                                </div>
                            </mat-radio-group>
                        </div>
                        <div *ngIf="false">
                            Filter Options:
                            - Date Range
                            - Email
                            - Issue Description
                            - Assignee
                            - Responders
                            - School
                            - School Authority
                            - Pending Requesting Callback
                            - Assessment Code 
                            - Status 
                            - Resolution 
                            - ResolutionTest Window
                            ... more
                        </div>
                        <!-- 
                        <div>
                            <div>Top Keywords</div>
                            <ul>
                                <li>
                                    <span class="label">KEYWORD</span>
                                    x20
                                </li>
                            </ul>                            
                        </div>
                        <div>
                            <div>Top Common Causes</div>
                            <ul>
                                <li>
                                    <code>cause</code>
                                    <small>
                                        <span class="label">ASSOC_KEYWORD</span>
                                        x20
                                    </small>
                                </li>
                            </ul>  
                        </div> 
                        <div>
                            <div>Resolution Times</div>
                            <ul>
                                <li></li>
                            </ul>                            
                        </div>
                        <div>
                            <div>Response Times</div>
                            <ul>
                                <li></li>
                            </ul>                            
                        </div>
                        -->
                    </div>
                    <div class="issue-group-container">
                        <div *ngFor="let grouping of agg.groupings">
                            <div class="grouping-label">
                                <button 
                                    class="button is-small"
                                    (click)="groupingsCollapsed[grouping.key] = !groupingsCollapsed[grouping.key]"
                                    [class.is-info]="groupingsCollapsed[grouping.key]"
                                >
                                    {{grouping.caption}} 
                                </button>
                                ({{grouping.n}})
                            </div>
                            <div class="issue-list-container" *ngIf="!groupingsCollapsed[grouping.key]">
                                <div *ngFor="let record of grouping.records" 
                                    class="issue-list-item" 
                                    (click)="selectIssue(record)"
                                    [class.is-selected]="selectedIssue && selectedIssue.ric_id === record.ric_id"
                                    [class.is-resolved]="record.is_resolved==1"
                                    [class.is-dismissed]="record.resolution_slug=='DISMISSED'"
                                    [class.is-in-use]="isIssueInUse(record.ric_id)"
                                >
                                    <div class="space-between">
                                        <code>{{record.ric_id}}</code>
                                        <span class="label">{{record.resolution_slug}}</span>
                                    </div>
                                    <small><strong>{{record.keywords}}</strong></small>
                                    <p>{{record.msg}}</p>
                                    <div class="presence-indicators">
                                        <div *ngFor="let presence of getPresenceIndicators(record.ric_id)" 
                                             class="presence-initial"
                                             [class.self]="presence.isSelf">
                                            {{presence.initials}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>
                            
            <div *ngIf="selectedIssue" class="reported-issue-main">
                <div class="main-panel-active-agents">
                    <strong>Active Agents:</strong>
                    <span 
                        *ngFor="let presence of getPresenceIndicators(selectedIssue.ric_id)" 
                        class="tag is-small"
                        [class.is-danger]="!presence.isSelf"
                        [class.is-dark]="presence.isSelf"
                    >
                        {{presence.display_name}}
                    </span>
                </div>
                <table>
                    <tr>
                        <th>ID</th>
                        <td>{{selectedIssue.ric_id}}</td>
                    </tr>
                    <tr>
                        <th>Date</th>
                        <td>{{selectedIssue.created_on}}</td>
                    </tr>
                    <tr>
                        <th>Contact Email</th>
                        <td>{{  selectedIssue.invigilator_email}}</td>
                    </tr>
                    <tr>
                        <th>Assigned To</th>
                        <td>
                            <mat-form-field class="assignee-field">
                                <input type="text"
                                       matInput
                                       [formControl]="assigneeControl"
                                       [matAutocomplete]="auto"
                                       placeholder="Search assignee..."
                                       class="input is-small">
                                <mat-autocomplete #auto="matAutocomplete" 
                                                 (optionSelected)="onAssigneeSelected($event.option.value)"
                                                 [displayWith]="displayAssigneeFn">
                                    <mat-option [value]="null">Unassigned</mat-option>
                                    <mat-option *ngFor="let assignee of filteredAssignees | async" [value]="assignee">
                                        {{assignee.assigned_name}} ({{assignee.assigned_email}})
                                    </mat-option>
                                </mat-autocomplete>
                            </mat-form-field>
                            <span *ngIf="isAssigning" class="tag">Assigning...</span>
                        </td>
                    </tr>
                    <tr *ngIf="selectedIssue.s_code || selectedIssue.sc_name || selectedIssue.ts_slug">
                        <th>Session</th>
                        <td>
                            {{selectedIssue.sd_name}} ({{selectedIssue.sd_code}}) <br>
                            {{selectedIssue.s_name}} ({{selectedIssue.s_code}}) <br>
                            Class: {{selectedIssue.sc_name}} ({{selectedIssue.ts_slug}}) <br>
                        </td>
                    </tr>
                    <tr>
                        <th>Category</th>
                        <td>{{selectedIssue.categorySelection}}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <div class="space-between" *ngIf="!selectedIssue._isEditingStatus">
                                <span 
                                    class="tag" 
                                    [class.is-success]="selectedIssue.is_resolved==1" 
                                    [class.is-danger]="selectedIssue.is_resolved==0"
                                >{{selectedIssue.resolution_slug}}</span>
                                <button (click)="statusEditStart()" class="button is-small">Edit</button>
                            </div>
                            <div *ngIf="selectedIssue._isEditingStatus">
                                <div class="space-between">
                                    <input [(ngModel)]="selectedIssue._newStatus" type="text" class="input is-small" maxlength="255" style="background-color: #333; color: #fff">
                                    <mat-slide-toggle [(ngModel)]="selectedIssue._newResolved">Resolved?</mat-slide-toggle>
                                </div>
                                <div class="buttons">
                                    <button (click)="statusEditSave()"   [disabled]="selectedIssue._isSavingStatus" class="button is-small">Save</button>
                                    <button (click)="statusEditCancel()" [disabled]="selectedIssue._isSavingStatus" class="button is-small">Cancel</button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th>Keywords</th>
                        <td>
                            <div class="keywords-container">
                                <div class="keywords-list">
                                    <span *ngFor="let keyword of getKeywordsArray(selectedIssue.keywords)" class="tag is-info is-medium">
                                        {{keyword}}
                                        <button class="delete is-small" (click)="deleteKeyword(keyword)"></button>
                                    </span>
                                </div>
                                <div class="keyword-input">
                                    <mat-form-field class="keyword-field">
                                        <input type="text"
                                               matInput
                                               [formControl]="keywordControl"
                                               [matAutocomplete]="keywordAuto"
                                               (keydown.enter)="onKeywordInput($event)"
                                               placeholder="Add keyword..."
                                               class="input is-small">
                                        <mat-autocomplete #keywordAuto="matAutocomplete" 
                                                         (optionSelected)="addKeyword($event.option.value)">
                                            <mat-option *ngFor="let keyword of filteredKeywords | async" [value]="keyword">
                                                {{keyword}}
                                            </mat-option>
                                        </mat-autocomplete>
                                    </mat-form-field>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                <div class=" notification is-small is-light">
                    <div class="space-between">
                        <strong>Description of the Issue:</strong>
                        <button *ngIf="selectedIssue.is_resolved==0" (click)="dismissIssue()" class="button is-small is-danger" >Dismiss Issue</button>
                        <button *ngIf="selectedIssue.is_resolved==1" (click)="unresolveIssue()" class="button is-small is-warning" >Unresolve Issue</button>
                    </div>
                    <div >
                        <issue-thread-block [message]="selectedIssue.msg"></issue-thread-block>
                    </div>
                </div>
                <div *ngIf="!isClassRefinementActive" class="space-between">
                    <strong>Students ({{selectedIssue.students.length}}):</strong>
                    <button class="button is-small is-light" (click)="isStudentListingExpanded=!isStudentListingExpanded">
                        <span class="icon">
                            <i class="fas" [class.fa-chevron-down]="!isStudentListingExpanded" [class.fa-chevron-up]="isStudentListingExpanded"></i>
                        </span>
                        <span>{{isStudentListingExpanded ? 'Collapse' : 'Expand'}}</span>
                    </button>
                </div>
                <div *ngIf="isStudentListingExpanded">
                    <button 
                        (click)="classRefinementStart()"
                        class="button is-small"
                    >
                        Add/Remove Students from Class
                    </button>
                    <div *ngIf="isClassRefinementActive">
                        <table class="table is-small is-bordered">
                            <tr>
                                <th>Include?</th>
                                <th>Student Name</th>
                                <th><tra slug="{{whitelabel.isABED() ? 'student_account_num_abed' : 'pc_lbl_student_oen'}}"></tra></th>
                                <th>Exceptions</th>
                                <th>View</th>
                            </tr>
                            <tr *ngFor="let student of selectedIssue._allStudents">
                                <td>
                                    <mat-slide-toggle [(ngModel)]="student.isIncluded">
                                        {{student.isIncluded ? 'Included' : "Excluded"}}
                                    </mat-slide-toggle>
                                </td>
                                <td>
                                    <span>{{student.first_name}} {{student.last_name}}</span>
                                </td>
                                <td>
                                    {{whitelabel.isABED() ? student.StudentASN : student.StudentOEN}}
                                </td>
                                <td>
                                    <div class="tags">
                                        <span *ngIf="student.num_stu_ex > 0" class="tag is-dark">{{student.single_stu_ex_cat}}</span>
                                        <span *ngIf="student.num_stu_ex > 1" class="tag is-info">Excess Overrides</span>
                                        <span *ngIf="student.num_stu_item_ex > 0" class="tag is-info">{{student.num_stu_item_ex}} item exception(s)</span>
                                    </div>
                                </td>
                                <td>
                                    <button 
                                        (click)="selectStudent(student)" 
                                        class="button is-small"
                                        [class.is-info]="isSelectedStudent(student)"
                                    >Responses</button>
                                </td>
                            </tr>
                        </table>
                        <div class="buttons">
                            <button (click)="classRefinementSave()" class="button is-small">Save</button>
                            <button (click)="classRefinementCancel()" class="button is-small">Cancel</button>
                        </div>
                    </div>
                    <div *ngIf="!isClassRefinementActive">
                        <table class="table is-small is-bordered">
                            <tr>
                                <th>Student Name</th>
                                <th><tra slug="{{whitelabel.isABED() ? 'student_account_num_abed' : 'pc_lbl_student_oen'}}"></tra></th>
                                <th>Exceptions</th>
                                <th>View</th>
                            </tr>
                            <tr *ngFor="let student of selectedIssue.students">
                                <td>
                                    <label class="checkbox">
                                        <input type="checkbox" [(ngModel)]="student._isSelected">
                                        <span>{{student.first_name}} {{student.last_name}}</span>
                                    </label>
                                </td>
                                <td>{{student.StudentIdentificationNumber}}</td>
                                <td>
                                    <div class="tags">
                                        <span *ngIf="student.num_stu_ex > 0" class="tag is-dark">{{student.single_stu_ex_cat}}</span>
                                        <span *ngIf="student.num_stu_ex > 1" class="tag is-info">Excess Overrides</span>
                                        <span *ngIf="student.num_stu_item_ex > 0" class="tag is-info">{{student.num_stu_item_ex}} item exception(s)</span>
                                    </div>
                                </td>
                                <td>
                                    <button 
                                        (click)="selectStudent(student)" 
                                        class="button is-small"
                                        [class.is-info]="isSelectedStudent(student)"
                                    >Responses</button>
                                </td>
                            </tr>
                        </table>
                        <!-- <button class="button" (click)="isShowUid = !isShowUid">Show UIDs</button> -->
                        <div>
                            <em>With {{isAnyStudentSelected() ? 'selected' : 'all'}} students...</em>
                            <div class="buttons">
                                <button (click)="withSelStuPend()" class="button is-small">Pend</button>
                                <!-- <button (click)="notYetAvailable()" class="button is-small">Pro-rate</button> -->
                                <button (click)="withSelStuOverride()" class="button is-small">Override</button>
                                <button (click)="withSelStuWithhold()" class="button is-small">Withhold</button>
                            </div>
                        </div>
                    </div>
                    <hr> 
                </div>
                <div>
                    <strong>Comments</strong>
                    <div style="padding-left:1.5em; padding-top:0.5em;">
                        <div 
                            *ngFor="let commentEntry of selectedIssue.comments"
                            class="comment-entry"
                        >
                            <div class="comment-id">
                                <div class="name-bubble"></div>
                                <a href="mailto:{{commentEntry.contact_email}}">{{commentEntry.first_name}} {{commentEntry.last_name}}</a>
                                <span class="comment-time"> {{renderTime(commentEntry.created_on)}} </span>
                            </div>
                            <div>
                                <issue-thread-block 
                                    [message]="commentEntry.comment"
                                    [isFramed]="commentEntry.is_auto==0"
                                    [isNote]="commentEntry.is_email_sent==0"
                                ></issue-thread-block>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div>
                            <textarea 
                                [(ngModel)]="currentComment" 
                                [disabled]="isAddingComment"
                                class="textarea is-small"
                            ></textarea>

                        </div>
                        <div style="background-color:#ccc; text-align:right; padding:0.5em;">
                            <div *ngIf="selectedIssue.shouldSendEmail" class="mb-1">
                                <table>
                                    <tr>
                                        <td><span class="tag">To:</span></td>
                                        <td><input class="input" [(ngModel)]="selectedIssue.invigilator_email"></td>
                                    </tr>
                                    <tr>
                                        <td><span class="tag">Subject:</span></td>
                                        <td><input class="input" [(ngModel)]="selectedIssue.email_subject"><button (click)="setDefaultEmailSubject()">default</button></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="space-between">
                                <div>
                                    <div class="mt-2" *ngIf="selectedIssue.invigilator_email">
                                        <mat-slide-toggle [(ngModel)]="selectedIssue.shouldSendEmail">
                                            Send email
                                        </mat-slide-toggle>
                                    </div>
                                </div>
                                <button 
                                    (click)="addComment()" 
                                    [disabled]="isAddingComment" 
                                    class="button is-small"
                                >
                                    <span *ngIf="selectedIssue.shouldSendEmail">Reply</span>
                                    <span *ngIf="!selectedIssue.shouldSendEmail">Note</span>
                                </button>
                            </div>
                        </div>
                        <br/>
                        <small>Use the box above to enter a comment on this reported issue for internal resolution. This comment will not be shared with the school or invigilator.</small>
                    </div>
                    <hr>
                    <button 
                        (click)="resolveIssue()" 
                        *ngIf="selectedIssue.is_resolved==0"
                        class="button is-success is-inverted"
                    >Mark as Resolved</button>
                    <span  *ngIf="selectedIssue.is_resolved==1">Resolution: {{selectedIssue.resolution_slug}}</span>
                </div>
            </div>
            <div *ngIf="selectedStudent">
                <div class="space-between">
                    <div>Student {{getSelectedStudentIndex()+1}} of {{selectedIssue.students.length}}</div>
                    <div>
                        <button (click)="selectAdjStudent(-1)" class="button is-light is-small">Prev</button>
                        <button (click)="selectAdjStudent(1)" class="button is-light is-small">Next</button>
                    </div>
                </div>
                <hr>
                <panel-student-lookup 
                    [roleContext]="roleContext" 
                    [isStudentFocus]="true"
                    [uid]="selectedStudent.uid"
                    [ts_id]="selectedIssue.ts_id"
                    [ric_id]="selectedIssue.ric_id"
                    (exceptionsChange)="onSubExceptionsChange()"
                ></panel-student-lookup>
            </div>
        </div>
    </div>
</ng-container>

