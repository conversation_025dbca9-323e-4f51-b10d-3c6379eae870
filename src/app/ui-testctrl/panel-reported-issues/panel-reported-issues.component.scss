.record-limit-info {
    display: inline-flex;
    min-height: 30px;
    align-items: center;
    padding-left: 5px;
}

.comment-entry {
    margin-bottom: 1.0em;
    position: relative;
    .name-bubble {
        position: absolute;
        left: -4em;
        width: 1em;
        height: 1em;
        color: #333;
        border-radius: 100%;
    }
    .comment-id {
        font-size: 0.8em;
        .comment-time {
            color: #ccc;
        }
    }
}

.grid-container {
    flex-grow: 2;
}

.reported-issue-main {
    width:44em; 
    min-width:44em; 
    padding:2em; 
    align-self: flex-start; 
    max-height: 90vh;
    overflow: auto;
}

table.table.is-bordered {
    th {
        background-color: #f1f1f1;
    }
}

.structured-view {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    max-height:90vh;
    overflow: auto;
}
.reported-issue-agg {
    width:20em;
    min-width:20em;
    margin-right:2em;
}
.issue-group-container {
    display: flex;
    flex-direction: column;
    gap: 1em;
    .grouping-label {
        font-weight: bold;
    }
    .issue-list-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.5em;
        .issue-list-item {
            width:10em;
            height:7em;
            padding:0.5em;
            border:1px solid #ccc;
            border-radius: 0.5em;
            background-color: #fff;
            color: #333;
            code {
                font-size: 0.7em;
            }
            overflow:hidden;
            cursor: pointer;
            position: relative;
            &:hover {
                border:1px solid #0ac;
                background-color: rgb(198, 228, 234);
            }
            &.is-resolved {
                background-color: rgb(113, 206, 90);
            }
            &.is-dismissed {
                background-color: #aaa;
                color: #333
            }
            &.is-selected {
                border:1px solid #0ac;
                background-color: rgb(64, 64, 64);
                color: #fff;
                strong {
                    color: #fff;
                }
            }
            &.is-in-use {
                background-color: #fff3cd !important; // Yellow background that overrides other states
            }
            .presence-indicators {
                position: absolute;
                bottom: 0.2em;
                right: 0.2em;
                display: flex;
                gap: 0.2em;
                .presence-initial {
                    width: 1.2em;
                    height: 1.2em;
                    border-radius: 50%;
                    background-color: #f00;
                    color: white;
                    font-size: 0.7em;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    &.self {
                        background-color: #666;
                    }
                }
            }
        }
    }
}

.radio-group {
  margin-top: 8px;
  
  .radio-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0em 2em ;
    .radio-option {
      white-space: nowrap;
      margin-right: 0.5em;
    }
  }
}

.main-panel-active-agents {
    display: flex;
    flex-direction: row;
    gap: 0.5em;
    margin-bottom: 2em;
}

.assignee-field {
  width: 300px;
  
  ::ng-deep {
    .mat-form-field-wrapper {
      margin: 0;
      padding: 0;
    }
    
    .mat-form-field-infix {
      padding: 0;
      border-top: 0;
    }
    
    .mat-form-field-underline {
      display: none;
    }
    
    .mat-form-field-subscript-wrapper {
      display: none;
    }
  }
}

.keywords-container {
  display: flex;
  flex-direction: column;
  gap: 0.5em;

  .keywords-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;

    .tag {
      display: inline-flex;
      align-items: center;
      gap: 0.25em;
      margin: 0;

      .delete {
        margin-left: 0.25em;
        background: none;
        border: none;
        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .keyword-input {
    .keyword-field {
      width: 100%;
      max-width: 300px;
    }
  }
}