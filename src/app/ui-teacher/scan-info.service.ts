import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { IQuestionConfig } from '../ui-item-maker/item-set-editor/models';
import { identifyQuestionResponseEntries } from '../ui-item-maker/item-set-editor/models/expected-answer';
import { ITestDesignPayload } from '../ui-testtaker/view-tt-test-runner/view-tt-test-runner.component';
import { IGenRespSheetConfig } from './scan-button-bar/scan-button-bar.component';
import { EResponseType } from './t-modal-student-scan/t-modal-student-scan.component';
import { LoginGuardService } from '../api/login-guard.service';
import { LangService } from '../core/lang.service';
import { IQuestionScan } from "./view-invigilate/types";

const UPLOAD_COMPLETION_CHECKER_SEC = 10;

const QR_SLUG_PREFIX = "SESSION_"

interface IScanInfoPayload {
  studentScanInfoMap: IStudentScanInfo,
  testSlugs: string[]
}

interface IBulkReqScanInfoContents {
  [uid: number]: {
    file_path: string,
    test_form_id: number
  }
}

interface ISingleScanRes {
  croppedFile: string,
  uncroppedFile: string
}

export interface IPaperResponseInfo {
  url: string
}

type IOnlineResponseInfo = any;
export interface IScanResponse {
  responseType: EResponseType,
  info: IPaperResponseInfo | IOnlineResponseInfo
}
export interface IScanQuestion {
  [test_question_id: number]: any
}

export interface IScanQuesConfig {
  SESSION_A?: IScanQuestion,
  SESSION_B?: IScanQuestion,
  SESSION_C?: IScanQuestion
}

export interface IScanInfo {
  file_path: string,
  scanningQues: IScanQuesConfig,
  scanningQuesIds: string[],
  test_attempt_id: number,
  test_form_id: number
}

export interface IStudentScanInfo {
  [uid:number]: IScanInfo
}
@Injectable({
  providedIn: 'root'
})
export class ScanInfoService {

  constructor(private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService,
    public lang: LangService, 
    private sanitizer: DomSanitizer
  ) { 
    this.qrcodeProcessingComplete.subscribe(async (data) => {
      if (data) {
        await this.startBulkUpload(data);
      }
    })
  }

  private qrcodeProcessingComplete:BehaviorSubject<any> = new BehaviorSubject(null);
  isScanInfoLoaded: boolean = false;
  isUploadingSingle: boolean = false;
  isUploadingBulk: boolean = false;
  isPrintingBulk: boolean = false;
  // isOverwriting: boolean = false;
  isRespSheetTemplateLoaded: boolean = false;
  bulkStudentScanPdf: string; // base 64 string 
  studentScanInfoMap: IStudentScanInfo;
  sessionSlugsSet:any = new Set(); 
  currStudentResponseTemplates: any;
  bulkUploadWaitInterval: NodeJS.Timeout | null;
  bulkUploadQrCodeInterval: NodeJS.Timeout | null;
  timePerUploadCheck: number = UPLOAD_COMPLETION_CHECKER_SEC;
  bulkUploadTimer;
  uploadUntildateEnd?: Date;

  findCrQuestion(scanInfo: any, qid: number): any {
    const scanningQues = scanInfo.scanningQues;
    const quesConfigs = [];
    for (const sec in scanningQues) {
      quesConfigs.push(scanningQues[sec]);
    }

    const qConfig = quesConfigs.find(qConfig => {
      const key = (Object.keys(qConfig))[0];
      return +key === qid;
    });

    return qConfig[qid];
  }

  loadScanInfo(sessionId: number, schl_class_group_id: number) {
    return this.auth.apiGet(this.routes.EDUCATOR_CLASS_SCAN_INFO, sessionId, {
      query: {
        schl_class_group_id
      }
    }).then(async (res: any) => {
      const {studentScanInfoMap, testSlugs}: IScanInfoPayload = res;
      const sessions = [];
      // Find the longest session list from scan info
      for(const studentUid in studentScanInfoMap){
        const scanInfo = studentScanInfoMap[studentUid];
        if(scanInfo && scanInfo.scanningQues){
          for(const sessionKey in scanInfo.scanningQues){
            if(!sessions.includes(sessionKey)){
              sessions.push(sessionKey);
            }
          }
        }
      }
      
      if(testSlugs && studentScanInfoMap){
        // testSlugs.forEach(slug => this.sessionSlugsSet.add(slug));
        sessions.forEach(slug => this.sessionSlugsSet.add(slug))
      }

      this.studentScanInfoMap = studentScanInfoMap;
      this.isScanInfoLoaded = true;
    }).catch((e) => {
      console.log(e);
    })
  }

  loadSingleScanInfo(schl_class_group_id: number, tass_id: number){
    return this.auth.apiFind(this.routes.EDUCATOR_CLASS_SCAN_INFO, {
      query: {
        schl_class_group_id,
        tass_id
      }
    }).then(res => {
      // update this.studentScanInfoMap
      if(Object.keys(res).length === 0){
        return
      }
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].hasTaqr = res.hasTaqr
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isPaperFormat = res.isPaperFormat
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isNoPaperResponse = res.isNoPaperResponse
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isNotLegible = res.isNotLegible
    }).catch((e) => {
      console.log(e);
    })
  }

  base64ToArrayBuffer(base64) {
    let binaryString = window.atob(base64);
    let binaryLen = binaryString.length;
    let bytes = new Uint8Array(binaryLen);
    for (let i = 0; i < binaryLen; i++) {
       let ascii = binaryString.charCodeAt(i);
       bytes[i] = ascii;
    }
    return bytes;
  }

  generateResponseSheetPreview(byte) {
    let blob = new Blob([byte], {type: "application/pdf"});
    let fileURL = URL.createObjectURL(blob);
    let a = document.createElement('a');
    a.href = fileURL;
    a.download = 'StudentResponseSheets.pdf';
    a.dispatchEvent(new MouseEvent('click'));
    // a.target = '_blank';
    // document.body.appendChild(a);
    // a.click();
    // document.body.removeChild(a);
  };

  generateResponseSheetLink(byte) {
    const pdfArrayBuffer = this.base64ToArrayBuffer(byte);
    let blob = new Blob([pdfArrayBuffer], {type: "application/pdf"});
    let fileURL = URL.createObjectURL(blob);
    const safeFileURL: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileURL);
    return safeFileURL;
  }

  async generateResponseSheetPdf(base64: string) {
    const pdfArrayBuffer = this.base64ToArrayBuffer(base64);
    this.generateResponseSheetPreview(pdfArrayBuffer);
  }

  async getClassResponseTemplate(respSheetConfig: IGenRespSheetConfig) { 
    this.isPrintingBulk = true;
    let res;
    res = await this.auth
    .apiFind(this.routes.EDUCATOR_GEN_RESPONSE_SHEETS,
      {
        query: respSheetConfig
      });
    if (res.length > 0) {
      const resObj = res[0];
      await this.generateResponseSheetPdf(resObj.responsePdf);
      this.isPrintingBulk = false;
    }
  }

  /** Fetch and store response sheets for one student, separately for each session, to be ready to download through student modal */
  async getStudentResponseTemplates(respSheetConfig: IGenRespSheetConfig) { 
    this.isRespSheetTemplateLoaded = false;
    await this.auth
      .apiFind(this.routes.EDUCATOR_GEN_RESPONSE_SHEETS,
        {
          query: respSheetConfig
        }).then(res => {
          if (res.length > 0) {
            this.currStudentResponseTemplates = new Map();
            res.forEach(r=> {
              const sessionSlug = r.session_slug;
              if (!this.currStudentResponseTemplates.has(+sessionSlug)){
                this.currStudentResponseTemplates.set(+sessionSlug, r.responsePdf);
              }
            })
            this.isRespSheetTemplateLoaded = true;
          }     
          else {
            throw new Error('Problem retrieving response sheets');
          }
        }).catch((e) => {
          throw new Error(e.message);
        })
  }

  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const result = reader.result as string;
            const withoutPrefix = result.substr(result.indexOf(',') + 1);
            resolve(withoutPrefix);
        };
        reader.onerror = error => reject(error);
    });
}

  getMinifiedScanInfo(): IBulkReqScanInfoContents {
    const studentScanInfo = Object.assign(this.studentScanInfoMap);
    const minifiedScanInfo = {};

    for (const stu in studentScanInfo) {
      const studentData = {};
      studentData['test_form_id'] = studentScanInfo[stu]['test_form_id'];
      studentData['file_path'] = studentScanInfo[stu]['file_path'];
      studentData['test_attempt_id'] = studentScanInfo[stu]['test_attempt_id'];
      const sessionInfo = Object.assign(studentScanInfo[stu]['scanningQues']);
      studentData['sessionInfo'] = sessionInfo;
      minifiedScanInfo[stu] = {...studentData};
    }

    return minifiedScanInfo;
  }

   /** Starting a bulk scan from a single file for many students in the class */
   async initSingleFileBulkScan(bulk_file_path: string, test_session_id: number, isSasn: number, schl_class_group_id:number, schl_class_id:number){
    const data = {
      isBulk: true,
      isSasn,
      bulk_file_path, 
      test_session_id,
      schl_class_group_id, 
      schl_class_id
    }
    return this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, data)
    .catch(err => {
      this.loginGuard.quickPopup(this.lang.tra('lbl_error'))
      throw new Error(err.message)
    })
  }

  async startBulkUpload(bulkFileConfig: any) {
    const {hasResponses, noValidScans, resp} = bulkFileConfig;
    if (noValidScans) {
      this.loginGuard.quickPopup(this.lang.tra('pj_upload_warn_no_scans_uploaded'));
      return;
    }
    if (hasResponses) {
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('warn_bulk_scan_overwrite'),
        confirm: async () => {
          await this.uploadSingleFileBulkScan(bulkFileConfig)
        }
      })
    } else {
      await this.uploadSingleFileBulkScan(bulkFileConfig)
    }
  }

  startBulkUploadTimer() {
    this.clearBulkUploadTimer();
    this.bulkUploadTimer = setInterval(() => {
      if (this.timePerUploadCheck > 0) {
        this.timePerUploadCheck--;
      } else {
        this.timePerUploadCheck = 10;
      }
    }, 1000)
  }

  clearBulkUploadTimer() {
    if (this.bulkUploadTimer){
      clearInterval(this.bulkUploadTimer);
    }
  }
  
  async reviewScansForOverrides (bulkFile: File, isSasn: number, testSessionId: any, schl_class_group_id, school_class_id): Promise<any> {
    this.isUploadingBulk = true;
    // return this.auth.uploadFile(bulkFile, `user_scans/uploads/${bulkFile.name}`)
    const res = await this.auth.uploadFile(bulkFile, `user_scans/uploads/${bulkFile.name}`)
    if (res.success && res.url) {
      try {
        this.startBulkUploadTimer();
        const {resQrData} = await this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_QR_CONTENTS, {
          bulkFileUrl: res.url, 
          isSasn, 
          testSessionId,
          schl_class_group_id: schl_class_group_id,
          school_class_id: school_class_id
        });
        if (!this.bulkUploadQrCodeInterval) {
          return await new Promise((resolve, reject) => {
            this.bulkUploadQrCodeInterval = setInterval(async () => {
              const resp = await this.auth.apiFind(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_QR_CONTENTS, {query: {
                test_session_id: testSessionId,
                schl_class_group_id: schl_class_group_id
              }})
              
              if (resp.length > 0) {
                const response = resp[0];
                let hasResponses = false;
                const studentUids = Object.keys(response);
                if (studentUids.length < 1) {
                  this.isUploadingBulk = false;
                  this.qrcodeProcessingComplete.next({noValidScans: true})
                }
                for (const student in response) {
                  const uid = student;
                  // if qr code scan is for student that is not in class
                  if (!(Object.keys(this.studentScanInfoMap).includes(uid))) {
                    continue;
                  }
                  const studentData = this.studentScanInfoMap[uid].scanningQues;
                  const scansToAdd = response[uid].question_slugs;
                  for (let questionInfo in studentData){
                    const question = studentData[questionInfo];
                    if(scansToAdd.includes(question["scanSlug"]) && question["responses"].length > 0){
                      hasResponses = true;
                    }
                  }
                }
                this.isUploadingBulk = false;
                clearInterval(this.bulkUploadQrCodeInterval);
                this.clearBulkUploadTimer();
                this.bulkUploadQrCodeInterval = null;
                this.qrcodeProcessingComplete.next({hasResponses, bulkFileUrl: res.url, resp:resQrData, isSasn, testSessionId, schl_class_group_id})
              }
            }, 10000)
          }).then((res) => {
            return res;
          }) 
        }
      } catch (e) {  
        console.log(e.message);
      }
    }
    return {hasResponses: false, bulkFileUrl: null}
  }
  async uploadSingleFileBulkScan(bulkScanConfig: any) {
    const {bulkFileUrl, testSessionId, isSasn, schl_class_group_id, resp} = bulkScanConfig;
    this.isUploadingBulk = true;
    const minifiedScanInfo = this.getMinifiedScanInfo();
    try {
      console.log("BULKFILEURL", bulkFileUrl);
      await this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, //public/educator/resp-sheet-upload/upload-response-sheets
        {
          bulkFileUrl, 
          studentScanInfo: JSON.stringify(minifiedScanInfo),
          testSessionId,
          isBulk: true,
          isSasn,
          resp,
          schl_class_group_id
        });
      if (!this.bulkUploadWaitInterval) {
        this.startBulkUploadTimer();
        this.bulkUploadWaitInterval = setInterval(async () => {
          const response = await this.auth.apiFind(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, {
            query: {
              test_session_id: testSessionId,
              schl_class_group_id
            }})

          if (response.length > 0) {
            this.isUploadingBulk = false;
            clearInterval(this.bulkUploadWaitInterval);
            this.clearBulkUploadTimer();
            this.bulkUploadWaitInterval = null;
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('pj_scan_bulk_upload'),
              btnCancelConfig: {
                hide: true
              },
              confirm: () => { 
                window.location.reload()
              }
            })
            // const res = response[0];
            // const {studentResponseMapping, sessionsNotSubmitted} = res;
            // const students = Object.keys(studentResponseMapping);
            // if (students.length === 0 && sessionsNotSubmitted) {
            //   this.loginGuard.quickPopup(this.lang.tra(('pj_err_session_not_submitted')));
            //   clearInterval(this.bulkUploadWaitInterval);
            //   this.clearBulkUploadTimer();
            //   this.isUploadingBulk = false;
            //   return;
            // } else if (students.length === 0 && !sessionsNotSubmitted) {
            //   this.loginGuard.quickPopup('Cannot override previous session scans!');
            //   clearInterval(this.bulkUploadWaitInterval);
            //   this.clearBulkUploadTimer();
            //   this.isUploadingBulk = false;
            //   return;
            // } else {
            //   for (const student in studentResponseMapping) {
            //     const studentRes = studentResponseMapping[student];
            //     // case if student found in scans is not in the class
            //     if (!studentRes) {
            //       continue;
            //     }
            //     const studentScanInfo = this.studentScanInfoMap[student].scanningQues;
            //     for (const session in studentRes) {
            //       const questionId = studentRes[session].questionId;
            //       studentScanInfo[session][questionId]['isConfirmed'] = false;
            //       const scanUrl = studentRes[session].uncroppedImgUrl;
            //       const croppedScanUrl = studentRes[session].croppedImgUrl;
            //       const responseObj = {
            //         responseType: EResponseType.PAPER,
            //         info: {
            //           url: scanUrl,
            //           url_cropped: croppedScanUrl
            //         }
            //       };
            //       if (studentScanInfo[session][questionId]['responses'].length > 0) {
            //         studentScanInfo[session][questionId]['responses'] = [];
            //       } 
            //       studentScanInfo[session][questionId]['responses'].push(responseObj);
            //     }
            //   }
            //   this.isUploadingBulk = false;
            //   clearInterval(this.bulkUploadWaitInterval);
            //   this.clearBulkUploadTimer();
            //   this.bulkUploadWaitInterval = null;
            //   this.loginGuard.confirmationReqActivate({
            //     caption: this.lang.tra('pj_scan_bulk_upload'),
            //     btnCancelConfig: {
            //       hide: true
            //     },
            //     confirm: () => { 
            //       if (sessionsNotSubmitted) {
            //         this.showNotSubmittedModal();
            //       }
            //     }
            //   })
            // }
          }
        }, 10000)
      }
    } 
    catch (e) {
      this.isUploadingBulk = false;
      throw new Error('Upload Failed')
    }
  }

  showNotSubmittedModal() {
    this.loginGuard.confirmationReqActivate({
      caption:('pj_err_session_not_submitted'),
      isModalChain: true,
      modalChainLimit: 1
    });
  }


  questionSlugToShortSlug(questionSlug: string){
    const scanSlugSplit = questionSlug.split(QR_SLUG_PREFIX)
    if (scanSlugSplit.length == 2){
      const shortSlug = scanSlugSplit[1]
      return shortSlug
    } else {
      return questionSlug
    }
  }

  shortSlugToQuestionSlug(shortSlug: string){
    return QR_SLUG_PREFIX + shortSlug
  }

    /** Start uploading a single scan for one student, check QR code first */
    async initUploadSingleScan(test_attempt_id:number, test_question_id:number, uid: number, file: File, tassId: number, schl_class_group_id: number, questionsForScan: IQuestionScan[]) {
      this.isUploadingSingle = true;
      const sessionSlug = this.shortSlugToQuestionSlug(this.studentScanInfoMap[uid].scanningQues[test_question_id]?.scanSlug);
      return this.auth.uploadFile(file, `user_scans/uploads/${file.name}`).then(res => {
        if(res.success && res.url) {
          const scanFileUrl = res.url;
          // Pass the file through the QR code scanner
          const query = { isSingleScanCheck: 1}
          const data = { 
            uid,
            sessionSlug,
            scanFileUrl
          }
          this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_QR_CONTENTS, data, {query})
          .then((res) => {
            // If no errors (scan matches intended student and session) - proceed to upload
            const data = {
              scanFileUrl,
              test_attempt_id,
              test_question_id,
              uid,
              tassId,
              schl_class_group_id
            }
            if (res.isMatch){
              this.finishUploadSingleScan(data)
            } else if (res.isNoQr){
              this.loginGuard.confirmationReqActivate({
                caption: this.lang.tra("lbl_confirm_scan_student_no_qr"),
                btnProceedConfig: {caption: this.lang.tra('lbl_yes')},
                btnCancelConfig: {caption: this.lang.tra('lbl_no')},
                confirm: () => {
                  this.finishUploadSingleScan({...data, isForced: true});
                }
              })
            }
          })
          .catch((err) => {
            this.isUploadingSingle = false;
            switch (err.message){
              case "ERR_TOO_MANY_SCANS":
                return this.loginGuard.quickPopup(this.lang.tra('err_too_many_pages_msg'));
              // case "NO_QR_DETECTED":
              //   return this.loginGuard.quickPopup(this.lang.tra('err_scan_no_qr_detected'));
              case "STUDENT_MISMATCH":
                return this.loginGuard.quickPopup(this.lang.tra('err_scan_student_mismatch_abed'));
              // If matches student but not session, prompt whether to upload to the correct session instead.
              case "STUDENT_MATCH_SESSION_MISMATCH":
                const qrSessionSlugFull = err.data.qrSessionSlug
                const qrSessionSlug = this.questionSlugToShortSlug(qrSessionSlugFull)
                const scanningQuestions = this.studentScanInfoMap[uid].scanningQues
                let qrQuestionId;
                let qrQuestionLabel;
                for (const qId in scanningQuestions){
                  if (scanningQuestions[qId].scanSlug == qrSessionSlug){
                    qrQuestionId = +qId;
                    qrQuestionLabel = questionsForScan.find((q: IQuestionScan) => q.item_id == +qrQuestionId)?.question_label;
                    break;
                  }
                }
                if (!qrQuestionId){
                  return;
                }
                const ogQuestionLabel = questionsForScan.find((q: IQuestionScan) => q.item_id == +test_question_id)?.question_label;
                return this.loginGuard.confirmationReqActivate({
                  caption: this.lang.tra("err_scan_student_match_session_mismatch", undefined, {qr_session: qrQuestionLabel, og_session: ogQuestionLabel}),
                  btnProceedConfig: {caption: this.lang.tra('lbl_yes')},
                  btnCancelConfig: {caption: this.lang.tra('lbl_no')},
                  confirm: () => {
                    const data = {
                      scanFileUrl,
                      test_attempt_id,
                      test_question_id: qrQuestionId,
                      uid,
                      tassId: scanningQuestions[qrQuestionId].tassId,
                      schl_class_group_id
                    }
                    this.finishUploadSingleScan(data)
                    .then(() => {
                      // Confirm when the upload is done
                      const caption = this.lang.tra('lbl_succ_upload_scan_corrected_session', undefined, {qr_session: qrQuestionLabel})
                      this.loginGuard.quickPopup(caption)
                    });
                  }
                })
              default:
                return this.loginGuard.quickPopup('err_check_single_scan'); 
            }
          })
        } else {
          throw new Error('Upload Failed')
        } 
      })
    }

    /** Complete uploading a single scan for one student */
    async finishUploadSingleScan (scanData: {scanFileUrl:string, test_attempt_id:number, test_question_id:number, uid:number, tassId: number, schl_class_group_id: number, isForced?: boolean}){
      this.isUploadingSingle = true;
      const {scanFileUrl, test_attempt_id, test_question_id, uid, tassId, schl_class_group_id, isForced} = scanData
      return this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, 
        {
          scanFileUrl,
          test_attempt_id, 
          test_question_id,
          sessionSlug: test_question_id,
          tassId,
          schl_class_group_id,
          isForced
        }).then( (resList: ISingleScanRes) => {
          if(resList) {
            const crQuestion = this.studentScanInfoMap[uid].scanningQues[test_question_id];
            crQuestion.isConfirmed = false;
            crQuestion.isForcedUpload = isForced;
            if (crQuestion.responses.length > 0) {
              crQuestion.responses = [];
            }
            crQuestion.responses.push({
              responseType: EResponseType.PAPER,
              info: {
                url: resList['uncroppedFile'],
                url_cropped: resList['croppedFile']
              }
            })
          }
        }).catch((e) => {
          if (e.message === 'ERR_TOO_MANY_SCANS') {
            this.loginGuard.quickPopup(this.lang.tra('err_too_many_pages_msg'));
          } else if (e.message === 'ERR_SESS_NOT_SUBMITTED') {
            this.loginGuard.quickPopup('pj_err_session_not_submitted');
          }
        })
        .finally(() => {this.isUploadingSingle = false})
    }

  getMissingUnconfirmedScans(studentScanInfo: any, student: any, onMissingScans: (sessionKey: string) => void, onUnconfirmedScans: (sessionKey: string) => void, onExpectedScans?: (sessionKey: string) => void) {
    const {uid, isPaperFormatDefault, studentSubSessionsState} = student;
    for (const quesId in studentScanInfo[uid]?.scanningQues) {
      const scanInfo = studentScanInfo[uid].scanningQues[quesId];
      const isConfirmed = scanInfo.isConfirmed;
      const isOnPaper = studentScanInfo[uid].scanningQues[quesId]['isPaperFormat'];
      const hasTaqr = studentScanInfo[uid].scanningQues[quesId]['hasTaqr'];
      const subsession = studentSubSessionsState.length === 1 ? studentSubSessionsState[0] : studentSubSessionsState.find(ss => ss.subsession_slug.includes('session_a')); // temporary fix, need to refactor to get session based on slug
      let isSubmitted;
      if(subsession) {
        isSubmitted = subsession.is_submitted;
      }
      if (onExpectedScans){
        onExpectedScans(quesId);
      }
      if (scanInfo.responses.length < 1 && hasTaqr) {
        onMissingScans(quesId);  
      }
      if (!scanInfo.isConfirmed && scanInfo.responses.length > 0 && isOnPaper && !isConfirmed) {
        onUnconfirmedScans(quesId);
      }
    }
  }

  getScanInfo(uid: number) {
    return this.studentScanInfoMap[uid];
  }

  getScanSlugs(uid: number) {
    return Object.keys(this.getScanInfo(uid).scanningQues).sort();
  }

  getCrQuestions(uid: number) {
    return this.getScanInfo(uid)?.scanningQues || {};
  }

  getTestAttemptId(uid: number) {
    return this.getScanInfo(uid).test_attempt_id;
  }

  getSessionSlugs() {
    return this.sessionSlugsSet.size > 0 ? [...this.sessionSlugsSet].sort() : [];
  }
  
  isLoaded() {
    return this.isScanInfoLoaded;
  }

  getQuesIdBySection(uid: number, sessionSlug: string) {
    return this.studentScanInfoMap[uid]['scanningQuesIds'][sessionSlug];
  }

}
