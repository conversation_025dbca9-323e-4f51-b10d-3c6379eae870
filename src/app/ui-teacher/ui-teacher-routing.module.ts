
import { RouterModule, Routes } from '@angular/router';
import { NgModule } from '@angular/core';
import { TeacherClassroomsComponent } from './teacher-classrooms/teacher-classrooms.component';
import { ViewAssessmentNewComponent } from './view-assessment-new/view-assessment-new.component';
// import { ViewAssessmentReportComponent } from './view-assessment-report/view-assessment-report.component';
import { ViewTeacherStudentReports } from './view-teacher-student-reports/view-teacher-student-reports.component';
import { ViewInvigilateComponent } from './view-invigilate/view-invigilate.component';
import { ViewManageStudentsComponent } from './view-manage-students/view-manage-students.component';
import { ViewComponent } from './view/view.component';
import { ViewAssessmentSchedulerComponent } from './view-assessment-scheduler/view-assessment-scheduler.component';
import { ViewTeacherStudentReportsG9Component } from './view-teacher-student-reports-g9/view-teacher-student-reports-g9.component';
import { ViewTeacherAdminQuestionniareComponent } from '../ui-questionnaire/view-teacher-admin-questionniare/view-teacher-admin-questionniare.component';
import { ViewTeacherSessionReportComponent } from './view-teacher-session-report/view-teacher-session-report.component';
import { ViewUploadBulkScanComponent } from './view-upload-bulk-scan/view-upload-bulk-scan.component';

const routes: Routes = [
  { path: '', redirectTo: 'classrooms', pathMatch: 'full' },
  { path: 'classrooms', component: TeacherClassroomsComponent },
  { path: 'classrooms/:classroomId', component: TeacherClassroomsComponent },
  { path: 'assessment/:classroomId/:asmtSessionId', component: ViewInvigilateComponent },
  { path: 'assessment/:classroomId/:asmtSessionId/bulk-scan', component: ViewUploadBulkScanComponent },
  { path: 'assessment/score-entry/:classroomId/:asmtSessionId', component: ViewInvigilateComponent },
  { path: 'session-report/:classroomId/:asmtSessionId', component: ViewTeacherSessionReportComponent },
  { path: 'session-report/:classroomId/:asmtSessionId/:formId', component: ViewTeacherSessionReportComponent },
  { path: 'session-report/:classroomId/:asmtSessionId/:formId/:questionId', component: ViewTeacherSessionReportComponent },
  { path: 'assessment-create/:classroomId', component: ViewAssessmentNewComponent },
  { path: 'assessment-scheduler/:classroomId', component:ViewAssessmentSchedulerComponent},
  { path: 'results-report/:classroomId/:schlClassGroupId', component: ViewTeacherStudentReports },
  { path: 'assessment-report/:classroomId/:schlClassGroupId', component: ViewTeacherStudentReportsG9Component },
  // { path: 'assessment-report/:classroomId/student/:student', component: ViewAssessmentReportComponent },
  { path: 'students/:classroomId', component: ViewManageStudentsComponent },
  { path: 'classrooms/students/:classroomId', component: ViewManageStudentsComponent },
  { path: 'view', component: ViewComponent },
  { path: 'questionnaire/:testSessionId', component: ViewTeacherAdminQuestionniareComponent},
  { path: 'assessment/view-responses/:classroomId/:asmtSessionId', component: ViewInvigilateComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UITeacherRoutingModule {}
