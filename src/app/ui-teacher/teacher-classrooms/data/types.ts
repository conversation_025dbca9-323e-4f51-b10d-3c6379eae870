import { IAgreementConfirmConfig, IAgreementConfirmData } from "../../../ui-schooladmin/view-schooladmin-dashboard/data/types";

export const ProfResponsibilityTeacherAgreement: IAgreementConfirmConfig = {
    storageKey: "profresponsibility-teacher-confirm",
    captionSlug: "txt_caption_public_prof_teacher_agreement",
    checkboxSlug: "txt_checkbox_prof_resp",
    btnSlug: "btn_accept_attestation"
}

export interface ScanSessionInfo {
  [test_session_id: string]: {
    test_design_id?: number;
    is_scan_session?: boolean;
    n_students?: number;
    n_scans_expected?: number;
    n_scans_received?: number;
  }
};

export interface TwSlot {
  tw_id: number, 
  academic_year?: string, 
  sortOrder:string, 
  name?: string, 
  classes: any[]
  is_teacher_creation?: boolean, 
}
