export enum SCORE_ENTRY_ACTION_TYPES {
    SCORE_CHANGE = "SCORE_CHANGE",
    LOCK_CHANGE = "LOCK_CHANGE",
    SCORE_CLEAR = "SCORE_CLEAR",
    SUBMIT = "SUBMIT",
    UNSUBMIT = "UNSUBMIT"
}

export enum IDiscontActions {
    LOCK = "LOCK",
    UNLOCK = "UNLOCK",
    CONFIRM = "CONFIRM"
}

export type ComparisonOperator = ">" | ">=" | "<" | "<=" | "==" | "===" | "!=" | "!==" | "isNull";

export interface IDiscontConditions {
    sourceCard?: string,
    property?: string,
    properties?: string[],
    operator: ComparisonOperator,
    value?: number | string
}
export interface IDiscontRules {
    action: IDiscontActions,
    targetProperty?: string,
    conditions: IDiscontConditions[]
    message?: {en: string, fr: string} // for CONFIRM
}
export interface IDiscontinuationCard {
    rules: IDiscontRules[]
}

export type IDiscontRuleMap = {[key: string]: IDiscontinuationCard}

export const comparisonOperators = {
    '>': (a, b) => a != null && a > b,
    '>=': (a, b) => a != null &&  a >= b,
    '<': (a, b) => a != null &&  a < b,
    '<=': (a, b) => a != null &&  a <= b,
    '==': (a, b) => a != null &&  a == b,
    '===': (a, b) => a != null &&  a === b,
    '!=': (a, b) => a != null &&  a != b,
    '!==': (a, b) => a != null &&  a !== b,
    'isNull': (a, b) => a == undefined || a == null
};

export const evaluateComparison = (a: number, operator: ComparisonOperator, b: string | number | undefined): boolean => {
    return comparisonOperators[operator](a, b);
  }


export const evalCondition = (cond, lockMap) => {
    const raw = lockMap[cond.sourceCard][cond.property].score;
    return comparisonOperators[cond.operator](raw, cond.value)
}