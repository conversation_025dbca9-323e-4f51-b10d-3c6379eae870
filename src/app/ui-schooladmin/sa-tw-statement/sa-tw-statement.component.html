<div style="display:table">
    <div style = "display: table-cell;">
        <filter-toggles 
            [state]="mySchool.getClassFilterToggles({isExcludeClassroomAssessment: true})"
            (id)="setClassFilter($event)"
        ></filter-toggles>
    </div> 
    <div style = "display: table-cell;">
        <sa-test-window-filter
          [currentClassFilter] = "currentClassFilter"
          (setTestWindowEvent) = "setTestWindowFilter($event)"
          [restrictToSaSignoff]="true"
        ></sa-test-window-filter>
    </div>  

</div>

<div *ngIf="twStatement()">

    <ng-container *ngIf="!decideDataToShow()">
        <div class="notification is-warning">
            <tra [slug]="'abed_sa_pk_no_students'"></tra>
        </div>
    </ng-container>

    <ng-container *ngIf="decideDataToShow()">

        <div class="space-between">
            <div style="display:flex; flex-direction: row; gap:1em;">
                <div class="select">
                    <select [(ngModel)]="selectedAsmtTypeSlug" (change)="onSelectedAsmtTypeSlugChange()">
                        <option value="__OVERVIEW__">
                            <tra [slug]="'abed_sa_pk_overview'"></tra>
                        </option>
                        <option *ngFor="let component of twStatement().asmtComponents" [value]="component.asmt_type_slug">
                            {{component.caption_short}}
                        </option>
                    </select>
                </div>
                <mat-slide-toggle 
                    [(ngModel)]="isShowingOnlyFlaggedStudents" 
                >
                    <span [ngSwitch]="!!isShowingOnlyFlaggedStudents">
                        <span *ngSwitchCase="true">
                            <tra [slug]="'abed_sa_pk_flagged_students_on'"></tra>
                        </span>
                        <span *ngSwitchCase="false">
                            <tra [slug]="'abed_sa_pk_flagged_students_off'"></tra>
                        </span>
                    </span>
                </mat-slide-toggle>
            </div>
            <div>
                <button  
                    class="button is-small" 
                    (click)="printReportPdfs()"
                    [disabled]="isExportingPrelim"
                    [class.is-dark]="isExportingPrelim"
                >
                    <tra [slug]="'abed_sa_pk_export_result'"></tra>
                    <span *ngIf="isExportingPrelim">&nbsp;(Loading...)</span>
                </button>
            </div>  
        </div>

        <table style="width: auto; margin-top: 1em;">
            <tr>
                <th>
                    
                </th>
                <th>
                    <tra [slug]="'abed_sa_pk_exam_comp'"></tra>
                </th>
                <!-- <th>Administration Date</th> -->
                <th *ngIf="!isRegistrationBasedWindow()">
                    <tra [slug]="'abed_sa_pk_enrollments'"></tra>
                </th>
                <ng-container *ngFor="let stat of twStatement().statRows" >
                    <th *ngIf="showStat(stat)" class="th-multiline">
                        <tra [slug]="stat.caption"></tra>
                    </th>
                </ng-container>
                <th>
                    <tra [slug]="'abed_sa_pk_submitted'"></tra>
                </th>
                <th>
                    <tra [slug]="'abed_edit'"></tra>
                </th>
            </tr>
            <ng-container *ngFor="let component of twStatement().asmtComponents">
                <tr  
                    class="th-multiline" 
                    [class.is-selected]="isCurrentAsmtSlug(component.asmt_type_slug)"
                    *ngIf="showAssessmentComponent(component)"
                >
                    <td>
                        <span *ngIf="assessmentComponentHasFlaggedStudents(component)" class="tag is-danger">
                            {{numFlaggedStudentsFor(component.asmt_type_slug)}} students
                        </span>
                    </td>
                    <td>
                        <a (click)="selectAsmtTypeSlug(component.asmt_type_slug)">
                            {{component.caption_short}}
                        </a>
                    </td>
                    <td *ngIf="!isRegistrationBasedWindow()">
                        {{twStatement().students.length}}
                    </td>
                    <!-- <td>
                        {{component.date_start}}
                    </td> -->
                    <ng-container *ngFor="let stat of twStatement().statRows" >
                        <td *ngIf="showStat(stat)">
                            <ng-container *ngIf="stat.prop == 'n_exc' && excusedImplemented(twStatement().schl_tw_sign_off.meta, component.asmt_type_slug)">
                                {{ twStatement().assessmentComponentStats[component.asmt_type_slug][stat.prop] }}
                            </ng-container>
                            <ng-container *ngIf="stat.prop == 'n_exc' && !excusedImplemented(twStatement().schl_tw_sign_off.meta, component.asmt_type_slug)">
                                N/A
                            </ng-container>
                            <ng-container *ngIf="!(stat.prop == 'n_exc')">
                                {{ twStatement().assessmentComponentStats[component.asmt_type_slug][stat.prop] }}
                            </ng-container>
                        </td>
                    </ng-container>
                    <td>
                        <ng-container [ngSwitch]="!!isAsmtSubmitted(component.asmt_type_slug)">
                            <span *ngSwitchCase="true" class="tag is-success">
                                <tra [slug]="'abed_sa_pk_stat_submit'"></tra>
                            </span>
                            <ng-container *ngSwitchCase="false">
                                <ng-container [ngSwitch]="!!hasRegistrationOrAttempts(component.asmt_type_slug)">
                                    <span *ngSwitchCase="true" class="tag is-danger">
                                        <tra [slug]="'abed_sa_pk_stat_not_submit'"></tra>
                                    </span>
                                    <span *ngSwitchCase="false" class="tag is-warning">
                                        <tra [slug]="'abed_sa_pk_stat_no_students'"></tra>
                                    </span>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                    </td>
                    <td>
                        <button class="button is-small" (click)="selectAsmtTypeSlug(component.asmt_type_slug)">
                            <tra [slug]="'abed_edit'"></tra>
                        </button>
                    </td>
                </tr>
            </ng-container>
        </table>
        
        
        <ng-container *ngIf="!decideOverviewDisplay()">
          <div *ngIf="currentAsmtMap?.test_design_id && !isPerusalAvailable" class="notification is-warning is-light" style="margin-top:1em;">
            Perusal is not available.
            <span *ngIf="renderPerusalDate">
              The availability period is {{renderPerusalDate}}.
            </span>
            <span *ngIf="renderPerusalDate">
              Perusal is not configured for this assessment, contact the ministry for more details.
            </span>
          </div>
          <div class="notification" style="margin-top: 1em" *ngIf="currentAsmtMap?.test_design_id && isPerusalAvailable">
              <a routerLink="/{{lang.c()}}/test-auth/shared-test-version/{{currentAsmtMap.source_item_set_id}}/{{currentAsmtMap.test_design_id}}/ts/{{currentAsmtMap.sample_test_session_id || 1}}" [queryParams]="{isPeruseAdmin:1}" target="_blank">
                  <tra slug="abed_sr_peruse_link"></tra>
              </a>
              <span>
                {{currentAsmtMap.caption_short}} <tra slug="lbl_accessible"></tra>
              </span>
              <span *ngIf="renderPerusalDate">
                {{renderPerusalDate}}
              </span>
              .
            </div>
            <div class="space-between" style="margin-top: 1em;">
                <h3>
                    <tra [slug]="'abed_sa_pk_list'"></tra>
                </h3>
                <ng-container *ngIf="isRegistrationBasedWindow()">
                    <mat-slide-toggle 
                        [(ngModel)]="isShowingUnregisteredStudents" 
                        >
                        <span [ngSwitch]="!!isShowingUnregisteredStudents">
                            <span *ngSwitchCase="true">
                                <tra [slug]="'abed_sa_pk_show_no_reg'"></tra>
                            </span>
                            <span *ngSwitchCase="false">
                                <tra [slug]="'abed_sa_pk_exclude_no_reg'"></tra>
                            </span>
                        </span>
                    </mat-slide-toggle>
                </ng-container>
            </div>

            <div>
              <table style="width: auto">
                <tr>
                  <th></th>
                  <th><tra [slug]="'student_account_num_abed'"></tra></th>
                  <th><tra [slug]="'abed_sa_pk_student_name_head'"></tra></th>
                  <th><tra [slug]="'abed_sa_pk_bday_head'"></tra></th>
                  <ng-container *ngFor="let component of twStatement().asmtComponents">
                      <th *ngIf="isCurrentAsmtSlug(component.asmt_type_slug)" class="th-multiline">
                        <!-- {{component.caption_short}} -->
                        <tra [slug]="'abed_sa_pk_submission_stat_head'"></tra>
                      </th>
                  </ng-container>
                  <th><tra [slug]="'abed_sa_pk_absence_head'"></tra></th>
                  <th *ngIf="!isRegistrationBasedWindow()"><tra [slug]="'abed_sa_pk_other_class_head'"></tra></th>
                  <!-- <th>Administration Anomaly?</th> -->
                  <th *ngIf="!isRegistrationBasedWindow()"><tra [slug]="'abed_sa_pk_excused_head'"></tra></th>
                  <th *ngIf="!isRegistrationBasedWindow()"><tra [slug]="'abed_sa_pk_transferred_head'"></tra></th>
                  <th><tra [slug]="'abed_notes'"></tra></th>
                  <th><tra [slug]="'abed_sa_pk_notes_content_head'"></tra></th>
                  <th><tra [slug]="'lbl_reported_issues'"></tra></th>
                </tr>
                <ng-container *ngFor="let student of twStatement().students">
                  <ng-container *ngIf="showStudent(student, selectedAsmtTypeSlug)">
                    <tr >
                        <td>
                            <span *ngIf="isStudentFlagged(student)" class="tag is-danger">
                                <tra [slug]="'abed_sa_pk_clarify'"></tra>
                            </span>
                        </td>
                        <td>{{student.student_gov_id}}</td>
                        <td>{{student.last_name}}, {{student.first_name}}</td>
                        <td>{{student.dob}}</td>
                        <ng-container *ngFor="let component of twStatement().asmtComponents">
                            <td *ngIf="isCurrentAsmtSlug(component.asmt_type_slug)">
                                <ng-container [ngSwitch]="student.asmt_status[component.asmt_type_slug].status_code">
                                    <ng-container *ngSwitchCase="StuAsmtStatus.REGISTERED">
                                        <span class="tag"><tra [slug]="'abed_registered'"></tra></span>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="StuAsmtStatus.SUBMITTED">
                                        <span class="tag is-success"><tra [slug]="'abed_received'"></tra></span>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="StuAsmtStatus.ABSENT">
                                        <ng-container [ngSwitch]="isStudentAsmtSubm(student, component.asmt_type_slug)">
                                            <ng-container *ngSwitchCase="true">
                                                <span class="tag is-primary">
                                                    Received (but Absent)
                                                </span>
                                            </ng-container>
                                            <ng-container  *ngSwitchCase="false">
                                                <span class="tag is-warning"><tra [slug]="'abed_absent'"></tra></span>
                                            </ng-container>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </td>
                        </ng-container>
                        <td>
                            <mat-slide-toggle (change)="silentSave()" [(ngModel)]="getStudentMeta(student)['is_absent']" >
                                <span [ngSwitch]="!!getStudentMeta(student)['is_absent']">
                                    <span *ngSwitchCase="true"><tra [slug]="'abed_absent'"></tra></span>
                                    <span *ngSwitchCase="false"><tra [slug]="'abed_no'"></tra></span>
                                </span>
                            </mat-slide-toggle>
                        </td>
                        <td *ngIf="!isRegistrationBasedWindow()">
                            <mat-slide-toggle (change)="silentSave()" [(ngModel)]="getStudentMeta(student)['is_other_class']" >
                                <span [ngSwitch]="!!getStudentMeta(student)['is_other_class']">
                                    <span *ngSwitchCase="true"><tra [slug]="'abed_sa_pk_other_class'"></tra></span>
                                    <span *ngSwitchCase="false"><tra [slug]="'abed_no'"></tra></span>
                                </span>
                            </mat-slide-toggle>
                        </td>
                        <td *ngIf="!isRegistrationBasedWindow()">
                            <mat-slide-toggle (change)="silentSave()" [(ngModel)]="getStudentMeta(student)['is_excused']" [disabled]="!enabledExcused()">
                                <span [ngSwitch]="!!getStudentMeta(student)['is_excused']">
                                    <span *ngSwitchCase="true"><tra [slug]="'abed_sa_pk_excused'"></tra></span>
                                    <span *ngSwitchCase="false"><tra [slug]="'abed_no'"></tra></span>
                                </span>
                            </mat-slide-toggle>
                        </td>
                        <td *ngIf="!isRegistrationBasedWindow()">
                            <mat-slide-toggle (change)="silentSave()" [(ngModel)]="getStudentMeta(student)['is_transferred']">
                                <span [ngSwitch]="!!getStudentMeta(student)['is_transferred']">
                                    <span *ngSwitchCase="true"><tra [slug]="'abed_sa_pk_transferred'"></tra></span>
                                    <span *ngSwitchCase="false"><tra [slug]="'abed_no'"></tra></span>
                                </span>
                            </mat-slide-toggle>
                        </td>
                        <td>
                            <mat-slide-toggle (change)="silentSave()" [(ngModel)]="getStudentMeta(student)['is_anomaly']" >
                                <span [ngSwitch]="!!getStudentMeta(student)['is_anomaly']">
                                    <span *ngSwitchCase="true"><tra [slug]="'abed_anomaly'"></tra></span>
                                    <span *ngSwitchCase="false"><tra [slug]="'abed_no'"></tra></span>
                                </span>
                            </mat-slide-toggle>
                        </td>
                        <td>
                            <ng-container *ngIf="getStudentMeta(student)['is_anomaly']">
                                <textarea [(ngModel)]="getStudentMeta(student)['notes']" (change)="silentSave()" class="textarea is-small"></textarea>
                            </ng-container>
                        </td>
                        <ng-container *ngFor="let component of twStatement().asmtComponents">
                            <td *ngIf="isCurrentAsmtSlug(component.asmt_type_slug)">
                                <ng-container *ngIf="student.asmt_status[component.asmt_type_slug].report_num >0">
                                    <button *ngIf="getStudentIssue(student, component.asmt_type_slug).length == 0" class="button is-small" (click)="showStudentIssues(student, component.asmt_type_slug)">
                                        Expand ({{student.asmt_status[component.asmt_type_slug].report_num}})
                                    </button>
                                    
                                    <ng-container *ngFor="let issue of getStudentIssue(student, component.asmt_type_slug)">
                                        <p style="max-width: 500px">Issue: {{issue.id}} - {{issue.description}}</p>
                                        <br>
                                    </ng-container>
                                    <ng-container *ngIf="getStudentIssue(student, component.asmt_type_slug).length > 0">
                                        <button class="button is-small" (click)="clearIssues(student, component.asmt_type_slug)">
                                            <tra [slug]="'btn_close'"></tra>
                                        </button> 
                                    </ng-container>
                                </ng-container>
                            </td>
                        </ng-container>
                    </tr>
                  </ng-container>
                </ng-container>
              </table>
            </div>
            
            <h3><tra [slug]="'abed_sa_pk_declaration_header'"></tra></h3>
            
            <p><tra [slug]="'abed_sa_pk_declaration_body'"></tra></p>
            
            <div>
                <div>
                    <label>
                        <span class="input-margin"> <input type="checkbox" (change)="silentSave()" [(ngModel)]="cOAsmtProps().overall_issues_no"> </span>
                        <span><tra [slug]="'abed_sa_pk_overall_no'"></tra></span>
                    </label>
                </div>
                
                <div>
                    <label>
                        <span class="input-margin"> <input type="checkbox" (change)="silentSave()" [(ngModel)]="cOAsmtProps().overall_issues_yes"> </span>
                        <span><tra [slug]="'abed_sa_pk_overall_yes'"></tra></span>
                    </label>
                    <div style="margin-top:1em;">
                        <div> <em><tra [slug]="'abed_sa_pk_other_comments'"></tra></em> </div>
                        <textarea style="width:100%;" rows="8" (change)="silentSave()"  [(ngModel)]="cOAsmtProps().overall_issues_notes"></textarea>
                    </div>
                </div>
            
            </div>
            
            <div>
                <button 
                    class="button" 
                    (click)="saveSchlTwSignoff()" 
                    [disabled]="twStatement().isSaving"
                >
                    <tra [slug]="'abed_sa_pk_save_later'"></tra>
                </button>
                <button 
                    class="button is-success" 
                    (click)="saveSchlTwSignoff(true)" 
                    [disabled]="twStatement().isSaving"
                >
                    <tra [slug]="'abed_sa_submit_kit'"></tra>
                </button>
            </div>

        </ng-container>
        
    </ng-container>

</div>