<header [breadcrumbPath]="breadcrumb" ></header>

<div style="padding: 2em;">

    <h3>
        User Roles search
    </h3>
    <div style="margin-bottom:2em;">
        You can search by multiple properties at a time. If you want to search multiple emails at a time, you can just put them one line after another.
    </div>
    <div>
        <table style="width:auto;">
            <tr> <td>UID</td> <td><textarea [(ngModel)]="lookupForm.uid"></textarea></td> </tr>
            <tr> <td>Group ID</td> <td><textarea [(ngModel)]="lookupForm.group_id"></textarea></td> </tr>
            <tr> <td>First Name</td> <td><textarea [(ngModel)]="lookupForm.first_name"></textarea></td> </tr>
            <tr> <td>Last Name</td> <td><textarea [(ngModel)]="lookupForm.last_name"></textarea></td> </tr>
            <tr> <td>Email (contact)</td> <td><textarea [(ngModel)]="lookupForm.contact_email"></textarea></td> </tr>
            <tr> <td>Email (auth)</td> <td><textarea [(ngModel)]="lookupForm.auth_email"></textarea></td> </tr>
            <tr> <td>district_id</td> <td><textarea [(ngModel)]="lookupForm.district_id"></textarea></td> </tr>
            <tr> <td>district_code</td> <td><textarea [(ngModel)]="lookupForm.district_code"></textarea></td> </tr>
            <tr> <td>school_id</td> <td><textarea [(ngModel)]="lookupForm.school_id"></textarea></td> </tr>
            <tr> <td>school_code</td> <td><textarea [(ngModel)]="lookupForm.school_code"></textarea></td> </tr>
            <tr> <td>Role Type</td> <td><select multiple [(ngModel)]="lookupForm.role_type">
                <option value="">(any)</option>    
                <option value="bc_fsa_admin">FSA Admin (?)</option>    
                <option value="bc_grad_admin">Grad Admin (?)</option>    
                <option value="ministry_admin">Ministry Admin</option>    
                <option value="schl_dist_admin">District Admin</option>    
                <option value="schl_admin">School Admin</option>    
                <option value="schl_student">Student</option>    
                <option value="debug">Support</option>    
            </select></td> </tr>
            <tr> <td>Account Type (dont rely on)</td> <td><select multiple [(ngModel)]="lookupForm.account_type">
                <option value="">(any)</option>    
                <option value="student">Student</option>    
                <option value="school admin">School Admin (alt)</option>    
                <option value="school-admin">School Admin</option>    
                <option value="dist-admin">Dist Admin</option>    
                <option value="ministry-admin">Ministry Admin</option>    
                <option value="test-ctrl-adm-bcfsa">fsa</option>    
                <option value="test-ctrl-adm-bcfsa-district">fsa-district</option>    
                <option value="test-ctrl-adm-bcfsa-district-score-entry">fsa-district-score-entry</option>    
                <option value="test-ctrl-adm-bcfsa-school">fsa-school</option>    
                <option value="test-ctrl-adm-bcfsa-school-score-entry">fsa-school-score-entry</option>    
                <option value="test-ctrl-adm-bcgrad-district">grad-district</option>    
                <option value="test-ctrl-adm-bcgrad-ministry">grad-ministry</option>    
                <option value="test-ctrl-adm-bcgrad-school">grad-school</option>    
                <option value="test-auth">Auth</option>    
                <option value="support">Support</option>    
            </select></td> </tr>
            <tr> <td>Group Type</td> <td><select multiple [(ngModel)]="lookupForm.group_type">
                <option value="">(any)</option>    
                <option value="school_district">School</option>    
                <option value="single_questionbank">School</option>    
                <option value="questionbank">Item Bank</option>    
                <option value="single_questionbank">Item</option>    
                <option value="ministry">Ministry (?)</option>    
                <option value="test_controller">TC (?)</option>    
                <option value="project">Project</option>    
                <option value="mpt_sys">System</option>    
            </select></td> </tr>
        </table>
        
        
    </div>
    <div style="margin-top:1em;">
        <button class="button" (click)="runSearch()">Search</button>
    </div>

    <hr/>

    <div class="buttons">
        <ng-container *ngFor="let filter of columnFilters">
            <button 
                class="button is-small" 
                [class.is-info]="filter.isActive"
                (click)="filter.isActive = !filter.isActive"
            >{{filter.prop}}</button>
        </ng-container>
        <button 
            class="button is-small" 
            [class.is-info]="hideRevoked"
            (click)="hideRevoked = !hideRevoked"
        >Hide Revoked?</button>
    </div>

    <hr/>

    <div style="max-width:100%; overflow-x:auto;  max-height: 20em;">

        <table>
            <tr>
                <ng-container *ngFor="let column of columns">
                    <th *ngIf="!isColumnHidden(column)">
                        {{column.slug}}
                    </th>
                </ng-container>
                <th>Revoke/Unrevoke</th>
            </tr>
            <tr *ngFor="let row of responseData">
                <ng-container *ngFor="let column of columns">
                    <td *ngIf="!isColumnHidden(column) && !(hideRevoked && (row.is_revoked==1 || !row.group_id))">
                        <span *ngIf="column.slug !== 'revoked_by'">{{row[column.slug]}}</span>
                        <span *ngIf="column.slug === 'revoked_by'">{{formatRevokedBy(row)}}</span>
                    </td>
                </ng-container>
                <td *ngIf="row.ur_id && !(hideRevoked && (row.is_revoked==1 || !row.group_id))">
                  <button class="button is-small is-light" [class.is-danger]="!row.is_revoked" [class.is-success]="row.is_revoked" (click)="toggleUserRoleRevoked(row)">
                    {{row.is_revoked ? 'Unrevoke' : 'Revoke'}}
                  </button>
                </td>
            </tr>
        </table>

    </div>

    <div>
        <hr/>
        <h3>Login as</h3>
        UID: <input type="number" [(ngModel)]="loginAsUid" (change)="loginAsUrl=''">
        <button (click)="loginAs(loginAsUid)">Create URL</button>
        <div *ngIf="loginAsUrl">
            Copy this URL into a different browser: <input [(ngModel)]="loginAsUrl">
        </div>
    </div>

    <div>

        <hr/>

        <h3>Change Account Type</h3>
        <p>You can lookup a UID and change their account type here:</p>
        <div>
            UID: <input [(ngModel)]="accountTypeChange.uid"><br/>
            Account Type: <select style="width:28em;" [(ngModel)]="accountTypeChange.account_type" >
                <option *ngFor="let accountType of accountTypes" [value]="accountType">{{accountType}}</option>
            </select><br/>
            <button (click)="changeAccountType(accountTypeChange)">Change</button><br/>
        </div>

    </div>

    <div>

        <hr/>

        <h3>Merge Accounts</h3>
        <p>This will mark the "From" account as a duplicate and re-assign all of it's roles to the other account. A log will also be made to track the transfer against a timestamp </p>
        <div>From UID : <input type="number" [(ngModel)]="mergeFromUid"></div>
        <div>To UID :   <input type="number" [(ngModel)]="mergeToUid"></div>
        <div> <button [disabled]="isSaving" (click)="mergeUidPair()">Merge</button> </div>
        
        <hr>

        <p>or, use the form below for multi-line bulk replacement (one line per change, comma delimited, last number is the one being merged to):</p>
        <p>
            <textarea [(ngModel)]="mergeBulkInput"></textarea>
        </p>
        <div *ngIf="mergeBulkInput">
            <button [disabled]="isSaving" (click)="mergeBulk()">Merge Bulk</button>
        </div>


    </div>

</div>
