import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LangService } from '../../core/lang.service';
import { RoutesService } from 'src/app/api/routes.service';

@Component({
  selector: 'view-s-user-roles',
  templateUrl: './view-s-user-roles.component.html',
  styleUrls: ['./view-s-user-roles.component.scss']
})
export class ViewSUserRolesComponent implements OnInit {

  constructor(
    private auth:AuthService,
    private whiteLabel:WhitelabelService,
    private loginGuard:LoginGuardService,
    private lang: LangService,
    private router: Router,
    private breadcrumbsService: BreadcrumbsService,
    private routes: RoutesService
  ) { }

  public breadcrumb: IBreadcrumbRoute[];
  lookupForm:any = { }

  columnFilters = [
    {prop: 'isAcctDetail', isActive: false},
    {prop: 'isGroupDetail', isActive: false},
    {prop: 'isRoleDetail', isActive: false},
  ]
  hideRevoked:boolean = true;

  columns = [
    {slug:'uid',},
    {slug:'first_name',},
    {slug:'last_name',},
    {slug:'contact_email', isAcctDetail: true},
    {slug:'auth_email', isAcctDetail: true},
    {slug:'created_on', isAcctDetail: true,},
    {slug:'group_id',},
    {slug:'district_id', isGroupDetail: true,},
    {slug:'district_code', isGroupDetail: true,},
    {slug:'school_id', isGroupDetail: true,},
    {slug:'school_code', isGroupDetail: true,},
    {slug:'group_type',},
    {slug:'role_type',},
    {slug:'role_created_on', isRoleDetail: true,},
    {slug:'is_revoked', isRoleDetail: true,},
    {slug:'revoked_on', isRoleDetail: true,},
    {slug:'revoked_by', isRoleDetail: true, isRevokedBy: true,},
  ]

  accountTypes = [
    // 'cl_sys',
    // 'cron',
    // 'test-ctrl-adm-bcfsa',
    // 'test-ctrl-data',
    'test-ctrl-adm-bcgrad-school',
    'test-ctrl-adm-bcfsa-school',
    'test-ctrl-adm-bcfsa-school-score-entry',
    'test-ctrl-adm-bcgrad-district',
    'test-ctrl-adm-bcfsa-district',
    'test-ctrl-adm-bcfsa-district-score-entry',
    'test-ctrl-adm-bcgrad-ministry',
    'ministry-admin',
    'test-auth',
    'dist-admin',
    'test-ctrl-exch',
    'school-admin',
    // 'field-tester',
    // 'test-admin',
    // 'mrkg-cand',
    // 'mrkg-coord',
    // 'mrkg-crtl',
    // 'mrkg-ctrl',
    // 'mrkg-lead',
    // 'mrkg-mrkr',
    // 'mrkg-supr',
    // 'mrkg-upld',
    'support',
    'scor-lead',
    'scor-rafi',
    'scor-scor',
    'scor-supr',
    // 'test-ctrl',
    'student',
    'educator'
  ]

  responseData = [];
  accountTypeChange: any = {};

  ngOnInit(): void {
    
    this.loginGuard.activate();
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(this.lang.tra('title_dashboard'), `/${this.lang.c()}/support/dashboard`),
      this.breadcrumbsService._CURRENT('User Roles', this.router.url),
    ];
  }

  async runSearch(){
    this.responseData = [];
    this.responseData = await this.auth.apiFind(this.routes.SUPPORT_USER_ROLES, {query: this.lookupForm});
  }

  /** Revoke or unrevoke a user role after confirmation */
  toggleUserRoleRevoked(userRole){
    const {ur_id, is_revoked} = userRole;
    if (!ur_id) return;
    const newRevokedStatus = is_revoked ? 0 : 1;
    const confirmMessage = `Are you sure you want to ${ newRevokedStatus ? 'revoke' : 'unrevoke'} this role?`
    this.loginGuard.confirmationReqActivate({
      caption: confirmMessage,
      confirm: () => {
        this.auth.apiUpdate(this.routes.SUPPORT_USER_ROLES, ur_id, {
          ur_id,
          is_revoked: newRevokedStatus,
        })
        .then(() => {
          userRole.is_revoked = newRevokedStatus;
        })
        .catch((err) => {
          this.loginGuard.quickPopup('Error changing user role status: ' + err.message)
        })
      }
    })
  }

  isColumnHidden(column:{slug:string, isGroupDetail: boolean, isRevokedBy?: boolean}){
    let isHidden = false;
    this.columnFilters.forEach(filter => {
      if (column[filter.prop] && !filter.isActive){
        isHidden = true;
      }
    })

    // Hide revoked_by column when hideRevoked is true (showing only non-revoked roles)
    if (column.isRevokedBy && this.hideRevoked) {
      isHidden = true;
    }

    return isHidden;
  }

  /** Format the revoked_by column to show 'email (uid)' format */
  formatRevokedBy(row: any): string {
    return `${row.revoked_by_email} (${row.revoked_by_uid})`;
  }

  isSaving:boolean;
  mergeFromUid:number;
  mergeToUid:number;
  mergeBulkInput:string;
  async mergeUidPair(){
    this.isSaving = true;
    try {
      await this.auth
        .apiCreate('public/support/user-role-merge', {
          from_uid: this.mergeFromUid,
          to_uid:   this.mergeToUid,
        });
      alert('Successfully merged accounts');
    }
    catch(e){
      alert('Failed to merge accounts');
    }
    
    this.isSaving = false;
  }

  loginAsUid:number;
  loginAsUrl:string;
  async loginAs(target_uid:number) {
    const record = await this.auth.apiCreate('public/support/login', {target_uid});
    this.loginAsUrl = window.location.origin + '/#/en/support/dashboard/' + record.id + 'x' + record.secret;
  }

  async changeAccountType(accountTypeChange:any){
    await this.auth.apiPatch(this.routes.SUPPORT_USER_ROLES, accountTypeChange.uid, {account_type: accountTypeChange.account_type})
    alert('The account type has been updated for this user')
  }

  async mergeBulk(){
    if (this.mergeBulkInput){
      const merges = ( ''+this.mergeBulkInput )
        .split('\n')
        .map(row => row.split(',') 
                       .map(cell => cell.trim() ) 
        )
      if (confirm(`Are you sure you want to proceed with ${merges.length} merges?`)){
        this.isSaving = true;
        for (let i=0; i<merges.length; i++){
          const row = merges[i];
          const to_uid = row[row.length-1];
          for (let j=0; j<row.length-1; j++){ 
            const from_uid = row[j]
            console.log('Merging from', from_uid, 'to', to_uid);
            await this.auth
              .apiCreate('public/support/user-role-merge', {
                from_uid,
                to_uid,
              });
          }
        }
        this.isSaving = false;
      }
    }
  }

}
