import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';

@Component({
  selector: 'system-message',
  templateUrl: './system-message.component.html',
  styleUrls: ['./system-message.component.scss']
})
export class SystemMessageComponent implements OnInit {

  @Input() slug:string;
  
  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService
  ) { }

  systemMessage: string = "";
  color: string = "";

  ngOnInit(): void {
    this.loadSystemMessage();
  }

  ngOnChanges(){
    this.loadSystemMessage();
  }

  async loadSystemMessage(){
    await this.auth.apiGet(this.routes.TEST_CTRL_SYSTEM_MESSAGE, this.slug).then((res)=>{
      this.systemMessage = JSON.parse(res.data);
      this.color = res.color;
    })
  }

  getSystemMessage(){
    return this.systemMessage?.[this.lang.c()]
  }

}
