<div class="space-between" style="gap: 2em; align-items: flex-start">

    <div>
        <div *ngIf="!newIssue">
            <button class="button" (click)="newIssueStart()">
                Report New Issue
            </button>
        </div>
        <div *ngIf="newIssue">
            <p>You can post quick messages here (not associated to a window or student, but is sent to our support team)</p>
            <div class="select is-small">
                <select [(ngModel)]="newIssue.categorySelection">
                    <option *ngFor="let category of issueCategories" [value]="category.slug">
                        {{category.caption}}
                    </option>
                </select>
            </div>
            <option></option>
            <textarea class="textarea" [(ngModel)]="newIssue.msg"></textarea>
            <div style="margin-top:1.5em;">
                <div class="space-between">
                    <div>
                        Callback options?
                        <p class="help">(these can be left blank to ensure fastest response and resolution)</p>
                    </div>
                    <mat-slide-toggle [(ngModel)]="newIssue.isCallback" >
                    </mat-slide-toggle>
                </div>
                <ng-container *ngIf="newIssue.isCallback">
                    <div class="contact-options">
                        <strong>Phone</strong>
                        <div class="field">
                            <div class="control has-icons-left">
                              <input class="input is-small" [(ngModel)]="newIssue.phone_number" type="tel" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}">
                              <span class="icon is-small is-left">
                                <i class="fas fa-phone"></i>
                              </span>
                            </div>
                        </div>
                    </div>
                    <div class="contact-options">
                        <strong>Email</strong>
                        <div class="field">
                            <div class="control has-icons-left">
                              <input class="input is-small" [(ngModel)]="newIssue.contact_email" type="email" >
                              <span class="icon is-small is-left">
                                <i class="fas fa-envelope"></i>
                              </span>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div style="margin-top:1.5em;">
                <button (click)="newIssueConfirm()" class="button">Save</button>
                <button (click)="newIssueCancel()" class="button is-danger">Cancel</button>
            </div>
        </div>
    </div>

    <div>
        Previously Reported Issues
        <table class="table">
            <tr>
                <th>#</th>
                <th>Category</th>
                <th>Description</th>
                <th>Date/Time</th>
            </tr>
            <tr *ngFor="let record of records">
                <td>{{record.ric_id}}</td>
                <td>{{record.categorySelection}}</td>
                <td>
                    <div class="content">
                        <div>{{record.msg}}</div>
                        <ul>
                            <li *ngFor="let comment of record.comments">
                                <div><strong><u>Replied on: {{renderDate(comment.created_on)}}</u></strong></div>
                                <div>
                                    <markdown 
                                        class="markdown" 
                                        [data]="comment.comment" 
                                        [class.is-condensed]="false"
                                        [class.is-flex]="false"
                                        ngPreserveWhitespaces
                                    ></markdown>
                                </div>
                            </li>
                        </ul>
                    </div>
                </td>
                <td>{{record.created_on}}</td>
            </tr>
        </table>
    </div>


</div>