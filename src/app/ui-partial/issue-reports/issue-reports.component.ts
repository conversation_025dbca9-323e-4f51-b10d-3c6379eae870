import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { LangService } from 'src/app/core/lang.service';
import { mtz } from 'src/app/core/util/moment';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

const ISSUE_CATEGORIES = [
  {slug:'USER_SYS_FAM', caption:'How-To Support'},
  {slug:'ADMIN_ACCESS', caption:'Administrator Access'},
  {slug:'ACCOMMODATION', caption:'Accommodation'},
  {slug:'TECH_CONFIG_LOCKDOWN', caption:'Lockdown Browser Setup'},
  {slug:'TECH_CONFIG_DEVICE', caption:'Device Compatibility'},
  {slug:'REG_STU_ENROL', caption:'Student Enrollment/Registrations'},
  {slug:'RESULTS_ACCESS', caption:'Acessing Reports'},
  {slug:'SYS_ISSUE', caption:'System Issue'},
]

interface IIssueRecord {
  ric_id:number,
  categorySelection:string,
  msg:string,
  created_on?:string,
  phone_number?:string,
  contact_email?:string,
  lang?:string,
}

interface INewIssue {
  msg?:string,
  phone_number?:string,
  contact_email?:string,
  lang?:string,
  categorySelection?:string,
  // 
  isCallback?:boolean,
  isSaving?:boolean,
}

@Component({
  selector: 'issue-reports',
  templateUrl: './issue-reports.component.html',
  styleUrls: ['./issue-reports.component.scss']
})
export class IssueReportsComponent implements OnInit {

  records:IIssueRecord[] = [];
  newIssue:INewIssue | null = null
  issueCategories = ISSUE_CATEGORIES

  constructor(
    private auth:AuthService,
    private lang:LangService,
    private loginGuard:LoginGuardService,
    private whitelabelService: WhitelabelService,
  ) { }


  ngOnInit(): void {
    if (this.auth.getUid()){ // quick check, only care about authenticated people for now
      this.loadMyIssues();
    } 
  }

  async loadMyIssues(){
    this.records = await this.auth.apiFind('public/user-authenticated/report-issue')
  }

  newIssueStart(){
    this.newIssue = {}
  }
  newIssueCancel(){
    this.newIssue = null
  }
  async newIssueConfirm(){
    let {msg, categorySelection, contact_email, phone_number, isCallback, isSaving} = this.newIssue;
    if (isSaving){
      this.loginGuard.quickPopup('Please allow previous report to be received before sending another request.')
      return;
    }
    if (!msg){
      this.loginGuard.quickPopup('Please describe your issue.')
      return;
    }
    if (!isCallback){
      contact_email = null
      phone_number = null
    }

    this.newIssue.isSaving = true
    try {
      const lang = this.lang.c();
      const newRecord = await this.auth.apiCreate('public/user-authenticated/report-issue', {
        msg,
        categorySelection, 
        contact_email, 
        phone_number,
        lang
      })
      this.records.unshift({
        ric_id: newRecord.id, 
        msg,
        categorySelection,
        contact_email, 
        phone_number,
        lang
      })

    }
    catch(e){
      this.loginGuard.quickPopup('Connection issue. Could not save your support request. Please ensure you are logged in, and if you continue experiencing issues, please email '+ this.whitelabelService.getSiteText('supportEmail'))
      this.newIssue.isSaving = false
    }


    // todo: cache email and phone number in local storage
    
    this.newIssue = null
  }
  renderDate(date:string){
    return mtz(date).format(this.lang.tra('datefmt_dashboard_short'));
  }

}
