import {
  AfterViewInit,
  Directive,
  ElementRef,
  Input,
  NgZone,
  OnDestroy,
  Renderer2,
} from '@angular/core';

@Directive({
  selector: '[pan-zoom]',
})
export class PanZoomMinimapDirective implements AfterViewInit, OnDestroy {
  /** DOM references */
  private container!: HTMLElement;          // host element

  @Input() isPanZoomEnabled: boolean[];
  

  /** Pointer state */
  private isDown = false;
  private startX = 0;
  private startY = 0;
  private startScrollX = 0;
  private startScrollY = 0;

  /** Unregister helpers */
  private unlisteners: Array<() => void> = [];
  private monitorListener;

  constructor(
    private elRef: ElementRef<HTMLElement>,
    private rd: Renderer2,
    private zone: NgZone,
  ) {}

  /* ---------- life‑cycle -------------------------------------------------- */

  ngAfterViewInit(): void {
    console.log('bindEvents::ngAfterViewInit')
    this.container = this.elRef.nativeElement;
    if (this.isPanZoomEnabled && !this.isDeviceiPad()){
      this.bindEvents();
      this.monitorListener = setInterval(() => { 
        console.log('bindEvents::monitor', this.container === this.elRef.nativeElement)
      }, 5000)
    }

  }
  
  ngOnDestroy(): void {
    // detach all listeners
    this.unbindEvents()
  }

  unbindEvents(): void {
    try {
      console.log('bindEvents::undo')
      this.unlisteners.forEach((u) => u());
      clearInterval(this.monitorListener);
    }
    catch (e){
      console.error('Error unbinding events:', e);
    }
  }

  isDeviceiPad(){
    return (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 0) || navigator.platform === 'iPad';
  }

  /* ---------- event binding ---------------------------------------------- */

  private bindEvents(): void {
    this.unbindEvents()
    console.log('bindEvents', this.isPanZoomEnabled)
    // Pointer drag
    this.listen(this.container, 'pointerdown', (e: PointerEvent) => {
      console.log('bindEvents::pointerdown');
      const el = this.container
      if (e.button !== 0) return;
      e.preventDefault();
      el.setPointerCapture(e.pointerId);
      el.classList.add('active');
  
      const vParent = this.getScrollableY(el);
      let prevX = e.clientX;
      let prevY = e.clientY;
  
      function onMove(ev) {
        const dx = ev.clientX - prevX;
        const dy = ev.clientY - prevY;
  
        // Horizontal scroll
        const maxLeft = el.scrollWidth - el.clientWidth;
        el.scrollLeft = Math.max(0, Math.min(maxLeft, el.scrollLeft - dx));
  
        // Vertical scroll logic
        const maxTop = el.scrollHeight - el.clientHeight;
        const atTop    = el.scrollTop <= 0 && dy > 0;
        const atBottom = el.scrollTop >= maxTop && dy < 0;
        const canScrollSelfY = !(atTop || atBottom);
  
        if (canScrollSelfY) {
          el.scrollTop -= dy;
        } else {
          vParent.scrollBy(0, -dy);
        }
  
        prevX = ev.clientX;
        prevY = ev.clientY;
      }
  
      function endDrag() {
        console.log('bindEvents::endDrag')
        el.releasePointerCapture(e.pointerId);
        el.removeEventListener('pointermove', onMove);
        el.removeEventListener('pointerup', endDrag);
        el.removeEventListener('pointercancel', endDrag);
        el.classList.remove('active');
      }
  
      el.addEventListener('pointermove', onMove);
      el.addEventListener('pointerup', endDrag, { once: true });
      el.addEventListener('pointercancel', endDrag, { once: true });
    });
  }

  private getScrollableY(el) {
    let p = el.parentElement;
    while (p && p !== document.documentElement) {
      if (p.scrollHeight > p.clientHeight && getComputedStyle(p).overflowY !== 'visible') {
        return p;
      }
      p = p.parentElement;
    }
    return document.scrollingElement;
  }

  private listen(
    target: any,
    event: string,
    handler: (evt: any) => void,
  ): void {
    const un = this.rd.listen(target, event, handler);
    this.unlisteners.push(un);
  }

}


