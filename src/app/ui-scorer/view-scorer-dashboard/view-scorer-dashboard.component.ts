import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { SidepanelService } from '../../core/sidepanel.service';
import { UserSiteContextService } from '../../core/usersitecontext.service';
import { Router, ActivatedRoute } from '@angular/router';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import { memo } from '../../ui-testrunner/element-render-video/element-render-video.component';
import { SAMPLE_MSG_CTR_MESSAGES } from './data/sample-message-centre';
import { IAssignedItemComponent, AssignedItemComponentType, AssignedItemStatus, IAssignedItem } from './models/assigned-item';
import * as moment from 'moment-timezone';
import { MyScorerTasksService } from '../my-scorer-tasks.service';
import { AuthService } from '../../api/auth.service';
import { QT_CUT_SCORE } from '../view-scorer-qualifying-test/view-scorer-qualifying-test.component';


const CLAIM_ALL_MAX_ITEMS = 10;

enum GENDER {
  MALE = 'M',
  FEMALE = 'F'
}
@Component({
  selector: 'view-scorer-dashboard',
  templateUrl: './view-scorer-dashboard.component.html',
  styleUrls: ['./view-scorer-dashboard.component.scss']
})
export class ViewScorerDashboardComponent implements OnInit {

  constructor(
    public whitelabelService: WhitelabelService,
    private route: ActivatedRoute,
    private router: Router,
    private sidePanel: SidepanelService,
    public lang: LangService,
    private userSiteContext: UserSiteContextService,
    private myScorerTasks: MyScorerTasksService,
    public  loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private auth:AuthService,
    private routes: RoutesService
  ) { }

  public isLoaded:boolean;
  public AssignedItemStatus = AssignedItemStatus;
  public AssignedItemComponentType = AssignedItemComponentType;
  public breadcrumb:any[];
  private memoClaimIterator = new Map();
  desiredClaimQuantity:number;
  assignedItems = [];
  scorerDashboardDocs = [];

  unreadCount = 0;
  isSummaryView:boolean;
  isSummaryViewAvailable: boolean = false;
  isSummaryViewSelectAll: boolean = false;

  ngOnInit(): void {
    this.sidePanel.activate();
    this.sidePanel.unexpand();
    this.loginGuard.activate();

    this.breadcrumb = [
      this.breadcrumbsService.SCOR_SCOR_DASHBOARD()
    ];

    this.initRouteView();
  }

  isFr = () => this.lang.c() == 'fr';
  currentLang(){
    return this.lang.c();
  }
  langs = [
    {code: 'en', caption: 'English'},
    {code: 'fr', caption: 'Français'},
  ];
  isLangActive(langCode: string) {
    return (this.currentLang() === langCode);
  }
  setLang(langCode: string) {
    this.lang.loadInLang(langCode);
  }

  initRouteView(){
    this.myScorerTasks.sub().subscribe(info => {
      if (info){
        this.isLoaded = true;
        this.isCheckingBatches = true;
        this.assignedItems = this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsList;
        this.assignedItems.forEach(async (item) => {
          const available = await this.auth.apiGet('/public/scor-scor/batches/claim', item.id);
          this.availableBatches.set(item.id, available.numBatches);
        });
        this.isCheckingBatches = false;
        this.scorerDashboardDocs = info.scorerDashboardDocs;

        this.isSummaryViewAvailable = this.assignedItems.every(item => item.mw_is_scorer_summary_view_allowed)
      }
    })
    
    this.auth.apiFind('public/scor-scor/message-centre').then(count => {
      this.unreadCount = count;
    })
  }

  getSupervisorRoute(assignedItem:IAssignedItem){
    return `${this.lang.c()}/scor-lead/supervisor/s2s/${assignedItem.window_id}/${assignedItem.item_slug}`
  }

  getSupervisorScorersRoute(assignedItem:IAssignedItem){
    return `${this.lang.c()}/scor-lead/supervisor/scorerstats/${assignedItem.window_id}/summary/${assignedItem.item_slug}`
  }

  getMessages(){
    return SAMPLE_MSG_CTR_MESSAGES;
  }

  getAssignedItems(){
    return this.myScorerTasks.getAssignedItems();
  }

  now(){
    return moment().format( this.getTimeFormat() )
  }
  tomorrow(){
    return moment().add(1, 'd').format( this.getTimeFormat() )
  }

  renderWindowDate(date:string){
    let fmt = 'ddd. MMM. D';
    if (this.isFr()){
      fmt = 'dddd D MMM'
    }
    return moment(date).format( fmt )
  }

  getTimeFormat(){
    if (this.isFr()){
      return '[le] dddd D MMM [à] H [h] mm'
    }
    else{
      return 'ddd. MMM. D [at] h:mma'
    }
  }

  getCurrentBatchDueDate(item:IAssignedItem){
    if (item.claimedBatches){
      const currentBatch = item.claimedBatches[0];
      if (currentBatch){
        const msg = this.lang.tra('txt_batch_expiry_notic')
        return msg + moment(currentBatch.created_on).add(1, 'd').format( this.getTimeFormat() ) // .add(1, 'd')
      }
    }
  }

  openScorerReport(item:any){

    const percSym = () => {
      if (this.isFr()){
        return ' %'
      }
      return '%'
    }

    const propo = (nume:any, deno:any) => {
      if (deno){
        return (100*(+nume)/(+deno)).toFixed(3).toString().replace('.', tra('decimal_delim')) +percSym()
      }
      else {
        return '0'+percSym()
      }
    }

    const tra = (str:string) => this.lang.tra(str);
    const informations = [`**Item**&nbsp;: ${item.name}`];

    const items = this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsMap.get(item.item_id);
    for(const item of items) {

      let batchesScored = '0';
      if (item.batch_size){
        batchesScored = (item.num_responses_scored / item.batch_size).toFixed(2);
        batchesScored = batchesScored.toString().replace('.', tra('decimal_delim'))      
      }
  
      let information = `
   ${item.scale_slug ? '- ' + item.scale_slug +' :' : ''} 
  
  **${tra('lbl_cum_resp_scor')}**&nbsp;: ${item.num_responses_scored}
  
  **${tra('lbl_cum_bat_scor')}**&nbsp;: ${batchesScored}
  
  **${tra('lbl_cum_batches_claimed')}**&nbsp;: ${item.num_batches_claimed_total} ${tra('lbl_includes_expired')}
  
  **${tra('lbl_cum_valid')}**&nbsp;:  
  - ${propo(item.num_validity_exact, item.num_validity_total)} ${tra('lbl_cum_resp_score_exact')}
  - ${propo((+item.num_validity_exact)+(+item.num_validity_adj), item.num_validity_total)} ${tra('lbl_cum_resp_score_validity')} 
  
  **${tra('lbl_cum_adjacency')}**&nbsp;:  
  - ${propo(item.num_validity_over, item.num_validity_total)} ${tra('lbl_score_adjacency_high')}
  - ${propo(item.num_validity_under, item.num_validity_total)} ${tra('lbl_score_adjacency_low')}  
  `
      if (item.isRollValCurr){
        information += `
        
  **${tra('lbl_rolling_batch')}**&nbsp;:    
  - ${propo(item.cache_roll_val_exact, 1)} ${tra('lbl_rolling_resp_score_exact')}
  - ${propo(item.cache_roll_val_adj, 1)} ${tra('lbl_rolling_resp_score_validity')}
  
  `
      }
      informations.push(information);

    }

    this.loginGuard.quickPopup(informations.join('\n'));
  }

  getNextComponent(item:IAssignedItem){
    const arr = item.components;
    for (let i=0; i<arr.length; i++){
      const itemComponent = arr[i];
      switch(itemComponent.status){
        case AssignedItemStatus.ACTIVE:
        case AssignedItemStatus.PENDING:
          return itemComponent;
        case AssignedItemStatus.COMPLETED:
        case AssignedItemStatus.NOT_AVAILABLE:
          continue;
      }
    }
  }

  getNextActionCaption(item:IAssignedItem){
    const itemComponent = this.getNextComponent(item);
    const caption = itemComponent.caption;
    switch (itemComponent.status){
      case AssignedItemStatus.PENDING: return this.lang.tra('btn_templ_start')+' '+this.insertProposition(caption);
      case AssignedItemStatus.ACTIVE: return this.lang.tra('btn_templ_resume')+' '+this.insertProposition(caption);
    }
  }

  private conjugateEnding(str:string, subject:string){
    const gender = this.approxGender(subject);
    if (gender === GENDER.FEMALE){
      if (this.startsWith(str, 'termin')){  return str + 'e' }
      if (this.startsWith(str, 'verro')){   return str + 'e' }
    }
    return str;
  }

  private insertProposition(str:string){
    if (this.isFr()){
      switch (this.approxGender(str)){
        case GENDER.FEMALE: str = 'la '+ str; break;
        default:
        case GENDER.MALE: str = 'le '+ str; break;
      }
    }
    return str;
  }

  approxGender(str:string){
    str = str.toLowerCase();
    if (this.startsWith(str, 'form')){      return GENDER.FEMALE }
    else if (this.startsWith(str, 'nota')){ return GENDER.FEMALE }
    else if (this.startsWith(str, 'test')){ return GENDER.MALE }
    else {  return GENDER.MALE }
  }

  private startsWith(str:string, comparison:string){
    return (str.substr(0, comparison.length) === comparison)
  }

  getNextActionRoute(item:IAssignedItem){
    const itemComponent = this.getNextComponent(item);
    const useSyncBatchesToWmiId = true;
    return this.getItemComponentRoute(item, itemComponent, useSyncBatchesToWmiId);
  }

  getAvailableClaimsRoute(item: IAssignedItem) {
    const useSyncBatchesToWmiId = true;
    const id = useSyncBatchesToWmiId ? item.sync_batches_to_wmi_id : item.id;

    return `/${this.lang.c()}/${AccountType.SCOR_SCOR}/available-claims/${id}`;
  }

  isNoneRemaining(item:IAssignedItem){
    return (item.maxBatchNumClaim === 0) || (item.numBatchesAvail === 0)
  }

  isPendingPairedMarkingStage1(item: IAssignedItem){
    const isPairedMarking = item.is_paired_marking_active || item.is_paired_marking_read_only
    if (!isPairedMarking) return false;
    const isPendingStage1 = item.is_paired_draft_completed && !item.is_paired_draft_stage_completed
    return isPendingStage1
  }

  isPendingClaim(item:IAssignedItem){
    const itemComponent = this.getNextComponent(item);
    if (itemComponent && itemComponent.componentType === AssignedItemComponentType.SCORING){
      if (this.checkStatusOpen(item, itemComponent, {isIgnoreBatchClaim:true})){
        if (item.numBatchesClaimed === 0){
          return true;
        }
      }
    }
  }
  
  isPrimaryScoringAvailable(item:IAssignedItem) {
    if (item.mw_is_scoring_disabled || item.mw_is_not_open || item.mw_is_closed){
      return false;
    }
    if (item.id !== item.sync_batches_to_wmi_id) {
      return false;
    }
    for (let assignedItem of this.getAssignedItems()) {
      if (item.id === assignedItem.sync_batches_to_wmi_id) {
        if (assignedItem.numBatchesClaimed !== 0) {
          return false;
        }
      }
    }
    return true;
  }

  isClaiming:boolean;
  async claimBatches(item:IAssignedItem, desiredClaimQuantity:number, isFromClaimAll: boolean = false){
    if (this.isClaiming && !isFromClaimAll){ throw new Error() }
    if(desiredClaimQuantity){
      if (!isFromClaimAll) this.isClaiming = true;

      const itemList = [];
      for (let scale of item.item_scales_info) {
        if (!itemList.includes(scale.group_to_mwi_id)) {
          itemList.push(scale.group_to_mwi_id);
        }
      }
      for (let item of itemList) {
        await this.myScorerTasks.claimBatch(item, desiredClaimQuantity).catch((e) => {
          if (!isFromClaimAll){
            if(e.message){
              this.onClaimBatchFail(e.message)
            }
            else{
              this.loginGuard.quickPopup(this.lang.tra('txt_dev_conn_error_1'))
            }
          } else {
            throw e;
          }
        })
      }
      if (!isFromClaimAll) {
        await this.myScorerTasks.reloadScorerInfo();
        this.isClaiming = false;
      }
      item.numBatchesClaimed = (+item.numBatchesClaimed) + (+desiredClaimQuantity);
      item.dateTimeBatchesDue = this.now();
      item.isSelected = false;
    }
  }

  isHideDropdown() {
    return this.whitelabelService.getSiteFlag('IS_SCORE_HIDE_DROPDOWN');
  }

  onClaimBatchFail (message: string = null){
    switch(message) {
      case "NO_BATCHES_REMAINING":
        this.loginGuard.quickPopup(this.lang.tra('lbl_not_avail_claim_batch'))
        break;
      case "Failed to fetch":
        this.loginGuard.quickPopup(this.lang.tra('txt_dev_conn_error_1'))
        break;
      case null:
        this.loginGuard.quickPopup(this.lang.tra('txt_dev_conn_error_1'))
      default:
        this.loginGuard.quickPopup(message)
    }
  }

  getItemComponentRoute(item:IAssignedItem, itemComponent:IAssignedItemComponent, useSyncBatchesToWmiId? : boolean){
    if (!this.checkStatusOpen(item, itemComponent)){
      return undefined;
    }

    const id = useSyncBatchesToWmiId ? item.sync_batches_to_wmi_id : item.id;

    if (itemComponent.status)
    switch (itemComponent.componentType){
      case AssignedItemComponentType.TRAINING_MATERIALS: 
      case AssignedItemComponentType.PRACTICE: 
      case AssignedItemComponentType.QUALIFYING:  
        return `/${this.lang.c()}/${AccountType.SCOR_SCOR}/training/${item.id}/${itemComponent.componentType}`
      case AssignedItemComponentType.SCORING:
        return `/${this.lang.c()}/${AccountType.SCOR_SCOR}/scoring/${id}`;
    }
  }

  getClaimIterator(maxBatchNumClaim:number, numBatchesAvail:number){
    const len = Math.min(maxBatchNumClaim, numBatchesAvail);
    if (this.memoClaimIterator.has(len)){
      return this.memoClaimIterator.get(len)
    }
    else{
      const options = [];
      for (let i=0; i<len; i++){
        const value = i +1;
        options.push({ value })
      }
      this.memoClaimIterator.set(len, options)
      return options;
    }
  }



  getItemComponentStatus(item:IAssignedItem, itemComponent:IAssignedItemComponent){
    switch (itemComponent.componentType){
      case AssignedItemComponentType.TRAINING_MATERIALS: return this.getComponentCompletionStatus(itemComponent);
      case AssignedItemComponentType.PRACTICE: return this.getComponentCompletionStatus(itemComponent);
      case AssignedItemComponentType.QUALIFYING:  return this.getComponentCompletionStatus(itemComponent);
      case AssignedItemComponentType.SCORING:  return this.getComponentProgressStatus(item, itemComponent);
    }
  }
  getComponentProgressStatus(item:IAssignedItem, itemComponent:IAssignedItemComponent){
    const conjugate = (transSlug:string) => {
      return this.conjugateEnding(
        this.lang.tra(transSlug), 
        itemComponent.caption
      );
    }
    if (itemComponent.componentType === AssignedItemComponentType.SCORING && itemComponent.status === AssignedItemStatus.FAILED ){
      return conjugate('lbl_locked_scor')
    }
    else if (this.checkStatusOpen(item, itemComponent)){
      // return `${item.numBatchesScored} of ${item.numBatchesClaimed} batches scored`;
      return `${item.numBatchesClaimed} `+ this.lang.tra('txt_batches_remaining');
    }
    else if ((itemComponent.status === AssignedItemStatus.ACTIVE) && this.isNoneRemaining(item)) {
      return this.lang.tra('lbl_completed_scor')
    }
    return this.getComponentCompletionStatus(itemComponent);
  }
  checkStatusOpen(item:IAssignedItem,itemComponent:IAssignedItemComponent, options:any = {}){
    if (itemComponent.componentType === AssignedItemComponentType.SCORING ){
      if (!options.isIgnoreBatchClaim){
        if (item.numBatchesClaimed <= 0 ){
          return false;
        }
      }
    }
    switch (itemComponent.status){
      case AssignedItemStatus.ACTIVE:
      case AssignedItemStatus.PENDING:
        return true;
    }
    if (itemComponent.status === AssignedItemStatus.COMPLETED){
      return !!itemComponent.allowRevisit;
    }
    return false;
  }

  lbl_batches
  
  getComponentCompletionStatus(itemComponent:IAssignedItemComponent){
    const conjugate = (transSlug:string) => {
      return this.conjugateEnding(
        this.lang.tra(transSlug), 
        itemComponent.caption
      );
    }
    switch (itemComponent.status){
      case AssignedItemStatus.ACTIVE: return conjugate('lbl_active_scor'); 
      case AssignedItemStatus.COMPLETED: 
        if (itemComponent.componentType === "TRAINING_MATERIALS") {
          return  itemComponent.isPassFail ? conjugate('lbl_qual_succ') : conjugate('lbl_completed_score2');
        } else {
          return  itemComponent.isPassFail ? conjugate('lbl_qual_succ') : conjugate('lbl_completed_scor');
        }
      case AssignedItemStatus.NOT_AVAILABLE: return conjugate('lbl_locked_scor');
      case AssignedItemStatus.FAILED: return conjugate('lbl_no_att_rem');
      default: 
      case AssignedItemStatus.PENDING: return conjugate('lbl_available');
    }
  }

  availableBatches: Map<number, number> = new Map();
  isCheckingBatches = true;

  async recomputeBatchesAvailable(item){

    this.isCheckingBatches = true;
    const _recompute = async(item: IAssignedItem) => {
      const marking_window_item_id = item.id;
      if (item.__isRecomputingBatches){
        alert('Loading in progress')
        return;
      }
      item.__isRecomputingBatches = true;
      try {
        const res = await this.auth.apiGet('public/scor-scor/batches/available', marking_window_item_id)
        item.numBatchesAvail = res.batchesRemaining
        item.is_paired_draft_stage_completed = res.is_paired_draft_stage_completed
        item.is_paired_draft_completed = res.is_paired_draft_completed
        const targetAssignedItem = this.assignedItems.find(i => i.id == marking_window_item_id)
        if (targetAssignedItem){
          targetAssignedItem.is_paired_draft_stage_completed = res.is_paired_draft_stage_completed
          targetAssignedItem.is_paired_draft_completed = res.is_paired_draft_completed
        }
      }
      catch(e){
        alert('Cannot recompute batches.')
      }
      item.__isRecomputingBatches = false;
    }        
    this.assignedItems.forEach(async (item) => {
      const res = await this.auth.apiGet('/public/scor-scor/batches/claim', item.id);
      this.availableBatches.set(item.id, res.numBatches);
    })
    console.log(this.availableBatches);
    // check if multi-scaled
    const items = this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsMap.get(item.item_id);
    // recompute for each windowItemId
    for(const item of items){
      await _recompute(item);
    }
    this.isCheckingBatches = false;

  }

  isRecomputingBatches(item) {
    const items = this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsMap.get(item.item_id); 
    for(const item of items) {
      if(item.__isRecomputingBatches) return true;
    }
    return false;
  }


  renderQtCutScore(){
    return +(QT_CUT_SCORE*100).toPrecision(6) + this.lang.tra('lbl_percent')
  }

  isFailedScoring(item?:IAssignedItem){
    if (!item){
      item = this.getAssignedItems()[0] // to do, probably check for *any*
      if (!item){  return false; }
    }
    for (let i=0; i<item.components.length; i++){
      const component = item.components[i];
      if (component.componentType === AssignedItemComponentType.SCORING){
        if (component.status === AssignedItemStatus.FAILED){
          return true;
        }
      }
    }
    return false;
  }

  isFailedQualifying(item:IAssignedItem){
    for (let i=0; i<item.components.length; i++){
      const component = item.components[i];
      if (component.componentType === AssignedItemComponentType.QUALIFYING){
        if (component.status === AssignedItemStatus.FAILED){
          return true;
        }
      }
    }
    return false;
  }

  isOtherBatchOpened() {
    const assignedItems = this.getAssignedItems();
    for (let i = 0; i < assignedItems.length; i++) {
      if (assignedItems[i].claimedBatches && assignedItems[i].claimedBatches.length > 0) {
        return true;
      }
    }
    return false;
  }

  numAvailable(num1:number, num2:number){
    return Math.min(num1, num2)
  }


  getMessageCentreRoute(){
    return this.getBaseRoute()+`/message-centre`;
  }

  getBaseRoute(){
    return `/${this.lang.c()}/${AccountType.SCOR_SCOR}`;
  }

  isMultiScale (item_id) {
    return this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsMap.get(item_id).length > 1;
  }

  renderDocCaption(doc){
    let title = doc.title;
    try{
      title = JSON.parse(title);
    }catch(e){console.log(e)}
    return title[this.lang.c()];
  }

  hasUNF(item: any) {
    if(!item || !item.flags || item.flags.length == 0) {
      return false;
    }

    return item.flags.includes('UNF');
  }

  /** Have any items available to be claimed been selected */
  isAnyClaimBatchSelected(){
    const validITemsSelected = this.getItemsClaimBatchAvailable().filter(i => i.isSelected)
    return validITemsSelected.length > 0
  }

  /** Can the scorer claim batches for any items on the dashboard */
  isAnyClaimBatchAvailable(){
    return this.getItemsClaimBatchAvailable().length > 0
  }

  getItemsClaimBatchAvailable(){
    return this.assignedItems.filter(item => this.isPendingClaim(item) && !this.isFailedScoring(item) && this.isPrimaryScoringAvailable(item) && !item.is_paired_marking_read_only)
  }

  /** Request to claim 1 batch for all items that are eligible for claiming */
  async claimBatchesForAll(){

    const doClaimAll = async () => {
      this.isClaiming = true;
      const validITemsSelected = this.getItemsClaimBatchAvailable().filter(i => i.isSelected)
      const claimPromises = validITemsSelected.slice(0, CLAIM_ALL_MAX_ITEMS).map(item => {
        return this.claimBatches(item, 1, true)
      })
      await Promise.all(claimPromises)
      .catch((e) => {
        this.loginGuard.quickPopup(this.lang.tra('lbl_err_claim_batch_all'))
      })
      .finally(() => {
        this.myScorerTasks.reloadScorerInfo();
        this.isClaiming = false;
      })
    }

    const validITemsSelected = this.getItemsClaimBatchAvailable().filter(i => i.isSelected)
    const isMaxClaimAllExceeded = validITemsSelected.length > CLAIM_ALL_MAX_ITEMS
    const confirmCaption = isMaxClaimAllExceeded ? this.lang.tra('lbl_claim_all_confirm_max_exceed', undefined, {claim_all_max_items: CLAIM_ALL_MAX_ITEMS}) : this.lang.tra('lbl_claim_all_confirm')
    this.loginGuard.confirmationReqActivate({
      caption: confirmCaption,
      btnProceedConfig: {
        caption: this.lang.tra('lbl_yes')
      },
      btnCancelConfig: {
        caption: this.lang.tra('lbl_no')
      },
      confirm: ()=> {
        doClaimAll();
      },
    })


  }

  /** Have as many items as can be claimed at the same time been selected */
  isMaxItemsSelected(){
    const numSelected = this.assignedItems.filter(i => i.isSelected).length
    return numSelected == CLAIM_ALL_MAX_ITEMS
  }

  /** When entering summary view, reset the initial selection of the maximum number of valid items for claiming batches */
  onToggleSummaryView(){
    if (this.isSummaryView){
      this.selectedTopMaxItems();
      this.isSummaryViewSelectAll = true;
    }
  }

  /** Select the maximum number of valid items for claiming batches */
  selectedTopMaxItems(){
    const itemsClaimBatchAvailable: IAssignedItem[] = this.getItemsClaimBatchAvailable();
    const itemsToSelect = itemsClaimBatchAvailable.slice(0, CLAIM_ALL_MAX_ITEMS);
    itemsToSelect.forEach(item => {
      item.isSelected = true;
    })
  }

  /** Use shortcut selection to select a maximum number of items, or unselect all */
  onToggleSelectAll() {
    if (this.isSummaryViewSelectAll) {
      this.selectedTopMaxItems();
    } else {
      this.assignedItems.forEach((i: IAssignedItem) => {
        i.isSelected = false;
      })
    }
  }

  /** If initial maximum selection of valid items is changed by the user, uncheck the select all checkbox for consistency */
  onItemSelectionChange(item: IAssignedItem){
    if (!item.isSelected){
      this.isSummaryViewSelectAll = false;
    }
  }
}
