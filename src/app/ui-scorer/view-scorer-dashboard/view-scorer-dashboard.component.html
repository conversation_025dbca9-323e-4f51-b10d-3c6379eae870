<div class="page-body">
  <div>
 
    <div class="header-extension">
      <header 
        [breadcrumbPath]="breadcrumb" 
        [hasSidebar]="true"
        [hideSupportBtn]="true"
        techSupportDataKey="SCORING_SUPPORT">
      </header>
      
      <div class="header-ext-full">
        <panel-scoring-timer></panel-scoring-timer>
        <panel-scoring-notifications></panel-scoring-notifications>
      </div>
    </div>
  
    <div class="page-content is-fullpage" >
      <div class="page-container" *ngIf="!isLoaded"> <tra slug='loading_caption'></tra> </div>
      <div class="page-container" *ngIf="isLoaded">
          
        <div class="space-between" style="margin-bottom:2em;     width: 100%;">
          <div>
          </div>
          <div>
            <button 
              *ngFor="let lang of langs"
              (click)="setLang(lang.code)" 
              class="button is-small"
              [class.is-light]="!isLangActive(lang.code)"
              [class.is-info]="isLangActive(lang.code)">
                {{lang.caption}}
            </button>
          </div>
        </div>

          <div *wlCtx="'IS_SCORE_MSG_CENTRE'" class="message-center-container item-box">
            <div class="message-center-header">
              <div class="message-center-title"><tra slug="lbl_centre_messag"></tra></div>
              <button *ngIf="false" disabled class="button  is-outlined is-small"> 
                No New Notifications
                <!-- 3 New Notifications -->
              </button>
            </div>
            <div  class="notification">
              <a [routerLink]="getMessageCentreRoute()">
                <span *ngIf="!unreadCount || unreadCount == 0"><span class="notification-circle grey"></span>
                  <tra *ngIf="whitelabelService.isABED()" slug="lbl_txt_no_newmessages_abed"></tra>
                  <tra *ngIf="!whitelabelService.isABED()" slug="lbl_txt_no_newmessages"></tra>
                </span>
                <span *ngIf="unreadCount == 1"><strong><span class="notification-circle red"></span><tra slug="lbl_txt_single_newmessage"></tra></strong></span>
                <span><strong *ngIf="unreadCount > 1"><span class="notification-circle red"></span>{{unreadCount}} <tra slug="lbl_txt_newmessages"></tra></strong></span>
              </a>
            </div>
            <div *ngIf="whitelabelService.isABED()" [class.notification]="scorerDashboardDocs.length">
              <a *ngFor="let doc of scorerDashboardDocs" target="_blank" [href]="lang.tra(doc.link_slug)">
                {{renderDocCaption(doc)}}
              </a>
            </div>
            <div *ngIf="!whitelabelService.isABED()" class="notification">
              <a target="_blank" [href]="lang.tra('link_scoring_manual')">
                <tra slug="lbl_ref_guide"></tra>
              </a>
            </div>
            <div *ngIf="isFailedScoring()" class="notification is-warning"  style="margin-top:1em">
              <tra-md slug="txt_alert_low_valid_block"></tra-md>
            </div>
          </div>

          <div *ngIf="isSummaryViewAvailable" style="width: 100%">
            <div class="is-pulled-right">
              <mat-slide-toggle [(ngModel)]="isSummaryView" (change)="onToggleSummaryView()"><tra slug="lbl_sl_sum_view"></tra></mat-slide-toggle>
            </div>
          </div>
          <div *ngIf="!isSummaryView" class="item-containers">
            <div *ngFor="let item of assignedItems" class="item-box assigned-item-container">
              <div class="notification" *ngIf="item.elevated_role_type">
                <tra slug="txt_elevated_access"></tra>&nbsp;
                <tra slug="txt_scor_use_super_views"></tra>
                <ul>
                  <li>
                    <a routerLink="/{{getSupervisorRoute(item)}}" target="s2s-{{item.id}}">
                      <tra slug="lbl_sl_dash_responses_caption"></tra>
                    </a> 
                    - <tra slug="txt_supervisor_process_responses_instr"></tra>
                  </li>
                  <li *ngIf="item.mw_is_group_marking">
                    <a routerLink="/{{getSupervisorScorersRoute(item)}}" target="s2s-{{item.id}}">
                      <tra slug="Scorer Access and Summary"></tra>
                    </a>
                    - <tra slug="txt_supervisor_scorers_access_instr"></tra>
                  </li>
                </ul>
              </div>
              <div class="header-stack">
                <div class="header-left">
                  <div class="title bold">
                    <tra slug="lbl_assigned_item"></tra><tra slug="txt_colon"></tra>
                  </div>

                  <div class="item-name">
                    <!-- Name Of the item -->
                    <div style="padding: 0.8rem 0 0.8rem 0; text-transform: uppercase; font-weight: 800;">
                      <div>{{item.name}}</div>
                      <div *ngIf="item.mw_short_differ_name" style="font-size: 0.6em; opacity:0.8;">
                        {{item.mw_short_differ_name}}
                      </div>
                    </div>
                    <!-- Item label -->
                    <ng-container *wlCtx="'IS_SCOR_CARD_DETAIL'">
                      <div style="font-size:1rem; margin:0.1rem 0 0.5rem 0; color: black" class="item-code">{{item.item_slug}} </div>
                      <div *ngIf="isMultiScale(item.item_id)" >
                        <div style="font-size:1rem; margin:1rem 0 0.2rem 0">
                          <tra slug="lbl_scales"></tra><tra slug="txt_colon"></tra>
                        </div>
                        <div 
                          *ngFor="let item_scale of item.item_scales" 
                          class="item-scale"
                          style="margin-left:1rem">
                          <li>{{item_scale}}</li>
                        </div>  
                      </div>
                    </ng-container>
                </div>
                  <div *ngIf="item.is_paired_marking_active || item.is_paired_marking_read_only" class="has-text-grey">
                    <tra slug="lbl_paired_marking"></tra>
                    (<tra [slug]="item.is_paired_marking_active ? 'lbl_paired_marking_active' : 'lbl_paired_marking_readonly'"></tra>)
                  </div>
                  <div>
                    <div *ngIf="!isHideDropdown()">
                      {{availableBatches.get(item.id)}} <tra slug="txt_lbl_avail_scor_bat"></tra>
                    </div>
                    <!-- {{numAvailable(item.item_scales_info[0].numBatchesAvail, item.item_scales_info[0].maxBatchNumClaim)}} <tra slug="txt_lbl_avail_scor_bat"></tra> -->
                  </div>
                </div>
                <div class="header-right">
                  <button 
                    (click)="openScorerReport(item)" 
                    *wlCtx="'IS_SCORER_REPORT'"
                    class="button is-small is-success" 
                    style="margin-right:0em; border-radius: 0.3rem;">
                      <tra slug="lbl_scor_view_report"></tra>
                  </button>
                  <div style="font-size:0.85em; margin-top:0.5em;">
                    {{renderWindowDate(item.date_window_start)}}
                    <br/>
                    <span *ngIf="isFr()">au </span>
                    <span *ngIf="!isFr()"> to </span>
                    {{renderWindowDate(item.date_window_end)}}
                  </div>
                  <!-- <div>Tue., Apr. 6</div> -->
                  <!-- <div>3:30pm</div> -->
                </div>
              </div>
              <div style="display: flex; justify-content: space-between;">                    
                <a (click)="recomputeBatchesAvailable(item)">
                  <ng-container *ngIf="!isRecomputingBatches(item)">
                    <!-- Refresh button -->
                    <button 
                      class="button is-small is-info is-light" 
                      style="border-radius: 0.2rem; margin: 0.8rem 0 0.8rem 0; font-weight: 600;">
                      <tra slug="mrkg_refresh"></tra>
                    </button>
                  </ng-container>
                  <span *ngIf="isRecomputingBatches(item)">...</span>
                </a>
                <a [routerLink]="getAvailableClaimsRoute(item)"
                >
                  <button 
                    *ngIf="hasUNF(item)"
                    [disabled]="item.is_paired_marking_read_only"
                    class="button is-small is-info is-light" 
                    style="border-radius: 0.2rem; margin: 0.8rem 0 0.8rem 0; font-weight: 600;"
                  >
                    <tra slug="lbl_avail_unf_claims"></tra>
                  </button>
                </a>
              </div>
              <div class="inner-container">
                <div style="padding: 0.8rem 0 0.8rem 0; text-transform: uppercase; font-weight: 800;">
                  <tra slug="lbl_batch_status"></tra><tra slug="txt_colon"></tra>
                </div>
                <!-- <div *ngFor="let scale of item.item_scales_info">                  
                  <div *ngIf="scale.scale_slug" style="margin-bottom:0.4rem">
                    <tra style="color: rgb(102, 102, 102);" [slug]="scale.scale_slug"></tra> :
                  </div>
                  <div class="sub-item" *ngFor="let itemComponent of scale.components" style="margin-bottom: 1.5rem">
                    <span class="status-icon" [ngSwitch]="itemComponent.status">
                      <i *ngSwitchCase="AssignedItemStatus.ACTIVE" class="fas fa-circle" style="color:#57DD54"></i>
                      <i *ngSwitchCase="AssignedItemStatus.COMPLETED" class="fas fa-check" style="color:#C5C5C5"></i>
                      <i *ngSwitchCase="AssignedItemStatus.NOT_AVAILABLE" class="fas fa-lock" style="color:#C5C5C5"></i>
                      <i *ngSwitchCase="AssignedItemStatus.PENDING" class="fas fa-circle" style="color:#0085FF"></i>
                      <i *ngSwitchCase="AssignedItemStatus.FAILED" class="fas fa-square" style="color:rgb(204, 44, 44)"></i>
                    </span>
                    <a [routerLink]="getItemComponentRoute(item, itemComponent, true)">
                      {{itemComponent.caption}}
                    </a> 
                    ({{getItemComponentStatus(scale, itemComponent)}})
                  </div>
                </div> -->

                <div *ngIf="item.item_scales_info && item.item_scales_info.length > 0">
                  <div>
                    <div class="sub-item" *ngFor="let itemComponent of item.item_scales_info[0].components" style="margin-bottom: 1.5rem">
                      <span class="status-icon" [ngSwitch]="itemComponent.status">
                        <i *ngSwitchCase="AssignedItemStatus.ACTIVE" class="fas fa-circle" style="color:#57DD54"></i>
                        <i *ngSwitchCase="AssignedItemStatus.COMPLETED" class="fas fa-check" style="color:#C5C5C5"></i>
                        <i *ngSwitchCase="AssignedItemStatus.NOT_AVAILABLE" class="fas fa-lock" style="color:#C5C5C5"></i>
                        <i *ngSwitchCase="AssignedItemStatus.PENDING" class="fas fa-circle" style="color:#0085FF"></i>
                        <i *ngSwitchCase="AssignedItemStatus.FAILED" class="fas fa-square" style="color:rgb(204, 44, 44)"></i>
                      </span>
                      <a [routerLink]="getItemComponentRoute(item, itemComponent, true)">
                        {{itemComponent.caption}}
                      </a> 
                      ({{getItemComponentStatus(item.item_scales_info[0], itemComponent)}})
                    </div>
                  </div>
                </div>
                                

              </div>

              <div *ngIf="!isPendingClaim(item) && (item.is_paired_marking_active || item.is_paired_marking_read_only)">
                <tra slug="ie_stage"></tra> {{item.is_paired_draft_stage_completed ? 2 : 1}}
                <span *ngIf="isPendingPairedMarkingStage1(item)">
                  (<tra slug="lbl_paired_marking_stage_1_pending"></tra>)
                </span>
              </div>
              <div style="margin-bottom:0.5em">
                {{getCurrentBatchDueDate(item)}}
              </div>
              <!-- && !isNoneRemaining(item) -->
              <div *ngIf="getNextComponent(item) && !isPendingClaim(item) && !isPendingPairedMarkingStage1(item)"> 
                <a class="button is-info is-fullwidth" 
                  [routerLink]="getNextActionRoute(item)"
                  [class.is-lock-disabled]="isCheckingBatches"
                >
                  {{getNextActionCaption(item)}} 
                  <span *ngIf="isCheckingBatches">&nbsp;(...)</span>
                </a>
              </div>

              <div *ngIf="isFailedQualifying(item)" class="notification is-warning">
                <tra-md slug="txt_alrt_qt_complete_unsucc_final" [props]="{threshhold_str: renderQtCutScore()}"></tra-md>
              </div>
              <div *ngIf="false">
                Your previously claimed batches have expired.
              </div>
              <pre *ngIf="false">
                isPendingClaim(item) = {{isPendingClaim(item)}} 
                !isFailedScoring(item) = {{!isFailedScoring(item)}}
                !isNoneRemaining(item) = {{!isNoneRemaining(item)}}  -- todo: control with whitelabel, EQAO only
                !isOtherBatchOpened()= {{!isOtherBatchOpened()}} -- todo: control with whitelabel, EQAO only
                isPrimaryScoringAvailable(item) = {{isPrimaryScoringAvailable(item)}} -- todo: this probably should not be applied 
              </pre>
              <div *ngIf="isPendingClaim(item) && !isFailedScoring(item) && isPrimaryScoringAvailable(item)">
                <div style="margin-top:0.5em;" *ngIf="!isHideDropdown()">
                  <strong> <tra slug="txt_max_batches"></tra> </strong>
                  <!-- <strong> <tra slug="txt_select_batches_bel"></tra> </strong> -->
                  <div><tra slug="txt_batch_exp_pre"></tra> ({{tomorrow()}} <tra slug="txt_batch_exp_if_now"></tra>).</div>
                  <div style="margin-top: 0.5em"><tra slug="txt_select_batches_bel"></tra></div>
                </div>
                <div *ngIf="isHideDropdown()">
                  <div>
                    <button 
                      (click)="claimBatches(item, 1)" 
                      class="button  is-fullwidth"
                      [disabled]="isClaiming || item.is_paired_marking_read_only"
                    >
                      <tra slug="lbl_claim_batches"></tra>
                    </button>
                  </div>
                </div>
                <div *ngIf="!isHideDropdown()">
                  <div class="select is-fullwidth">
                    <select [(ngModel)]="item.__desiredClaimQuantity">
                      <option></option>
                      <option *ngFor="let option of getClaimIterator(item.item_scales_info[0].maxBatchNumClaim, item.item_scales_info[0].numBatchesAvail)" [value]="option.value">
                        {{option.value}} {{lang.tra('lbl_batches')}}
                      </option>
                    </select>
                  </div>
                  <div>
                    <button 
                      (click)="claimBatches(item, item.__desiredClaimQuantity)" 
                      class="button  is-fullwidth"
                      [disabled]="isClaiming || item.is_paired_marking_read_only"
                    >
                      <tra slug="lbl_claim_batches"></tra>
                    </button>
                  </div>
                </div>

              </div>
            </div>
          </div>

          <div *ngIf="isSummaryView" class="table-container">
            <table class="table is-bordered is-hoverable summary-table">
              <tr>
                <th colspan="7" style="background-color: transparent; border: none;">
                  <div class="is-pulled-right">
                    <button 
                      [disabled]="!isAnyClaimBatchSelected() || isClaiming"
                      class="button is-fullwidth is-warning"
                      (click)="claimBatchesForAll()"
                    ><tra slug="lbl_claim_batches_all_selected"></tra></button>
                  </div>
                </th>
              </tr>
              <tr>
                <th>
                  <input
                  type="checkbox" 
                  [(ngModel)]="isSummaryViewSelectAll"
                  [disabled]="isClaiming"
                  (change)="onToggleSelectAll()"
                  >
                </th>
                <th><tra slug="lbl_assigned_item"></tra></th>
                <th><tra slug="tra_times"></tra></th>
                <th><tra slug="lbl_batch_status_lc"></tra></th>
                <th><tra slug="mrkg_refresh"></tra></th>
                <th><tra slug="lbl_avail_unf_claims"></tra></th>
                <th><tra slug="ie_reviewer_action"></tra></th>
              </tr>
              <tr *ngFor="let item of assignedItems">
                <td>
                  <input *ngIf="isPendingClaim(item) && !isFailedScoring(item) && isPrimaryScoringAvailable(item)"
                   type="checkbox" 
                   [(ngModel)]="item.isSelected"
                   (change)="onItemSelectionChange(item)"
                   [disabled]="(!item.isSelected && isMaxItemsSelected()) || isClaiming"
                  >
                </td>
                <td>
                  <div><b>{{item.name}}</b></div>
                  <div *ngIf="item.mw_short_differ_name" style="font-size: 0.6em; opacity:0.8;">
                    {{item.mw_short_differ_name}}
                  </div>
                </td>
                <td>
                  {{renderWindowDate(item.date_window_start)}}
                  <tra slug="snippet_to_de"></tra>
                  {{renderWindowDate(item.date_window_end)}}
                </td>
                <td>
                  <ng-container *ngIf="item.item_scales_info && item.item_scales_info.length > 0">
                    <ng-container class="sub-item" *ngFor="let itemComponent of item.item_scales_info[0].components" style="margin-bottom: 1.5rem">
                      <div *ngIf="itemComponent.componentType == AssignedItemComponentType.SCORING">
                        <span class="status-icon" [ngSwitch]="itemComponent.status">
                          <i *ngSwitchCase="AssignedItemStatus.ACTIVE" class="fas fa-circle" style="color:#57DD54"></i>
                          <i *ngSwitchCase="AssignedItemStatus.COMPLETED" class="fas fa-check" style="color:#C5C5C5"></i>
                          <i *ngSwitchCase="AssignedItemStatus.NOT_AVAILABLE" class="fas fa-lock" style="color:#C5C5C5"></i>
                          <i *ngSwitchCase="AssignedItemStatus.PENDING" class="fas fa-circle" style="color:#0085FF"></i>
                          <i *ngSwitchCase="AssignedItemStatus.FAILED" class="fas fa-square" style="color:rgb(204, 44, 44)"></i>
                        </span>
                        {{getItemComponentStatus(item.item_scales_info[0], itemComponent)}}
                      </div>
                    </ng-container>
                  </ng-container>
                  <div>
                    {{getCurrentBatchDueDate(item)}}
                  </div>
                </td>
                <td>
                  <button *ngIf="!isRecomputingBatches(item)" class="button is-small is-fullwidth" (click)="recomputeBatchesAvailable(item)">
                    <tra slug="mrkg_refresh"></tra>
                  </button>
                  <span *ngIf="isRecomputingBatches(item)">...</span>
                </td>
                <td>
                  <a [routerLink]="getAvailableClaimsRoute(item)">
                    <button *ngIf="hasUNF(item)" class="button is-small is-fullwidth" [disabled]="item.is_paired_marking_read_only">
                      <tra slug="lbl_avail_unf_claims"></tra>
                    </button>
                  </a>
                </td>
                <td>
                  <div *ngIf="isPendingClaim(item) && !isFailedScoring(item) && isPrimaryScoringAvailable(item)">
                    <button 
                      (click)="claimBatches(item, 1)" 
                      class="button is-small is-fullwidth is-info is-light"
                      [disabled]="isClaiming || item.is_paired_marking_read_only"
                    >
                      <tra slug="lbl_claim_batches"></tra>
                    </button>
                  </div>
                  <div *ngIf="getNextComponent(item) && !isPendingClaim(item) "> 
                    <a class="button is-small is-info is-fullwidth" 
                      [routerLink]="getNextActionRoute(item)"
                      [class.is-lock-disabled]="isCheckingBatches"
                    >
                      {{getNextActionCaption(item)}} 
                      <span *ngIf="isCheckingBatches">&nbsp;(...)</span>
                    </a>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <!-- <a href="/#/en/mrkg-mrkr/marking/2/5/1" style="cursor: default;">
            <img src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Screen Shot 2021-01-11 at 7.01.52 AM/*************/Screen Shot 2021-01-11 at 7.01.52 AM.png">
          </a> -->
      </div>
    </div>
  </div>
  <footer [hasLinks]="false"  techSupportDataKey="SCORING_SUPPORT"></footer>
</div>

<!-- <chat-box accountType="marker"></chat-box> -->