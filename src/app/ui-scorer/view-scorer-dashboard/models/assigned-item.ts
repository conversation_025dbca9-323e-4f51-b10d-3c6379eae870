export interface IAssignedItem {
  id: number,
  name: string,
  components: IAssignedItemComponent[],
  claimedBatches?: Array<{
    id: number,
    created_on: string,
    is_marked: number,
    marking_window_item_id: number,
  }>,
  isAvailable:boolean,
  batch_size?: number,
  num_responses_scored?: number,
  num_batches_claimed_total?: number,
  num_validity_exact?: number,
  num_validity_total?: number,
  num_validity_adj?: number,
  num_validity_over?: number,
  num_validity_under?: number,
  isRollValCurr?: boolean,
  is_non_scored_profile: number,
  cache_roll_val_exact?: any,
  cache_roll_val_adj?: any,
  item_id?: number,
  mw_short_differ_name?: string,
  mw_is_score_profile_single_focus?: boolean,
  mw_is_scorer_summary_view_allowed?: boolean,
  allow_score_clear: boolean,
  mw_is_group_marking?: boolean,
  allow_batch_next_navigate?: boolean,
  is_paired_marking_active?: boolean,
  is_paired_marking_read_only?: boolean,
  is_paired_draft_completed?: boolean,
  is_paired_draft_stage_completed?: boolean,
  marking_pair_id?: number,
  window_id: number,
  item_scales_info?: any[],
  scale_slug?: string,
  item_slug?: string
  sync_batches_to_wmi_id?: number,
  group_to_mwi_id?: number,
  score_profile_group_id?: number,
  score_profile_group_prefix?: string,
  score_profile_group_name?: string,
  dateTimeAssigned: string,
  numBatchesAvail: number, // int
  numBatchesClaimed: number, // int
  numBatchesScored: number,
  dateTimeBatchesDue?: string,
  maxBatchNumClaim?: number,
  swum_marker_number?:string, 
  swum_max_read_level?:string,
  elevated_role_type?:string,
  __desiredClaimQuantity?: number, // int
  __isRecomputingBatches?:boolean,
  flags?: string[],
  mw_is_not_open?: boolean,
  mw_is_closed: boolean,
  mw_is_scoring_disabled: boolean,
  isSelected?: boolean;
}
export interface IAssignedItemComponent {
  id: number,
  componentType: AssignedItemComponentType,
  caption: string,
  status: AssignedItemStatus,
  allowRevisit?:boolean,
  isPassFail?:boolean,
  stats?: {
      caption:string,
  }[]
}
export enum AssignedItemStatus {
    PENDING = 'PENDING',
    NOT_AVAILABLE = 'NOT_AVAILABLE',
    ACTIVE = 'ACTIVE',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
}
export enum AssignedItemComponentType {
    TRAINING_MATERIALS = 'TRAINING_MATERIALS',
    PRACTICE = 'PRACTICE',
    QUALIFYING = 'QUALIFYING',
    SCORING = 'SCORING',
}
export const getTrainingTypeCaption = (trainingType:AssignedItemComponentType) => {
  switch (trainingType){
    case AssignedItemComponentType.TRAINING_MATERIALS: return 'Training Materials';
    case AssignedItemComponentType.PRACTICE: return 'Practice Test';
    case AssignedItemComponentType.QUALIFYING:  return 'Qualifying Test';
  }
}
