import { Injectable } from '@angular/core';
import { AuthService, IUserInfo } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { LangService } from '../core/lang.service';
import { BehaviorSubject } from 'rxjs';
import { IAssignedItem, AssignedItemStatus, IAssignedItemComponent } from './view-scorer-dashboard/models/assigned-item';
import { LoginGuardService } from '../api/login-guard.service';
import { WhitelabelService } from '../domain/whitelabel.service';
import { DOMAIN_LOCK_MESSAGE } from '../api/constants/access-controls';


export interface IScorerSummary {
  assignedItems: IAssignedItem[],
  scorerDashboardDocs: IDashboardDoc[]
}
export interface IDashboardDoc {
  title: any,
  link_slug: string
}

@Injectable({
  providedIn: 'root'
})
export class MyScorerTasksService {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
    private loginGuard: LoginGuardService,
    private whitelabelService: WhitelabelService
  ) { 
    this.auth.user().subscribe(this.updateUserInfo);
    this.sub().subscribe(this.onInfoUpdate);
  }

  private _info: IScorerSummary;
  private _userInfo: IUserInfo;
  private info: BehaviorSubject<IScorerSummary> = new BehaviorSubject(null);
  private roleFailed: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private hasAttestation:boolean = false;
  private _assignedItemsByQid: {
    assignedItemsMap: Map<number, IAssignedItem[]>;
    assignedItemsList: any[]
  };

  private onInfoUpdate = (v) => {
    this._info = v;

    if(v){
      this.loadAssignedItemsByQid()
    }
  }
    
  public  sub = () => this.info;
  public  fail = () => this.roleFailed;

  private updateUserInfo = (userInfo:IUserInfo) => {
    // console.log('updateUserInfo', userInfo)
    if (window.location.host === "localhost:4200"){
      this.hasAttestation = true; 
    }
    if (userInfo){
      this._userInfo = userInfo;
      this.confirmAttestation()
        .then(()=>{
          this.reloadScorerInfo();
        })
    }
    else{
      this.info.next(null);
    }
  }

  getAssignedItems(){
    // return SAMPLE_ASSIGNED_ITEMS;
    return this._info.assignedItems;
  }

  getAssignedItemsByQuestionId() {
    return this._assignedItemsByQid;
  }

  async confirmAttestation(){
    return new Promise<void>((resolve, reject) => {
      if (this.hasAttestation){
        resolve();
      }
      else if (!this.whitelabelService.isABED()) {
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('txt_scor_attest'),
          btnProceedConfig: {
            caption: this.lang.tra('btn_i_accept')
          },
          btnCancelConfig: {
            hide: true
          },
          confirm: ()=> {
            this.attest().then( () => {
              resolve()
            })
          },
        })
      } else {
        resolve()
      }
    })
  }
  private async attest(){
    await this.auth.apiPatch('/public/scor-scor/attest', this._userInfo.uid, {})
    this.hasAttestation = true;
  }

  claimBatch(window_item_id:number, num_batch_claim:number){
    return this.auth.apiCreate('/public/scor-scor/batches/claim', {window_item_id, num_batch_claim})
  }

  scorerUid: number;
  setUid(uid: number) {
    this.scorerUid = uid;
  }

  isRangeFinderView = false;
  setIsRangeFinderView(val: boolean){
    this.isRangeFinderView = val;
  }

  /**
   * Get/reload the scorer summary information from the API.
   * Provides a scorer UID parameter if applicable. If not, API searches for the current UID by default.
   */
  reloadScorerInfo(){
    const params = this.scorerUid ? {
      query: {
        scorer_uid: this.scorerUid
      }
    } : {};

    this.auth
      .apiFind(
        this.routes.SCOR_SCOR_SUMMARY, 
        params
      )
      .catch(e => {
        const {message, data} = e;
        if (e.message == 'DOMAIN_LOCKED'){
          this.loginGuard.quickPopup(DOMAIN_LOCK_MESSAGE + data.lockedDomain);
        }
        // Ignore errors in range finder view
        else if (!this.isRangeFinderView) {
          this.roleFailed.next(true);
          this.info.next(null);
          this.loginGuard.quickPopup('You do not have access to any items to mark.');
        }
      })
      .then( (summary:IScorerSummary) => {
        this.roleFailed.next(false);
        this.info.next( summary );
      })
      .catch( e =>{
        if(e.message == "MISSING_MWI_ID"){
          this.loginGuard.quickPopup(this.lang.tra("mrkg_no_marking_session"))
        }
        this.roleFailed.next(true);
        this.info.next(null);
      })
  }

  qualAttemptGraces:Map<number, number> = new Map()
  useUpQualAttempt(itemComponent:IAssignedItemComponent){
    let numGraces = this.getQualAttempts(itemComponent);
    this.qualAttemptGraces.set(itemComponent.id, numGraces+1);
  }
  getQualAttempts(itemComponent:IAssignedItemComponent){
    if (this.qualAttemptGraces.has(itemComponent.id)){
      return this.qualAttemptGraces.get(itemComponent.id);
    }
    return 0;
  }

  updateItemComponentStatus(itemComponent:IAssignedItemComponent, newStatus:AssignedItemStatus){
    // console.log('itemComponent', itemComponent)
    return this.auth
      .apiPatch(
        this.routes.SCOR_SCOR_SUMMARY, 
        itemComponent.id,
        { 
          target: 'status',
          newStatus 
        }
      )
      .then( (summary:IScorerSummary) => {
        itemComponent.status = newStatus;
      })
      .catch( e =>{
        alert('network error, could not update status')
      })
  }

  loadAssignedItemsByQid = () => {
    // Create a map of qId to assignedWindowItems
    const assignedItemsMap: Map<number, IAssignedItem[]> = new Map();
    const markingWindowItemsMap: Map<number, IAssignedItem[]> = new Map();
    const itemIdMap: Map<number, number> = new Map();
    this._info.assignedItems.forEach( assignedItem => {
      let {id, item_id} = assignedItem;
      itemIdMap.set(id, item_id);
    })
    this._info.assignedItems.forEach( assignedItem => {
      let {id, item_id, group_to_mwi_id} = assignedItem;
      if (group_to_mwi_id && id !== group_to_mwi_id) {
        item_id = itemIdMap.get(group_to_mwi_id);
      }
      if(assignedItemsMap.has(item_id)){
        assignedItemsMap.get(item_id).push(assignedItem);
      } else {
        assignedItemsMap.set(item_id, [assignedItem])
      }
      if(markingWindowItemsMap.has(group_to_mwi_id)){
        markingWindowItemsMap.get(group_to_mwi_id).push(assignedItem);
      } else {
        markingWindowItemsMap.set(group_to_mwi_id, [assignedItem])
      }
    });

    const assignedItems = [];
    markingWindowItemsMap.forEach((items) => {
      
      const item_scales = items.map(i => i.scale_slug).filter(v => v != null);

      const firstItemScale = items[0];
      const id = firstItemScale.sync_batches_to_wmi_id;
      const num_available_scoring_batches = Math.min(firstItemScale.numBatchesAvail, firstItemScale.maxBatchNumClaim)
      const date_time_assigned = firstItemScale.dateTimeAssigned
      const date_window_end = firstItemScale['dateWindowEnd']
      const date_window_start = firstItemScale['dateWindowStart']
      const {
        name,
        item_slug, // item_label
        components,
        claimedBatches,
        numBatchesClaimed,
        sync_batches_to_wmi_id,
        group_to_mwi_id,
        score_profile_group_id,
        score_profile_group_prefix,
        score_profile_group_name,
        maxBatchNumClaim,
        numBatchesAvail,
        mw_short_differ_name,
        mw_is_score_profile_single_focus,
        mw_is_scorer_summary_view_allowed,
        allow_score_clear,
        mw_is_group_marking,
        allow_batch_next_navigate,
        is_paired_marking_active,
        is_paired_marking_read_only,
        is_paired_draft_completed,
        is_paired_draft_stage_completed,
        marking_pair_id,
        window_id,
        elevated_role_type,
        flags,
        mw_is_not_open,
        mw_is_closed,
        mw_is_scoring_disabled
      } = firstItemScale

      const commonItemInfo = {
        id,
        window_id,
        item_id: firstItemScale.item_id,
        item_scales,
        item_slug,
        name,
        num_available_scoring_batches,
        date_time_assigned,
        date_window_end,
        date_window_start,
        elevated_role_type,
        components,
        claimedBatches,
        numBatchesClaimed,
        sync_batches_to_wmi_id,
        group_to_mwi_id,
        mw_short_differ_name,
        score_profile_group_id,
        score_profile_group_prefix,
        mw_is_score_profile_single_focus,
        mw_is_scorer_summary_view_allowed,
        allow_score_clear,
        mw_is_group_marking,
        allow_batch_next_navigate,
        is_paired_marking_active,
        is_paired_marking_read_only,
        is_paired_draft_completed,
        is_paired_draft_stage_completed,
        marking_pair_id,
        score_profile_group_name,
        maxBatchNumClaim,
        numBatchesAvail,
        flags,
        mw_is_not_open,
        mw_is_closed,
        mw_is_scoring_disabled
      } 
      
      const item = {
        ...commonItemInfo,
        item_scales_info : items
      }

      assignedItems.push(item)
    });

    this._assignedItemsByQid = {
      assignedItemsMap,
      assignedItemsList: assignedItems
    }
  }


}
