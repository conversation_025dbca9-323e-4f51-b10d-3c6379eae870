<div class="panel-score-action">
  <div *ngIf="getComment() && getComment().length > 0" class="top-flag-message">
    <b>Message from Scoring Leader</b> 
    <br>
    {{getComment()}}
  </div>
  <div class="score-nav-container">
    <div class="nav-progress">
      {{batchResponseIndex+1}} <tra slug="of_enum"></tra> {{numBatchResponses}}
    </div>
    <div class="nav-buttons">

      <!-- Prev. Response button -->
      <button 
        class="button is-small"
        (click)="navPrevResponse()"
        [disabled]="!isPrevRespNav">
        <tra slug="btn_prev_resp"></tra>
      </button>

      <!-- Next Response / Validate button -->
      <button 
        *ngIf="!isLastResponse"
        class="button is-small"
        [class.is-success]="isShowValidBtn"
        (click)="navNextResponse()"
        [disabled]="isNextRespDisabled()"
        [ngSwitch]="!!isShowValidBtn">
          <span *ngSwitchCase="false"><tra slug="btn_next_resp"></tra></span>
          <span *ngSwitchCase="true"><tra slug="btn_validate"></tra></span>
      </button>

      <button 
        *ngIf="isScoreProfileForceFocus && isLastResponse && isFocusGroupMarked && !isLastProfileGroup()"
        class="button is-small"
        (click)="changeProfileFocus.emit(focusScoreProfileGroupIndex + 1)"
      >
        <tra slug="btn_next_part"></tra>
      </button>

      <!-- Finish Button -->
      <button 
        *ngIf="isLastResponse"
        class="button is-small"
        [disabled]="isFinishDisabled() || isBatchCombinationInvalid"
        (click)="attemptBatchComplete.emit()">
          <tra slug="btn_batch_finish"></tra>
      </button>
    </div>
  </div>

  <div class="score-assign-container">

    <!-- Scales -->
    <ng-container *wlCtx="'IS_SCOR_SCALES_HELP'">
      <div *ngIf="hasQuestionScales()" class="score-header" style="display:flex; flex-direction: row; justify-content: space-between">
        <button 
          class="scale-buttons" 
          (click)="showScaleHelp()">
            <tra slug="mrkg_scales"></tra> 
            <i class="fas fa-info-circle" style="margin-left:0.5rem"></i>
        </button>
      </div>
    </ng-container>

    <div *ngIf="hasQuestionScales()" class="score-option-container">
      <div *ngFor="let group of scoreProfileGroups; let index = index"class="score-option-scale-container">
        <div class="space-between assignment-header">
          <div class="name" [class.has-text-grey-light]="isScoreProfileForceFocus && index != focusScoreProfileGroupIndex">{{group.name}}</div>
          <div *ngIf="hasTopFlagOptions(group.id)" class="general-score-options">
            <div *ngFor="let flagOption of getTopFlagOptions(group.id)" class="general-score-options">
              <button *ngIf="flagOption.is_comment_req == 1"
                class="button is-small is-flag"
                [class.is-flag]="isFlagOptionSelected(flagOption.id, group.id)"
                [disabled]="isResponseSubmLocked || !isScoreSelectionEnabled || (isScoreProfileForceFocus && index != focusScoreProfileGroupIndex) || (isPairedMarkingReadOnly && isPairedDraftStageCompleted)"
                (click)="openAssignFlagModal(flagOption.id, group.id)">
                <tra [slug]="renderOptionCaption(flagOption)"></tra>
              </button>
              <button *ngIf="flagOption.is_comment_req == 0"
                class="button is-small is-flag"
                [class.is-flag]="isFlagOptionSelected(flagOption.id, group.id)"
                [disabled]="isResponseSubmLocked || !isScoreSelectionEnabled || (isScoreProfileForceFocus && index != focusScoreProfileGroupIndex) || (isPairedMarkingReadOnly && isPairedDraftStageCompleted)"
                (click)="_assignFlag(flagOption.id, group.id)">
                <tra [slug]="renderOptionCaption(flagOption)"></tra>
              </button>
            </div>
          </div>
        </div>
        <div *ngIf="getTopFlagComment(group.id)" class="top-flag-message">
          {{getTopFlagComment(group.id)}}
        </div>
        <div *ngIf="isPairedDraftStageCompleted && getDraftTopFlags(group.id)?.length" class="notification paired-draft-scores">
          <p *ngFor="let draftTopFlag of getDraftTopFlags(group.id)">
             <span *ngIf="draftTopFlag.scorerMarkerNumber"><i>(#{{draftTopFlag.scorerMarkerNumber}}) </i></span>
             <b>{{draftTopFlag.scorerName}}: </b> 
             <span class="tag is-danger is-light"><tra [slug]="getScoreOptionCaptionById(draftTopFlag.flagId, scoreProfile.flagOptions)"></tra></span>
             <span *ngIf="getFlagDraftComment(draftTopFlag, getTopFlagOptions(group.id))">
               , "{{getFlagDraftComment(draftTopFlag, getTopFlagOptions(group.id))}}"
             </span>
            </p>
        </div>
        <!-- <div class="space-between" [style.justify-content]="'flex-end'" [style.margin]="'1em 0em'">
          <div *ngIf="hasgeneralOptions" class="general-score-options">
            <button *ngFor="let generalOption of getGeneralOptions(questionScales[0])"
            class="button is-small"
            [class.is-info]="isGeneralOptionSelected(generalOption.id, group.id)"
            [disabled]="isResponseSubmLocked || !isScoreSelectionEnabled"
            (click)="_assignScoreToScoreGroup(generalOption.id, group.id)">
            <tra [slug]="renderOptionCaption(generalOption)"></tra>
            </button>
          </div>
        </div> -->
        <div class="contents">
          <div *ngFor="let scale of questionScales">
            <div *ngIf="group.id == scale.score_profile_group_id" style="margin: 0.5em; display: flex; flex-direction: column;">
              <div
                style="display: flex; flex-direction: row; min-width: 6rem;">
                <span [class.has-text-grey-light]="isScoreProfileForceFocus && index != focusScoreProfileGroupIndex">
                  {{scale.scale_slug}}
                </span>
              </div>
              <div *ngIf="false && hasScoreOptions(scale)">
                <pre>
                     isScaleSupressed(scale): {{isScaleSupressed(scale)}}
                  or isResponseSubmLocked: {{isResponseSubmLocked}}
                  or !isScoreSelectionEnabled: {{!isScoreSelectionEnabled}}
                  or isScoringDisabled(group.id): {{isScoringDisabled(group.id)}}
                  or (isScoreProfileForceFocus && index != focusScoreProfileGroupIndex) : {{(isScoreProfileForceFocus && index != focusScoreProfileGroupIndex) }}
                  or (isPairedMarkingReadOnly && isPairedDraftStageCompleted): {{(isPairedMarkingReadOnly && isPairedDraftStageCompleted)}}
                </pre>
              </div>
              <div *ngIf="hasScoreOptions(scale)" class="score-options">
                <div 
                  *ngFor="let scoreOption of getScoreOptions(scale)" 
                  [class.is-offset]="scoreOption.is_offset"
                  class="score-option">
                  <button 
                      class="button is-fullwidth" 
                      [class.is-info]="isScoreOptionSelected(scoreOption.id, scale)"
                      [class.small-scale]="showSmallBtn(scale)"
                      [class.text-wrap]="showLongText(scoreOption)"
                      [disabled]="isScaleSupressed(scale) || isResponseSubmLocked || !isScoreSelectionEnabled || isScoringDisabled(group.id) || (isScoreProfileForceFocus && index != focusScoreProfileGroupIndex) || (isPairedMarkingReadOnly && isPairedDraftStageCompleted)"
                      (click)="_assignScore(scoreOption.id, scale, scoreOption.is_non_scored_profile == 1)">
                      <tra [slug]="renderOptionCaption(scoreOption)"></tra>
                  </button>
                </div>
              </div>
              <div *ngIf="isPairedDraftStageCompleted && getDraftScores(scale)?.length" class="notification paired-draft-scores">
                <ng-container *ngFor="let draftScore of getDraftScores(scale)">
                  <p *ngIf="draftScore.scoreOptionId">
                    <span *ngIf="draftScore.scorerMarkerNumber"><i>(#{{draftScore.scorerMarkerNumber}}) </i></span>
                    <b>{{draftScore.scorerName}}: </b> 
                    <span class="tag is-info is-light"><tra [slug]="getScoreOptionCaptionById(draftScore.scoreOptionId, getScoreOptions(scale))"></tra></span>
                  </p>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
        <button 
          *ngIf="isScoreProfileForceFocus && index < focusScoreProfileGroupIndex"
          class="button is-info is-outlined is-small is-fullwidth"
          (click)="changeProfileFocus.emit(index)"
        >
          <tra slug="btn_return_to"></tra>&nbsp;{{group.name}}
        </button>
      </div>
    </div>

    <div *ngIf="validationMessage && isResponseSubmLocked" class="notification is-warning">
      <tra-md [slug]="validationMessage"></tra-md>
    </div>
    <div *ngIf="isCombinationInvalid()" class="notification is-warning">
      <tra slug="Invalid score combination"></tra>
    </div>
    <div class="score-flag-container">

      <button 
        *ngIf="!isLastResponse"
        class="button is-success is-outlined is-small is-fullwidth"
        (click)="gotoNextResponse.emit()"
        [disabled]="isNextRespDisabled()">
          <tra slug="btn_next_resp"></tra>
      </button>
      <button 
        *ngIf="isScoreProfileForceFocus &&  isFocusGroupMarked && !isLastProfileGroup()"
        class="button is-success is-light is-small is-fullwidth"
        (click)="changeProfileFocus.emit(focusScoreProfileGroupIndex + 1)"
      >
        <tra slug="btn_next_part"></tra>
      </button>
      <button 
        *ngIf="isLastResponse"
        class="button is-success is-outlined is-small is-fullwidth"
        (click)="attemptBatchComplete.emit()"
        [disabled]="isFinishDisabled() || isBatchCombinationInvalid"
      >
          <tra slug="btn_batch_finish"></tra>
      </button>
      <div *ngIf="isFlagsAllowed" style="margin-top:6em">
        <button 
          (click)="isShowFlagOptions=!isShowFlagOptions" 
          class="button is-small is-danger is-outlined has-icon is-fullwidth">
            <span><tra slug="btn_report_issue_scor_resp"></tra></span>
            <span class="icon" [ngSwitch]="!!isShowFlagOptions">
              <i *ngSwitchCase="true" class="fas fa-caret-down"></i>
              <i *ngSwitchCase="false" class="fas fa-caret-right"></i>
            </span>
        </button>

        <div *ngIf="isShowFlagOptions" style="border:1px solid #f14668; padding:0.5em; background-color:white; width: max-content;">
          <div 
            *ngFor="let flagOption of getBottomFlagOptions()" 
            class="flag-option"
            [ngSwitch]="activeFlagId == flagOption.id">
            <button 
              class="button is-small is-white" 
              *ngSwitchCase="false"
              [disabled]="getMyCurrentFlagOption() && !isCurrentFlagOptionTop() || (isPairedMarkingReadOnly && isPairedDraftStageCompleted)"
              (click)="selectFlag(flagOption.id)">
                <tra [slug]="renderOptionCaption(flagOption)"></tra>
            </button>

            <div *ngSwitchCase="true" class="message-insertion" >
              <strong><tra [slug]="renderOptionCaption(flagOption)"></tra></strong>
              <tra [slug]="flagOption.report_msg"></tra>
              <textarea 
                  *ngIf="flagOption.is_comment_req == 1"
                  [(ngModel)]="flagMessage" 
                  [disabled]="isFlagOptionSelected(flagOption.id)" 
                  rows="2" 
                  class="textarea is-small is-fullwidth">
              </textarea>
              <div [ngSwitch]="isFlagOptionSelected(flagOption.id)">
                <div *ngSwitchCase="true" style="margin-top:0.5em; line-height:1.1em;" >
                  <tra slug="txt_notif_sent_scor"></tra>
                  <!-- <button (click)="undoReport(flagOption.id)" class="button is-small">Undo Report</button> -->
                </div>

                <ng-container *ngSwitchCase="false">
                  <button 
                    (click)="deselectFlag()" 
                    class="button is-small">
                      <tra slug="btn_cancel"></tra>
                  </button>
                  <button 
                    (click)="_assignFlag(flagOption.id)" 
                    [disabled]="flagOption.is_comment_req == 1 ? !flagMessage : false" 
                    class="button is-small">
                      <tra slug="lbl_send"></tra>
                  </button>
                </ng-container>

              </div>
            </div>
          </div>
        </div>

        <div *ngIf="isPairedDraftStageCompleted && getDraftBottomFlags()?.length" class="notification paired-draft-scores">
          <div *ngFor="let drafBottomFlag of getDraftBottomFlags()">
            <span *ngIf="drafBottomFlag.scorerMarkerNumber"><i>(#{{drafBottomFlag.scorerMarkerNumber}})</i></span>
            <b>{{drafBottomFlag.scorerName}}: </b> 
            <span class="tag is-danger is-light"><tra [slug]="getScoreOptionCaptionById(drafBottomFlag.flagId, scoreProfile.flagOptions)"></tra></span>
            <span *ngIf="getFlagDraftComment(drafBottomFlag, getBottomFlagOptions())">
              , "{{getFlagDraftComment(drafBottomFlag, getBottomFlagOptions())}}"
            </span>
          </div>
        </div>
      </div>
      
    </div>


    
  </div>
</div>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="'ASSIGN_FLAG'" style="width: 35em; ">
        <h2>Flag Message</h2>
        <tra slug="txt_scorers_flag_reason"></tra>
        <textarea 
          [(ngModel)]="flagMessage" 
          rows="2" 
          class="textarea is-fullwidth">
        </textarea>
        <modal-footer [pageModal]="pageModal"></modal-footer>
      </div>
    </div>
  </div>
</div>