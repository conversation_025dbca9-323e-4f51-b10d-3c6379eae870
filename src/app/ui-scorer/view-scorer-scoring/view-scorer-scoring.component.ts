import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { AssignedItemComponentType, getTrainingTypeCaption, IAssignedItem, IAssignedItemComponent, AssignedItemStatus } from '../view-scorer-dashboard/models/assigned-item';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { AuthService } from '../../api/auth.service';
import { Router, ActivatedRoute } from '@angular/router';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { AccountType } from '../../constants/account-types';
import { IResponseBatch, ResponseID, IResponseMarkState } from '../panel-scoring/types/response-batch';
import { ResponseBatchService, ResponseBatch } from '../response-batch.service';
import { MyScorerTasksService } from '../my-scorer-tasks.service';
import { Subscription } from 'rxjs';
import { IItemRules } from '../panel-scoring/types/item-rules';

const EVENT_PING_INTERVAL = 60*1000
@Component({
  selector: 'view-scorer-scoring',
  templateUrl: './view-scorer-scoring.component.html',
  styleUrls: ['./view-scorer-scoring.component.scss']
})
export class ViewScorerScoringComponent implements OnInit, OnDestroy {

  constructor(
    private breadcrumbsService: BreadcrumbsService,
    private auth: AuthService,
    private sidePanel: SidepanelService,
    private router:Router,
    private route:ActivatedRoute,
    private loginGuard:LoginGuardService,
    private responseBatchService: ResponseBatchService,
    private myScorerTasks: MyScorerTasksService,
    public lang:LangService,
    private routes:RoutesService,
  ) { }

  isLoaded:boolean = false;
  itemId:number;
  currentItem:IAssignedItem
  currentItemComponent: IAssignedItemComponent;
  breadcrumb:any[];
  isShowingResources:boolean;
  eventUpdateInterval;
  itemResponseBatch:ResponseBatch;
  itemResponseBatches:ResponseBatch[] = [];
  multiScaleResponseBatchesMap: Map<number, ResponseBatch> = new Map();
  itemScales: IAssignedItem[];
  currentSelectedWindowItemId: number
  isFromOBS: boolean;
  allowScoreClear: boolean = false;
  allowBatchNextNavigate: boolean = false;
  myScorerTasksSub:Subscription;
  ngOnInit(): void {
    this.sidePanel.activate();
    this.sidePanel.unexpand();
    this.loginGuard.activate();

    this.route.params.subscribe(params => {
      this.itemId = +params['itemId'];
      this.currentSelectedWindowItemId = this.itemId;
      this.initRouteView()
    });

    this.route.queryParams.subscribe((queryParams) => {
      const isFromOBS  = queryParams['isFromOBS'];
      if (isFromOBS){
        this.isFromOBS = isFromOBS;
      }
    });
  }

  isDestroyed:boolean;
  ngOnDestroy(){
    this.updateEvent(true);
    clearInterval(this.eventUpdateInterval);
    if (this.activitySub){
      this.activitySub.unsubscribe();
    }
    if(this.myScorerTasksSub){
      this.myScorerTasksSub.unsubscribe();
    }
  }


  currentEvent:{id:number, isIdle:boolean, idleCount:number};
  isIdle:boolean;
  activitySub:Subscription;
  
  private async initEventInterval(){
    // await this.updateEvent()
    // this.eventUpdateInterval = setInterval(this.updateEvent, EVENT_PING_INTERVAL);
    // this.activitySub = this.loginGuard.activity().subscribe(() => {
    //   this.isIdle = false;
    // })
  }
  private async initEvent(isIdle:boolean){
    if (this.isDestroyed){ return; }
    const hash = [this.auth.getUid(), (new Date()).valueOf(), Math.round(10000*Math.random())].join('-')
    // const newEvent = await this.auth
    //   .apiCreate('/public/scor-scor/event', {
    //     item_id: this.itemId, 
    //     is_idle: isIdle ? 1 : 0, 
    //     event_type: AssignedItemComponentType.SCORING, 
    //     hash,
    //   })
    // this.currentEvent = {id: newEvent.id, isIdle, idleCount:0};
  }
  private updateEvent = async (isLast:boolean=false) => {
    if (this.isDestroyed){ return; }
    if (this.currentEvent){
      if (this.isIdle){
        this.currentEvent.idleCount ++;
      }
      if (this.currentEvent.isIdle && !this.isIdle){
        await this.initEvent(this.isIdle);
      }
      else if (!this.currentEvent.isIdle && this.currentEvent.idleCount > 3){
        await this.initEvent(this.isIdle);
      }
      else{
        // await this.auth
        //   .apiPatch('/public/scor-scor/event', this.currentEvent.id, {})
        //   .catch(() => {
        //     this.currentEvent = null;
        //   })
      }
    }
    if (!this.currentEvent && !isLast){
      this.initEvent(this.isIdle);
    }
    this.isIdle = true;
  }
  
  backToDashboard(){
    if(this.isFromOBS){
      this.router.navigateByUrl(this.getShoppingCartRoute(this.itemId));
    }
    else{
      this.router.navigateByUrl(this.getDashboardRoute());
    }
  }

  async initRouteView(){
    this.myScorerTasksSub = this.myScorerTasks.sub().subscribe( async info => {
      if (info){
        await this.initEventInterval();
        await this.loadItemContent();
        await this.loadItemResponseBatch();
        this.isLoaded = true;
      }
    })
  }

  async loadItemContent(){
    this.myScorerTasks.getAssignedItems().forEach(item => {
      if (item.id === this.itemId){
        this.currentItem = item;
        item.components.forEach(itemComponent => {
          if (itemComponent.componentType === AssignedItemComponentType.SCORING){
            this.currentItemComponent = itemComponent;
          }
        })
      }
    })
    this.markComponentAsActive();
    this.updateBreadcrumb();
  }

  async loadItemResponseBatch(){
    const window_item_id = this.itemId;
    // find if multiscaled
    let isMultiScaled = false

    const { assignedItemsMap, assignedItemsList } = this.myScorerTasks.getAssignedItemsByQuestionId();
    const window_item_ids = []
    assignedItemsList.forEach(item => {
      if(item.sync_batches_to_wmi_id === this.itemId){
        this.itemScales = item.item_scales_info;
        item.item_scales_info.forEach( scale_info => {
          window_item_ids.push(scale_info.id)
        })
      }
    })

    const responseBatchesMap: Map<number, ResponseBatch> = new Map();

    if(window_item_ids.length > 0){
      const batchConfigRes = await this.auth.apiFind(this.routes.SCOR_SCOR_MARK, {query: { window_item_ids: window_item_ids}}).catch(e => {
        if (e.message !== 'NO_CLAIMED_BATCHES_AVAIL'){ this.loginGuard.quickPopup(e.message) }
      })
      for(const batchConfig of batchConfigRes){
        const itemResponseBatch = this.responseBatchService.initNewResponseBatch(batchConfig)
        responseBatchesMap.set(batchConfig.currentBatch.marking_window_item_id, itemResponseBatch)
        this.itemResponseBatches.push(itemResponseBatch);
      }
      
      this.multiScaleResponseBatchesMap = responseBatchesMap;
      this.itemResponseBatch = this.multiScaleResponseBatchesMap.get(this.itemId)
    }
    else{
      this.loginGuard.quickPopup("NO PARAMS, please refresh and try again.")
    }

    this.allowScoreClear = assignedItemsList.filter(item => item.sync_batches_to_wmi_id === this.itemId).every(item => item.allow_score_clear)
    
    this.allowBatchNextNavigate = assignedItemsList.filter(item => item.sync_batches_to_wmi_id === this.itemId).every(item => item.allow_batch_next_navigate)
  }


  getScoreProfiles() {
    const scoreProfiles = {
      scoreOptions: [],
      flagOptions: [],
    };
    this.multiScaleResponseBatchesMap.forEach(batch => {
      scoreProfiles.scoreOptions.push(...batch.scoreProfile.scoreOptions)
      scoreProfiles.flagOptions.push(...batch.scoreProfile.flagOptions)
    })
    return scoreProfiles;
  }

  getItemRules() {
    const rulesMap: Map<number, IItemRules[]> = new Map()
    this.multiScaleResponseBatchesMap.forEach(batch => {
      batch.scoreProfile.itemRules.forEach((rule) => {
        if(!rulesMap.has(rule.group_to_rule_id)) {
          rulesMap.set(rule.group_to_rule_id, []);
        }
  
        rulesMap.get(rule.group_to_rule_id).push(rule);
      })
    });
    
    const debugRulesMap = Array.from(rulesMap.values());

    return rulesMap
  }

  getResponses(){
    return this.itemResponseBatch.currentResponsBatch.responses;
  }
  getResponseIndexById(responseId:ResponseID){
    return this.getResponses().indexOf(responseId);
  }
  getNextResponseById(responseId:ResponseID){
    const responseIndex = this.getResponseIndexById(responseId);
    return this.getResponses()[responseIndex+1];
  }
  unlockRepsonseById(responseId:ResponseID){
    this.itemResponseBatch.currentResponsBatch.responseLockStates.set(responseId, false);
  }

  onScoreAssigned($event:{responseId:ResponseID, scoreOptionId:number}){
    const {responseId, scoreOptionId} = $event;
    if (scoreOptionId){
      const nextResponseIndex = this.getNextResponseById(responseId);
      this.unlockRepsonseById(nextResponseIndex);
    }
  }

  onBatchBlocked(errorMsg:string){
    let caption = 'txt_alert_batch_expired';
    switch (errorMsg){
      case 'LOW_VALIDITY': caption = 'txt_alert_low_valid_block'; break;
      case 'LOW_VALIDITY_FINAL': caption = 'txt_alert_low_valid_final_block'; break;
      case 'BATCH_EXPIRED': caption = 'txt_alert_batch_expir'; break;
      case 'SCORING_NOT_OPEN': caption = 'Scoring is not yet open.'; break;
      case 'SCORING_CLOSED': caption = 'Scoring has closed.'; break;
      case 'SCORING_DISABLED': caption = 'Scoring has closed.'; break;
      case 'SCORING_PAUSED': caption = 'Scoring has been paused.'; break;
      case 'ACCESS_REVOKED': caption = 'txt_alert_batch_expired'; break;
      default: caption = 'Your access to scoring has been disabled.'; break;
    }
    this.loginGuard.confirmationReqActivate({
      caption,
      btnCancelConfig: {
        hide: true
      },
      btnProceedConfig: {
        caption: this.lang.tra('lbl_back_to_dash')
      },
      confirm: () => this.backToDashboard()
    })
  }

  markComponentAsActive(){
    if (this.currentItemComponent){
      if (this.currentItemComponent.status === AssignedItemStatus.PENDING){
        return this.myScorerTasks.updateItemComponentStatus(this.currentItemComponent, AssignedItemStatus.ACTIVE)
      }
    }
  }

  getBaseRoute = () => `/${this.lang.c()}/scor-scor` 
  getDashboardRoute = () => `${this.getBaseRoute()}/dashboard`;
  getShoppingCartRoute = (mwi_id) => `${this.getBaseRoute()}/available-claims/${mwi_id}`;
  updateBreadcrumb(){
    // getTrainingTypeCaption(this.trainingType);
    let currentTabName = 'Training';
    if (this.currentItem && this.currentItemComponent){
      currentTabName = this.lang.tra('lbl_scoring')+' / ' + this.lang.tra(this.currentItem.name) ;
    }
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_dashbord_scor'), this.getDashboardRoute()),
      this.breadcrumbsService._CURRENT( currentTabName, this.router.url),
    ];
  }

  computeValidity(){
    // txt_alert_low_valid_block
    // txt_alert_low_valid_final_block
  }

  // Multi-scale

  getQuestionsScaleInfo() {
    if(this.itemScales) return this.itemScales;
    return [];
  }

}
