.panel-score-content {
    padding:0.5em;
    height: 100%;
    
    max-width: 42em; 
    &.is-unconstrained {
        max-width: unset; 
    }
    &.is-no-height-constraint {
        height: unset;
    }

    display:flex;
    flex-direction: column;
    justify-content: stretch;
    .response-info-container {
        flex-grow: 0;
        display:flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-left: 0.5em;
        .info-strip-right {
            display:flex;
            flex-direction: row;
            align-items: center;    
        }
    }
    .student-response-container {
        flex-grow: 1;
        margin-top:1em;
        background-color:#fff;
        padding:0.5em;
        // width:42em;
        flex-grow:1;
        overflow-y: scroll;

        $light-padding: 0.5em;
        .response-text-container {
            border: 1px solid #f1f1;
            overflow-x: auto;
            margin-bottom:1em;
        }
        .response-text-book-end {
            background-color:#ccc;
            color:#888;
            padding:$light-padding;
            text-align:center;
            font-size:0.8;
            display:flex;
            flex-direction: row;
            justify-content: space-between;
        }
        .response-text-content {
            padding:$light-padding;    
        }
    }

    .is-full-width {
      width: 100%;
    }

    .is-height-limit {
      //height: 100%;
    }
}
.notepad-container {
  position:fixed;
  top:30px;
  left:100px;
  padding:1.5em;
  padding-top:0.5em;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.5);
  width:32em;
  max-width:40vw;
  max-height:80vh;
  font-weight:600;
  background-color: #333;
  color:#fff;
  textarea.textarea {
      font-size: 1em;
  }
  z-index:11; 
  .notepad-close {
      position: absolute;
      top:0.4em;
      right:0.4em;
      cursor: pointer;
      color: #fff;
  }
}


.question-content {
  position: relative;
  overflow-y: scroll;
  &.is-no-scroll {
    overflow: unset;
  }
  &__overlay {
    position: absolute;
    width: 100%;
    z-index: 1;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    @media only screen and (max-width: 1200px) {
        // width: 100%;
    }
}
&__faux-overlay {
    position: absolute;
    height: 0;
    width: 100%;
    background: grey;
    opacity: 0.1;
    z-index: 1;
    pointer-events: none;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    @media only screen and (max-width: 1200px) {
        //width: 100%;
    }
}
}

.question-content__overlay {
  .draw-overlay-close {
      position: absolute;
      top: 0em;
      right: 0.1em;
      z-index: 1000;
      cursor: pointer;
  }
  &.deactivated {
      .draw-overlay-close {
          display:none;
      }
  }
}
.deactivated {
  background: transparent;
  pointer-events: none;
}

.navigation-panel-container {
  position: fixed;
  bottom: 0em;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  z-index: 100;

  .custom-nav-button-container {
      margin-bottom: 1.5em;
      margin-right: 4em;
      display: flex;
  }
}

.drawing-panel {
  display: flex;
  gap: 0.5em;
}

.color-palette {
  display: flex;
  gap: 1em;
  align-items: center;
  justify-content: center;
}

.annotation-tool-close {
  cursor: pointer;
  height: 3.25em; 
  margin-right:1em; 
  background: lightgray;
  border-top-left-radius: 1.3em; 
  border-top-right-radius: 1.3em; 
  padding:0 1em; 
  border: none;
  @media only screen and (max-width: 770px), screen and(max-height :650px){
      font-size: 1em;
  }
}

.color-square {
  width: 1.4em;
  height: 1.4em;
  border: 1px solid rgb(46, 46, 46);
  border-radius: 100%;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  &:hover{
      opacity:0.8;
  }
}

.tool-button-container{
  button.is-active{
    border: none;
    background-color: lightgrey;
  }
}

.basic-response-scan{
  margin:0 auto;
  display: block;
  max-width: none;
  overflow: hidden;
}