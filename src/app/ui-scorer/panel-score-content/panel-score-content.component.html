<div 
    class="panel-score-content" 
    [class.is-unconstrained]="(!isLeaderView || isFocusedOnStudentResponse || isExpandView || isScanMode)"
    [class.is-unconstrained-question-width]="isScanMode"
    [class.is-no-height-constraint]="isNoInnerScroll"
>
    <div class="response-info-container">

        <div *ngIf="!isLeaderView && !isFocusedOnStudentResponse && !isExpandView">
            <div>
                <span class="tag is-success">{{getMyMarkerNumber()}}</span>
                &nbsp;<tra slug="txt_is_yo_mkr_num"></tra>
            </div>
            <div *ngIf="prevReads?.length">
                <b><tra slug="llb_prev_reads"></tra></b>
                <ng-container *ngFor="let prevRead of prevReads">
                    <span class="tag is-info" *ngIf="(prevRead.is_invalid != 1) || isShowingInvalidatedReads" >
                        {{prevRead.read_id}} | <tra slug="lbl_marker"></tra> {{prevRead.marker_number}}
                    </span>
                </ng-container>
                <span style="margin-left:1.5em; font-size:0.7em;">
                    <label class="checkbox">
                        <input type="checkbox" [(ngModel)]="isShowingInvalidatedReads">
                        <span> <tra slug="lbl_show_invalidated"></tra> </span>
                    </label>
                </span>
            </div>
            <!-- <div>
                <b>Student Test Label:</b> {{getCurrentStudentLabel()}}
            </div> -->
            <div *ngIf="prevReadsAT?.length">
                <b><tra slug="llb_prev_reads_at"></tra></b>
                <ng-container *ngFor="let prevRead of prevReadsAT">
                    <span class="tag is-info" *ngIf="(prevRead.is_invalid != 1) || isShowingInvalidatedReadsAT" >
                        {{prevRead.read_id}} | <tra slug="lbl_marker"></tra> {{prevRead.marker_number}}
                    </span>
                </ng-container>
                <span style="margin-left:1.5em; font-size:0.7em;">
                    <label class="checkbox">
                        <input type="checkbox" [(ngModel)]="isShowingInvalidatedReadsAT">
                        <span> <tra slug="lbl_show_invalidated"></tra> </span>
                    </label>
                </span>
            </div>
        </div>
        
        <ng-container *wlCtx="'IS_SCORE_SCORING_NUM'">
            <div *ngIf="!rawResponseId">
                <tra slug="lbl_scoring"></tra> #{{responseId}}
            </div>
            <div *ngIf="rawResponseId">
                <tra slug="lbl_osslt_response"></tra> #{{rawResponseId}}
            </div>
        </ng-container>

        <div *ngIf="!isLeaderView && isScorerAnnotationAllowed">
          <div class="buttons tool-button-container">
            <button class="button is-rounded" (click)="setFreehand()" [class.is-active]="isShowOverlay && !showDeactivatedMode && toolToggles.showFreehand" [tooltip]="lang.tra(getFreehandSlug())" [attr.aria-label]="lang.tra(getFreehandSlug())">
              <i class="fas fa-pen-nib toolbar-icon-icon" aria-hidden="true"></i>
            </button>
            <button class="button is-rounded" (click)="setLine()" [class.is-active]="isShowOverlay && !showDeactivatedMode && toolToggles.showLine" [tooltip]="lang.tra(getDrawingToolsSlug())" [attr.aria-label]="lang.tra(getDrawingToolsSlug())">
              <i class="fas fa-edit toolbar-icon-icon" aria-hidden="true"></i>
            </button>
            <button class="button is-rounded" (click)="setHighlighter()" [class.is-active]="isShowOverlay && !showDeactivatedMode && toolToggles.showHighlight" [tooltip]="lang.tra(getHighlighterSlug())" [attr.aria-label]="lang.tra(getHighlighterSlug())">
              <i class="fas fa-highlighter toolbar-icon-icon" aria-hidden="true"></i>
            </button>
            <button class="button is-rounded" (click)="setEraser()" [class.is-active]="isShowOverlay && !showDeactivatedMode && toolToggles.showEraser" [tooltip]="lang.tra(getEraserSlug())" [attr.aria-label]="lang.tra(getEraserSlug())">
              <i class="fas fa-eraser toolbar-icon-icon" aria-hidden="true"></i>
            </button>
            <button class="button is-rounded" (click)="toggleNotepad()" [class.is-active]="isNotepadEnabled" [tooltip]="lang.tra(getNotepadSlug())" [attr.aria-label]="lang.tra(getNotepadSlug())">
              <i class="fas fa-sticky-note toolbar-icon-icon" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      
        <div class="columns" [class.is-full-width]="isLeaderView">
          <div class="column info-strip-right">
              <span style="margin-right:0.5em;" *ngIf="!isViewQuestionsHidden()">
                  (<a (click)="toggleNonPrintView()" [ngSwitch]="!!isForcedPrintMode">
                      <span *ngSwitchCase="false"><tra slug="lbl_osslt_scor_view_quest"></tra></span>
                      <span *ngSwitchCase="true"><tra slug="lbl_osslt_scor_hide_quest"></tra></span>
                  </a>)
              </span>
              <!-- <span style="margin-right:0.5em;">
                  (<a (click)="togglePlanningPages()" [ngSwitch]="!!isPlanningPagesRevealed">
                      <span *ngSwitchCase="false"><tra slug="lbl_scor_view_planning_abed"></tra></span>
                      <span *ngSwitchCase="true"><tra slug="lbl_scor_hide_planning_abed"></tra></span>
                  </a>)
              </span> -->
              <button (click)="zoomOut()" class="button is-small"  [disabled]="getZoomLevel() <= minZoomLevel">
                  <i class="fas fa-search-minus"></i>
              </button>
              <button (click)="zoomIn()" class="button is-small" [disabled]="getZoomLevel() >= maxZoomLevel">
                  <i class="fas fa-search-plus"></i>
              </button>
              <button (click)="toggleStudentResponseFocus()" class="button is-small btn-focus-toggle" [class.is-info]="isFocusedOnStudentResponse">
                  Focus View
              </button>
          </div>
          <div *ngIf="showExpandViewBtn && !isFocusedOnStudentResponse" class="column is-narrow info-strip-right">
            <button class="button is-small" (click)="onToggleExpandView()">
              <span *ngIf="!isExpandView">Expand View &nbsp; <i class="fas fa-expand"></i></span>
              <span *ngIf="isExpandView">Compress View &nbsp; <i class="fas fa-compress"></i></span>
            </button>
          </div>
        </div>
    </div>
    <div 
        #questionContent 
        class="question-content" 
        [class.is-no-scroll]="isNoInnerScroll"
    > 
      <ng-container 
        *ngFor="let responseState of responseStates; let i = index" 
      > 
  
          <div class="space-between" style="margin-bottom: 1em; margin-top: 1em;">
              <strong *ngIf="responseState?.name">
                  {{responseState?.name}}
              </strong>
              <div> <!-- todo: only show if whitelabel requires it -->
                  <span class="tag is-light">
                      <tra slug="lbl_attempt_id"></tra>&nbsp;{{responseState?.test_attempt_id}}
                  </span>
              </div>
          </div>
          <div *ngIf="isScoreProfileForceFocus && i != focusScoreProfileGroupIndex">
            <mat-slide-toggle [(ngModel)]="responseState.isShown">
              <tra slug="lbl_show_response"></tra>
            </mat-slide-toggle>
          </div>
          <ng-container *ngIf="(!isScoreProfileForceFocus || i == focusScoreProfileGroupIndex || responseState?.isShown) && responseState?.is_accomm_displayed">
              <strong (click)="toggleDisplayAccommodation(responseState)">
                  <i *ngIf="responseState?.display_accommodation" class="fa fa-caret-down"></i>
                  <i *ngIf="!(responseState?.display_accommodation)" class="fa fa-caret-right"></i>
                  Student Accommodations ({{getCurrentAccommodationList(responseState?.student_accommodation).length}}):</strong>
              <div *ngIf="getCurrentAccommodationList(responseState?.student_accommodation).length == 0 && responseState?.display_accommodation">None</div>
              <ul *ngIf="responseState?.display_accommodation">
                  <ng-container *ngFor="let acc of getCurrentAccommodationList(responseState?.student_accommodation)">
                      <li>
                      <ng-container *ngIf="acc.type == 'checkbox'">
                          <input [id]="acc.accommodation_slug" [disabled]="true" type="checkbox" [(ngModel)]="acc.value" />&nbsp;
                      </ng-container>
                      <label [for]="acc.accommodation_slug">
                          <b><tra [slug]="acc.name"></tra></b>
                      </label>&nbsp;
                      <ng-container *ngIf="acc.has_extra_notes">
                          <input type="text" [disabled]="true" [(ngModel)]="acc.extra_notes_value" />
                      </ng-container>
                  </li>
                  </ng-container>
              </ul>
          </ng-container>
          
          <!-- <pre>isPlanningPagesRevealed: {{isPlanningPagesRevealed}} && {{planningPages.length}}</pre> -->
          <div 
            pan-zoom
            [isPanZoomEnabled]="isScanMode"
            [class.pan-target]="isScanMode && !isDeviceiPad()"
            class="student-response-container"
          >
              <div *ngIf="(!isScoreProfileForceFocus || i == focusScoreProfileGroupIndex || responseState?.isShown) && isPlanningPagesRevealed" >
                  <div *ngIf="planningPages.length > 0" class="response-text-container">
                      <div class="response-text-book-end">
                          <i class="fas fa-sort-down"></i>
                          <span><tra slug="lbl_abed_scor_start_of_plan"></tra></span>
                          <i class="fas fa-sort-down"></i>
                      </div>
                      <div *ngFor="let planningPage of planningPages" [style.font-size.em]="fontSize">
                          <question-runner 
                              *ngIf="planningPage.config && !isResetting"
                              [currentQuestion]="planningPage.config"
                              [questionState]="planningPage.response_raw"
                              [isSkipAnimatedEntry] = "true"
                              [isSubmitted]="!isEditable" 
                              [isPrintMode]="false"
                              [isScoringLeaderView]="true"
                          ></question-runner>
                      </div>
                      <div class="response-text-book-end">
                          <i class="fas fa-sort-up"></i>
                          <span><tra slug="lbl_abed_scor_end_of_plan"></tra></span>
                          <i class="fas fa-sort-up"></i>
                      </div>
                  </div>
                  <div *ngIf="planningPages.length == 0" class="notification">
                      <em>
                          <tra-md slug="lbl_no_plan_pages"></tra-md>
                      </em>
                  </div>
              </div>
              <!-- todo, ng switch -->
              <div  *ngIf="!isScoreProfileForceFocus || i == focusScoreProfileGroupIndex || responseState?.isShown" class="response-text-container" [class.is-dbl-spaced-print]="isRenderDblSpace()">
                  <div class="response-text-book-end" *wlCtx="'IS_SCORE_RESP_BOOKEND'">
                      <i class="fas fa-sort-down"></i>
                      <span><tra slug="lbl_osslt_scor_start_of_stu"></tra></span>
                      <i class="fas fa-sort-down"></i>
                  </div>
                  <div class="response-text-content">
                      <div *ngIf="isLeaderView && !responseState.responseScanUrl && responseState.is_paper_response" class="notification is-warning">
                        <tra slug="lbl_abed_scor_lead_paper_no_scan"></tra>
                      </div>
                      <!-- {{responseContent}} -->
                      <div *ngIf="responseState.responseScanUrl">
                          <img [style]="'width:calc(100% * ' + getZoomValue() + ');'" class="basic-response-scan" [src]="responseState.responseScanUrl">
                          <!-- <div>
                              <button class="button" (click)="isToggleWritten=!isToggleWritten">Show/Hide Online Response</button>
                          </div> -->
                      </div>
                      <div *ngIf="isToggleWritten || !responseState.responseScanUrl" [style.font-size.em]="fontSize">
                            <question-runner 
                                *ngIf="getItemDisplayForState(responseState.item_id) && responseState.response && !isResetting && isPrintModeDetermined"
                                [currentQuestion]="getItemDisplayForState(responseState.item_id)" 
                                [questionState]="responseState.response"
                                [isSkipAnimatedEntry] = "true"
                                [isSubmitted]="!isEditable" 
                                [isPrintMode]="!isEditable && isPrintMode && !isForcedPrintMode"
                                [isScoringLeaderView]="true"
                            ></question-runner>
                      </div>
                  </div>
                  <div class="response-text-book-end" *wlCtx="'IS_SCORE_RESP_BOOKEND'">
                      <i class="fas fa-sort-up"></i>
                      <span><tra slug="lbl_osslt_scor_end_of_stu"></tra></span>
                      <i class="fas fa-sort-up"></i>
                  </div>
              </div>
          </div>
        </ng-container>
      <!-- Annotation overlay -->
      <div *ngIf="isShowOverlay && isAnnotationSet" class="question-content__overlay" [ngStyle]="{'height': getQuestionContentHeight() + 'px'}" [ngClass]="{'deactivated':showDeactivatedMode}" id="draw-overlay-split">
        <div (click)="disableOverlay()" class="draw-overlay-close">
            &times;
        </div>
        <element-render-drawing  
            id="panel-score-content" 
            [parent]="getDrawingDisplayMode()" 
            [currId]="1" 
            [activatedState]="!showDeactivatedMode" 
            [useEraser]="toolToggles.showEraser" 
            [useHighlighter]="toolToggles.showHighlight"
            [highlighterColor]="highlighterColor"
            [useFreehand]="toolToggles.showFreehand"
            [useLine]="toolToggles.showLine" 
            [zoomLevel]="getZoomValue()" 
            [isZoomIn]="isZoomIn"
            [isFrameEnabled]="false"
            [isToolbarEnabled]="false"
            [drawingCtx]="drawingCtx"
            (savedCtx)="saveDrawingLocal($event)"
            [clearAllDrawings]="clearAllDrawings"
        ></element-render-drawing>
      </div>
      <div 
          class="question-content__faux-overlay" 
          [class.animateDown]="isShowOverlay && !showDeactivatedMode"
          [class.animateUp]="showDeactivatedMode"
      ></div>
    </div>

    <!-- Highlight palette and clear/close buttons -->
    <div class="navigation-panel-container"  *ngIf="isShowOverlay && !showDeactivatedMode">
      <div
          *ngIf="toolToggles.showHighlight"
          class="drawing-panel annotation-tool-close color-palette"
      >
          <div *ngFor="let color of getColors()"
              class="color-square" 
              [style.backgroundColor]="color"
              (click)="setHighlightColor(color)"
          ></div>
      </div>
      <button class="annotation-tool-close"(click)="removeAllDrawings()">
        <tra [slug]="getRemoveDrawingSlug()"></tra>
      </button>
      <button class="annotation-tool-close" (click)="disableOverlay()">
        <tra [slug]="getCloseDrawingSlug()"></tra>
      </button>
    </div>
</div>

<!-- Hovering notepad -->
<div *ngIf="isNotepadEnabled" [ngStyle]="{'font-size.em': getZoomValue()}">
  <div cdkDrag class="notepad-container" >
      <div (click)="toggleNotepad()" class="notepad-close">&times;</div>
      <div style="padding-bottom:0.5em;">
          <tra [slug]="getNotepadSlug()"></tra>
      </div>
      <textarea class="textarea" style="font-size:1em;" spellcheck="false" (mousedown)="$event.stopPropagation()"  [(ngModel)]="notepadText"></textarea>
  </div>
</div>