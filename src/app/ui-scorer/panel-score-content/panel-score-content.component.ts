import { Component, OnInit, Input, Output, SimpleChanges, OnChanges, EventEmitter, ViewChild, ElementRef, On<PERSON><PERSON>roy, HostListener} from '@angular/core';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthService } from '../../api/auth.service';
import { LangService } from '../../core/lang.service';
import { IQuestionConfig, ElementType } from '../../ui-testrunner/models';
import { MyScorerTasksService } from '../my-scorer-tasks.service';
import { IPrevReads, ResponseBatch } from '../response-batch.service';
import { processItemConfigLangLink } from './util/lang-link';
import { ZoomService } from 'src/app/ui-testrunner/zoom.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { hasPrintBlocks } from 'src/app/ui-testrunner/services/util';
import { SectionDrawingCtx } from '../../ui-testrunner/sample-questions/data/sections';
import { MIN_HILIGHT_COLORS } from '../../ui-testrunner/background-fill.service';
import { DrawDisplayMode } from '../../ui-testrunner/element-render-drawing/constants';
import { IItemScale, IScoringPolicyRaw } from 'src/app/ui-scoring-leader/view-sl-response-scores/model/types';

interface IstudentAccommodation {
  uid: number,
  accommodation_slug: string,
  extra_notes_value?: string,
  id: number,
  name: string,
  system_slug: string,
  accommodation_type: string,
  accommodation_value: string | number,
  ta_id: number,
}

const SAVE_ANNOTATION_INTERVAL_MS = 30 * 1000;

@Component({
  selector: 'panel-score-content',
  templateUrl: './panel-score-content.component.html',
  styleUrls: ['./panel-score-content.component.scss']
})
export class PanelScoreContentComponent implements OnInit, OnDestroy, OnChanges {

  @Input() windowItemId:number = 16;
  @Input() responseId:number;
  @Input() rawResponseId:number;
  @Input() responseType:string;
  @Input() responseContent:any;
  @Input() isEditable:boolean;
  @Input() isLeaderView:boolean;
  @Input() isNoBatch:boolean;
  @Input() isRangeFinderView:boolean = false;
  @Input() isFocusedOnStudentResponse:boolean;
  @Input() isExpandView:boolean = false;
  @Input() scorerUid?: number;
  @Input() responseRawContainer:any; // only for isEditable
  @Input() multiScaleResponseBatchesMap: Map<number, ResponseBatch>;
  @Input() scoreProfileGroups?: {id: number, name: string}[];
  @Input() focusScoreProfileGroupIndex: number;
  @Input() isScoreProfileForceFocus: boolean;
  @Input() showExpandViewBtn:boolean = false;
  @Input() isNoInnerScroll:boolean = false;
  @Input() attemptId:number;
  @Input() itemScales:IItemScale[];
  @Input() scoringPolicies:IScoringPolicyRaw[];
  @Input() associatedResponses:{taqr_id:number, item_id: number}[];

  @Output() enableScoreSelection = new EventEmitter();
  @Output() responseStateInited = new EventEmitter();
  @Output() toggleFocusView = new EventEmitter();
  @Output() toggleExpandView = new EventEmitter();

  constructor(
    private auth:AuthService,
    public lang:LangService,
    public whiteLabel: WhitelabelService,
    private myScorerTasks: MyScorerTasksService,
    private zoom: ZoomService,
    private loginGuard: LoginGuardService,
    private routes: RoutesService,
  ) { }

  itemDisplays:{[itemId:number]: IQuestionConfig} = {};
  planningPages:any[] = [];
  responseStates:any[] = [];
  isPrintMode:boolean
  isForcedPrintMode:boolean
  isResetting:boolean;

  isScorerAnnotationAllowed: boolean = false;
  isShowOverlay: boolean = false;
  showDeactivatedMode: boolean = false;
  isAnnotationSet: boolean = false;
  isAnnotationSaving: boolean = false;
  toolToggles = {
    showFreehand: false,
    showLine: false,
    showEraser: false,
    showHighlight: false,
  }
  isNotepadEnabled: boolean = false;
  notepadText:string;
  notepadText__cache:string;
  drawingCtx: string;
  drawingCtx__cache: string;
  isZoomIn: boolean = false;
  highlighterColor: string;
  clearAllDrawings:boolean;
  saveAnnotationInterval;

  ngOnInit(): void {
    this.initZoom();
    if(this.scorerUid) {
      this.myScorerTasks.setUid(this.scorerUid);
    }
    this.myScorerTasks.setIsRangeFinderView(this.isRangeFinderView)
  }

  getCurrentStudentLabel() {
    const firstScale = Array.from(this.multiScaleResponseBatchesMap.values())[0]
    const label = firstScale.currentResponsBatch.studentLabelMap.get(this.responseId)
    
    return label ? label : 'N/A'
  }

  async ngOnChanges(changes:SimpleChanges){
    if (changes.windowItemId){
      // this.loadItemDisplay();    
    }
    if (changes.isEditable || changes.responseRawContainer){
      if (this.isEditable || this.responseRawContainer){
        const isResetRequired = !!this.responseStates;
        this.responseRawContainer.responseRaw = {};
        this.responseStates = this.responseRawContainer.responseRaw;
        if (isResetRequired){
          this.isResetting = true;
          setTimeout(() => {
            this.isResetting = false;
            this.responseStateInited.emit();
          }, 200)
        }
        else {
          this.responseStateInited.emit();
        }
      }
    }

    // Check if annotation is allowed in window - only once
    if (changes.responseId && changes.responseId.isFirstChange() && !this.isLeaderView){
      await this.checkIsScorerAnnotationAllowed();
    }
    // Save and clear annotations when navigating between questions
    if (changes.responseId && !changes.responseId.isFirstChange() && !this.isLeaderView && this.isScorerAnnotationAllowed) {
      await this.saveAnnotations(changes.responseId.previousValue)
      this.removeAnnotations();
    }
    // Load response content
    if (changes.responseId || changes.rawResponseId){
      await this.loadNewResponses();
    }
    // Load annotation and set to save it on interval
    if (changes.responseId && this.isScorerAnnotationAllowed) {
      await this.loadAnnotations(this.responseId);
      if (this.saveAnnotationInterval){
        clearInterval(this.saveAnnotationInterval)
      }
      this.saveAnnotationInterval = setInterval(() => {
        this.checkAnnotationSave();
      }, SAVE_ANNOTATION_INTERVAL_MS)
    }

  }

  @HostListener('window:beforeunload', ['$event'])
  async handleBeforeUnload(event: Event): Promise<void> {
    await this.checkAnnotationSave();
  }

  async ngOnDestroy(){
    await this.checkAnnotationSave();
  }

  fontSize = 1;
  minFontSize = 1;
  maxFontSize = 3;
  adjFontSize(incr:number){
    this.fontSize += incr/4;
    this.fontSize = Math.max(this.fontSize, this.minFontSize);
    this.fontSize = Math.min(this.fontSize, this.maxFontSize);
  }

  defaultZoomLevel = 0.75;
  minZoomLevel = 0.75;
  maxZoomLevel = 3.5;
  zoomIncrement = 0.25;
  isScanMode = false;  // determined dynamically
  
  initZoom(){
    this.zoom.update(this.defaultZoomLevel);
    this.zoom.updateScreenShrink(1);
  }

  zoomIn() {
    this.adjFontSize(1)
    if (this.getZoomLevel() + this.zoomIncrement <=  this.maxZoomLevel) {
      this.zoom.update(this.getZoomLevel() + this.zoomIncrement);
    }
  }
  zoomOut() {
    this.adjFontSize(-1)
    if (this.getZoomLevel() - this.zoomIncrement >=  this.minZoomLevel) {
      this.zoom.update(this.getZoomLevel() - this.zoomIncrement)
    }
  }

  isDeviceiPad(){
    return (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 0) || navigator.platform === 'iPad';
  }

  getZoomLevel() {
    return this.zoom.getZoom();
  }

  isRenderDblSpace(){
    return this.whiteLabel.getSiteFlag('IS_SCORE_RESP_DBL_SPACE')
  }

  isPlanningPagesRevealed:boolean = false
  togglePlanningPages(){
    this.isPlanningPagesRevealed = !this.isPlanningPagesRevealed
    this.triggerZoomRefresh()
  }

  async loadItemDisplay(responseId: number = 0, isSingleItem:boolean = false){
    this.itemDisplays = {};
    this.isPrintModeDetermined = false;
    if (this.windowItemId){
      const {mwiToItemId, itemDisplays, planningPages} = await this.auth.apiGet(
        '/public/scor-scor/batches/item-display', 
        this.windowItemId, 
        { query: { 
          responseId, 
          isSingleItem: isSingleItem ? 1 : 0
        }
      });
      if (mwiToItemId){
        const itemConfigDisplays:any = []
        for (let mapping of mwiToItemId){
          const itemDisplayRaw = itemDisplays[mapping.item_id]
          if (itemDisplayRaw.config) {
            const itemConfig = JSON.parse(itemDisplayRaw.config);
            const itemConfigDisplay = processItemConfigLangLink(itemConfig, itemDisplayRaw.lang);
            // set font scale override for scorer page only.
            itemConfigDisplay.content.forEach((c)=> {
              c.overrideFontScale = 1.2;
            })
            this.itemDisplays[mapping.item_id] = itemConfigDisplay;
            itemConfigDisplays.push(itemConfigDisplay)
          }
        }
        try {
          if (planningPages.data) {
            let itemIdNamePairs = [];
    
            // todo:REFACTOR it is not clear to me why this block of code is here
            this.myScorerTasks.getAssignedItems().forEach(item => {
              if (item.id === this.windowItemId){
                const items = this.myScorerTasks.getAssignedItemsByQuestionId().assignedItemsMap.get(item.item_id);
                for (let scale of items) {
                  itemIdNamePairs.push({item_id: scale.item_id, name: scale.name, item_slug: scale.item_slug})
                }
              }
            })
            itemIdNamePairs = itemIdNamePairs.filter((value, index, self) => index === self.findIndex((t) => (
              t.item_id === value.item_id && t.name === value.name
            )))
            // /todo:REFACTOR
    
            const planningPageData = JSON.parse(planningPages.data);
            for (let data of planningPageData) {
              const {config, response_raw} = data;
              const planningPage = {
                config: processItemConfigLangLink(config, this.lang.c()), 
                response_raw: response_raw, 
                item_id: -1 // todo:HARDCODED is this actually needed?
              };
              this.planningPages.push(planningPage);    
              // for (let itemIdNamePair of itemIdNamePairs) {
              //   for (let readSelection of config.readSelections) {
              //     if (readSelection == itemIdNamePair.item_slug) {
              //       const planningPage = {...processItemConfigLangLink(config, this.lang.c()), response_raw: response_raw, item_id: itemIdNamePair.item_id};
              //       this.planningPages.push(planningPage);    
              //     }
              //   }
              // }
            }
            this.triggerZoomRefresh()
          }
        }
        catch(e){
          console.warn('planning page error', e )
        }
        this.determinePrintMode(itemConfigDisplays);
        this.isPrintModeDetermined = true;
      }
    }
  }

  triggerZoomRefresh(){
    setTimeout(() => { this.zoom.triggerZoomRefresh(); }, 300)
  }

  isPrintModeDetermined:boolean = false;
  private determinePrintMode(itemDisplayList:IQuestionConfig[]){
    let isPrintMode = true; 
    for (let itemDisplay of itemDisplayList){
      let hasAtLeastOnePrintModeView = hasPrintBlocks(itemDisplay.content)
      if (!hasAtLeastOnePrintModeView){
        isPrintMode = false
      }
    }
    this.isPrintMode = isPrintMode;
  }

  toggleNonPrintView(){
    if (!this.isPrintMode){
      alert('The question is already displayed.');
      return;
    }
    this.isForcedPrintMode = ! this.isForcedPrintMode;
  }

  getValues(dataArr) {
    const values = [];
    for (let data of dataArr) {
      values.push(this.getValue(data));
    }
    return values;
  }

  getValue(data) {
    const values = [];
    for(let propName in data) {
      if(data.hasOwnProperty(propName)) {
        return data[propName];
      }
    }  
  }

  responseScanUrls:string[];
  responseScanUrl:string;
  isToggleWritten:boolean = false;
  prevReads: IPrevReads[]
  prevReadsAT: IPrevReads[]
  isShowingInvalidatedReads:boolean;
  isShowingInvalidatedReadsAT:boolean;
  
  scorerMeta:{swum_marker_number:string, swum_max_read_level:string, windowItemId:number};
  getScorerMeta():any{
    if (this.scorerMeta && this.scorerMeta.windowItemId == this.windowItemId){
      return this.scorerMeta
    }
    const itemScales = this.myScorerTasks.getAssignedItems()
    for (let itemScale of itemScales){
      if (+itemScale.id == +this.windowItemId){
        const {swum_marker_number, swum_max_read_level} = itemScale;
        this.scorerMeta = {
          swum_marker_number, 
          swum_max_read_level,
          windowItemId: this.windowItemId,
        }
        return this.scorerMeta
      }
    }
    return {}
  }

  getMyMarkerNumber(){
    const meta = this.getScorerMeta()
    return meta.swum_marker_number || 'N/A'
  }

  toggleStudentResponseFocus(){
    this.toggleFocusView.emit()
  }

  onToggleExpandView(){
    if (this.isFocusedOnStudentResponse){
      this.toggleStudentResponseFocus();
    }
    this.toggleExpandView.emit();
  }

  getItemDisplayForState(itemId:number){
    return this.itemDisplays[itemId];
  }
  
  // it is actually loading a set of response/scales for a single task 
  async loadNewResponses(){


    // For using this component in range finder - use basic way to load item display and response, without batches etc since it's not the scorer's view
    if (this.isRangeFinderView){
      this.loadSingleResponseSimple();
      return;
    }
    else {


      // todo: why can't this be at the top of the function
      this.planningPages = [];
      this.responseStates = [];
      this.responseScanUrls = [];
      this.isToggleWritten = false;

      if (this.isNoBatch){
        console.log([
          this.responseId,
          this.windowItemId,
          this.itemScales,
        ])
        const responseTaqrIdByGroupId:Map<number, number> = new Map()
        this.associatedResponses.forEach(r => {
          responseTaqrIdByGroupId.set(+r.item_id, r.taqr_id)
        })
        const targetScales:{item_id: number,caption:string, group_to_mwi_id:number, taqr_id:number}[] = [];
        for (let itemScale of this.itemScales){
          if (itemScale.group_to_mwi_id == this.windowItemId){
            if (itemScale.sync_batches_to_wmi_id == itemScale.mwi_id){
              const {item_id, caption, group_to_mwi_id} = itemScale;
              const taqr_id = responseTaqrIdByGroupId.get(+item_id);
              if (taqr_id){
                targetScales.push({
                  item_id,
                  caption,
                  group_to_mwi_id,
                  taqr_id,
                })
              }
            }
          }
        }
        let itemLoadedRef = new Map();
        for (let scale of targetScales){
          if (!itemLoadedRef.has(scale.group_to_mwi_id)){
            itemLoadedRef.set(scale.group_to_mwi_id, true);
            this.loadItemDisplay();
          }
          await this.loadByRawResponseId(scale.taqr_id, scale.item_id, scale.caption)
        }
      }
      else {
        // For Scorers and Scorings View
        const multiScaleResponseBatches = Array.from(this.multiScaleResponseBatchesMap.values());
        this.loadPreviousReads(multiScaleResponseBatches);
        const uniqueScaledResponses2D = this.determineScaledResponses2D(multiScaleResponseBatches)
        for (let uniqueScaledResponses of uniqueScaledResponses2D) {
          const syncScaledResponse = uniqueScaledResponses[0]
          if (this.getValues(uniqueScaledResponses).includes(this.responseId || this.rawResponseId)) {
            this.loadItemDisplay(this.getValue(uniqueScaledResponses[0]));
            for (let i = 0; i<uniqueScaledResponses.length; i++) {
              const uniqueScaledResponse = uniqueScaledResponses[i];
              const mcbr_id = this.getValue(uniqueScaledResponse);
              const item_id = Number(Object.keys(uniqueScaledResponse)[0]);
              const name = this.scoreProfileGroups[i].name
              if (this.responseId){
                await this.loadByScoringResponseId(item_id, name, mcbr_id)
              }
              if (this.rawResponseId){
                await this.loadByRawResponseId(this.rawResponseId, item_id, name)
              }
            }
          }
        }
      }
    }

    if (this.responseStates.length !== 0) {
      this.enableScoreSelection.emit();
    }

    this.determinePaperLayout();

  }

  determineScaledResponses2D(multiScaleResponseBatches: ResponseBatch[]){
    const uniqueScaledResponses2D = [];
    const syncBatch = multiScaleResponseBatches[0];
    const numResponseScaleBatches = syncBatch.currentResponsBatch.responses.length; // these are not responses, these are scales
    for (let i = 0; i < numResponseScaleBatches; i++) {
      const uniqueItemResponseMap: Map<number, number> = new Map();
      for (const [window_item_id, responseBatch] of this.multiScaleResponseBatchesMap){
        if (!uniqueItemResponseMap.get(responseBatch.currentResponsBatch.item_id)) {
          uniqueItemResponseMap.set(responseBatch.currentResponsBatch.item_id, responseBatch.currentResponsBatch.responses[i])
        }
      }
      const uniqueItemResponseArray = [];
      uniqueItemResponseMap.forEach((responseId, item_id) => {uniqueItemResponseArray.push({[item_id]:responseId})})
      uniqueScaledResponses2D.push(uniqueItemResponseArray);
    }
    return uniqueScaledResponses2D
  }

  loadPreviousReads(multiScaleResponseBatches: ResponseBatch[]){
    this.prevReads = [];
    this.prevReadsAT = [];
    const currentIndexOfResponse = multiScaleResponseBatches[0].currentResponsBatch.responses.indexOf(multiScaleResponseBatches[0].myPresence.responseId);
    try {
      let prevReadsLength = 0;
      let prevReadsATLength = 0;
      this.prevReads = [];
      this.prevReadsAT = [];
      multiScaleResponseBatches.map((responseBatch)=>{
        const prevReads = responseBatch.prevReads[responseBatch.currentResponsBatch.responses[currentIndexOfResponse]]
        if(prevReads && prevReads.length > prevReadsLength ){
          this.prevReads = prevReads;
          prevReadsLength = prevReads.length;
        }
        const prevReadsAT = responseBatch.prevReads_at[responseBatch.currentResponsBatch.responses[currentIndexOfResponse]]
        if(prevReadsAT && prevReadsAT.length > prevReadsATLength  ){
          this.prevReadsAT = prevReadsAT;
          prevReadsATLength = prevReadsAT.length;
        }
      });
    }
    catch (e){
      console.warn('failed to pull prev reads')
    }
  }

  async loadByScoringResponseId(item_id:number, name:string, mcbr_id:number){
    const query = {is_leader_view: this.isLeaderView ? 1 : 0}
    const {response_raw, test_attempt_id, is_paper_response, scan_url, student_accommodation_cache,is_accomm_displayed} = await this.auth.apiGet(this.routes.SCOR_SCOR_MARK, mcbr_id, {query}).catch(e => {
      if (e.message !== 'NO_CLAIMED_BATCHES_AVAIL'){ this.loginGuard.quickPopup(e.message) }
    })
    const newState: any = {
      test_attempt_id, 
      is_paper_response, 
      item_id, 
      response: JSON.parse(response_raw),
      student_accommodation: JSON.parse(student_accommodation_cache),
      is_accomm_displayed: is_accomm_displayed,
      display_accommodation: false,
      responseScanUrl: scan_url
    };
    if(this.scoreProfileGroups) {
      newState.name = name;
    }
    this.responseStates.push(newState);
    this.responseScanUrls.push(scan_url);
  }
  async loadByRawResponseId(taqr_id:number, item_id:number, name:string){
    const query = {is_leader_view: this.isLeaderView ? 1 : 0}
    const {response_raw, is_paper_response, test_attempt_id, scan_url} = await this.auth.apiGet('/public/scor-lead/validity/responses', taqr_id, {query})
    const newState: any = {
      test_attempt_id, 
      is_paper_response,
      item_id, 
      response: JSON.parse(response_raw),
      responseScanUrl: scan_url
    }
    if(this.scoreProfileGroups) {
      newState.name = name;
    }
    this.responseStates.push(newState);
    this.responseScanUrls.push(scan_url);
  }

  /** Load the item display and response of a single question at a time (use in range finder view) */
  async loadSingleResponseSimple(){
    this.loadItemDisplay(this.rawResponseId, true);
    this.responseStates = [];
    this.responseScanUrls = [];
    this.isToggleWritten = false;
    const query = {is_leader_view: this.isLeaderView ? 1 : 0}
    const {response_raw, test_attempt_id, is_paper_response, test_question_id, scan_url} = await this.auth.apiGet('/public/scor-lead/validity/responses', this.rawResponseId, {query})
    const newState: any = {
      test_attempt_id, 
      is_paper_response,
      item_id: test_question_id, 
      response: JSON.parse(response_raw),
      responseScanUrl: scan_url
    }
    this.responseStates.push(newState);
    this.responseScanUrls.push(scan_url);

    this.determinePaperLayout();

  }

  determinePaperLayout(){
    let isAnyPaper = false;
    for (let record of this.responseStates){
      if (record.is_paper_response){
        isAnyPaper = true;
        break;
      }
    }
    this.isScanMode = isAnyPaper;
  }

  isViewQuestionsHidden() {
    return this.whiteLabel.getSiteFlag('IS_MRKG_VIEW_QUESTIONS_HIDDEN')
  }

  getCurrentAccommodationList(student_accommodation: IstudentAccommodation[]){
    
    // Find unique accmmodation from the list
    let uniqueAccommodationList = [];
    if(student_accommodation && student_accommodation.length > 0){
      for (const accommodation of student_accommodation){
        let alreadyExist = false;
        for(const uniqueAccommodation of uniqueAccommodationList){
          if(uniqueAccommodation.name == accommodation.name){
            alreadyExist = true;
          }
        }
        if(!alreadyExist){
          uniqueAccommodationList.push(accommodation)
        }
      }
      return uniqueAccommodationList;
    }
    else{
      return [];
    }
  }

  toggleDisplayAccommodation(responseState: any){
    if(responseState){
      responseState.display_accommodation = !responseState.display_accommodation;
    }
  }

  /** Clear annotations (to wait for response to load first) */
  removeAnnotations(){
    this.isAnnotationSet = false;
    this.drawingCtx = undefined;
    this.notepadText = undefined;
  }

  /** Get any existing annotations for response */
  async loadAnnotations(responseId: number){
    const {drawingCtx, notepadText} = await this.auth.apiGet(this.routes.SCOR_SCOR_ANNOTATIONS, responseId)
    this.notepadText = notepadText;
    this.drawingCtx = drawingCtx;
    this.isAnnotationSet = true;
  }

  /** Check if annotation has changed and should be re-saved */
  async checkAnnotationSave(){
    if (!this.isScorerAnnotationAllowed || !this.isAnnotationSet) return;
    if (this.drawingCtx == this.drawingCtx__cache && this.notepadText == this.notepadText__cache){
      return;
    }
    this.drawingCtx__cache = this.drawingCtx;
    this.notepadText__cache = this.notepadText;
    if (!this.isAnnotationSaving){
      await this.saveAnnotations(this.responseId);
    }
  }

  /** Save annotation to db */
  async saveAnnotations(responseId: number){
    this.isAnnotationSaving = true;
    const data = {
      notepadText: this.notepadText,
      drawingCtx: this.drawingCtx,
    }
    await this.auth.apiPatch(this.routes.SCOR_SCOR_ANNOTATIONS, responseId, data)
    this.isAnnotationSaving = false;
  }


  setEraser(){
    this.toggleZwibbler();
    this.toggleTool('showEraser', 'SHOW_ERASER');
  }
  setHighlighter(){
    this.toggleZwibbler();
    this.toggleTool('showHighlight', 'SHOW_HIGHLIGHT');
  }
  setLine(){
    this.toggleZwibbler();
    this.toggleTool('showLine', 'SHOW_LINE');
  }
  setFreehand(){
    this.toggleZwibbler();
    this.toggleTool('showFreehand', 'SHOW_FREEHAND');
  }

  toggleNotepad(){
    this.isNotepadEnabled = !this.isNotepadEnabled;
  }

  toggleTool(variableName: string, toolSlug: string) {
    if(this.toolToggles[variableName] == undefined) {
      return;
    }
    if(!this.showDeactivatedMode && this.toolToggles[variableName] == true) {
      this.disableOverlay();
    }
    for (const key in this.toolToggles) {
      if (this.toolToggles.hasOwnProperty(key)) {
        this.toolToggles[key] = (key === variableName ? !this.toolToggles[key] : false);
      }
    }
  }
  
  disableOverlay(){
    this.showDeactivatedMode = true;
  }

  toggleZwibbler(){
    if (this.showDeactivatedMode){
      this.showDeactivatedMode = false;
    }
   }

  removeAllDrawings(){
    this.clearAllDrawings = !this.clearAllDrawings
  }

  getDrawingDisplayMode(){
    return DrawDisplayMode.PANEL_SCORE_CONTENT;
  }

  getScreenShrink() {
    return this.zoom.getScreenShrink();
  }

  getScreenShrinkZoom() {
    return this.zoom.getScreenShrinkZoom();
  }
  getZoomValue(){
    const ua = navigator.userAgent;
    if (ua.indexOf("iPad")!=-1 || ua.indexOf("iPhone")!=-1) {
      if (this.getZoomLevel() > this.defaultZoomLevel) {
        this.zoom.update(this.defaultZoomLevel);
      }
    }
    return this.getScreenShrinkZoom();
  }

  getDrawingToolsSlug() {
    return 'btn_toggle_editor';
  }

  getFreehandSlug() {
    if(this.whiteLabel.isABED()){
      return 'abed_btn_freehand'
    }
    return 'btn_freehand';
  }

  getHighlighterSlug() {
    return 'el_draw_highlighter';
  }

  getEraserSlug(){
    return 'btn_eraser';
  }

  getNotepadSlug(){
    if (!this.isFlushNavigation()){
      return 'lbl_notepad'
    }
    else{
      return 'lbl_notepad_bc'
    }
  }

  getRemoveDrawingSlug() {
    if(this.whiteLabel.isABED()){
      return 'btn_remove_drawings_abed'
    }
    return 'btn_remove_drawings';
  }

  getCloseDrawingSlug(){
    return 'draw_tool_exit';
  }

  isFlushNavigation(){
    return this.whiteLabel.getSiteFlag('IS_BCED') || this.whiteLabel.getSiteFlag('IS_VEA')
  }

  saveDrawingLocal(drawingContext:SectionDrawingCtx) {
    const drawing = drawingContext.ctx;
    this.drawingCtx = drawing;
  }

  setHighlightColor(color: string) {
    this.highlighterColor = color;
  }

  getColors(): string[] {
    return MIN_HILIGHT_COLORS;
  }

  @ViewChild('questionContent', { static: false }) questionContentRef: ElementRef;
  getQuestionContentHeight(){
    if (!this.questionContentRef) return;
    const questionContent: HTMLTableElement = this.questionContentRef.nativeElement;
    return questionContent.scrollHeight;
  }

  /** Check whether a scorer can annotate */
  async checkIsScorerAnnotationAllowed(){
    const query = {window_item_id: this.windowItemId}
    const {is_scorer_annotation_allowed} = await this.auth.apiFind(this.routes.SCOR_SCOR_ANNOTATIONS, {query})
    if (is_scorer_annotation_allowed){
      this.isScorerAnnotationAllowed = true;
      this.isShowOverlay = true;
      this.showDeactivatedMode = true;
    }
  }
}

