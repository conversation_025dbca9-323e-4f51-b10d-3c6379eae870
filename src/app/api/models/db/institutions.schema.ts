import * as DBT from "../db-types";

export interface IInstitution {
    id?              : DBT.ID,
    group_id?        : DBT.ID,
    name?            : DBT.VARCHAR,
    address?         : DBT.VARCHAR,
    city?            : DBT.VARCHAR,
    province?        : DBT.VARCHAR,
    postal_code?     : DBT.VARCHAR,
    phone_number?    : DBT.VARCHAR,
    fax_number?      : DBT.VARCHAR,
    email?           : DBT.VARCHAR,
    notes?           : DBT.VARCHAR,
    is_active?       : DBT.BOOL_INT,
    is_shown?        : DBT.BOOL_INT,
    created_on       : DBT.DATETIME,
    created_by_uid   : DBT.UID,
    role_types?      : string[],
    numActiveSessions?  : number,
    has_seb?         : DBT.BOOL_INT,    
    applicant_rt_policy? : string,
    resp_time_policy?  : string,
    reg_buffer_policy?: string,    
    accomm_responses?: {
        total:number,
        max:number,
        min:number,
        average:number
    },
    applicant_rt_policy_unit: DBT.VARCHAR,
    resp_time_policy_unit: DBT.VARCHAR,
    reg_buffer_policy_unit: DBT.VARCHAR,
    is_accomm_coord_assigned?: boolean,
    is_invigilator_assigned?: boolean,
    is_report_shown?: boolean
}